//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetInfographicChatResponse {
  /// Returns a new [DtoGetInfographicChatResponse] instance.
  DtoGetInfographicChatResponse({
    this.count,
    this.items = const [],
  });

  /// only use when query_type is COUNT
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? count;

  List<DtoMessage> items;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetInfographicChatResponse &&
     other.count == count &&
     other.items == items;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (count == null ? 0 : count!.hashCode) +
    (items.hashCode);

  @override
  String toString() => 'DtoGetInfographicChatResponse[count=$count, items=$items]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.count != null) {
      json[r'count'] = this.count;
    } else {
      json[r'count'] = null;
    }
      json[r'items'] = this.items;
    return json;
  }

  /// Returns a new [DtoGetInfographicChatResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetInfographicChatResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetInfographicChatResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetInfographicChatResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetInfographicChatResponse(
        count: mapValueOfType<int>(json, r'count'),
        items: DtoMessage.listFromJson(json[r'items']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoGetInfographicChatResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetInfographicChatResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetInfographicChatResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetInfographicChatResponse> mapFromJson(dynamic json) {
    final map = <String, DtoGetInfographicChatResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetInfographicChatResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetInfographicChatResponse-objects as value to a dart map
  static Map<String, List<DtoGetInfographicChatResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetInfographicChatResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetInfographicChatResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

