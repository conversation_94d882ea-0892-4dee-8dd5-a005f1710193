//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoFindPageAndMessagesResponse {
  /// Returns a new [DtoFindPageAndMessagesResponse] instance.
  DtoFindPageAndMessagesResponse({
    this.databaseId,
    this.items = const [],
    this.originId,
    this.originType,
    this.page,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? databaseId;

  List<DtoMessage> items;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? originId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? originType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? page;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoFindPageAndMessagesResponse &&
     other.databaseId == databaseId &&
     other.items == items &&
     other.originId == originId &&
     other.originType == originType &&
     other.page == page;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (databaseId == null ? 0 : databaseId!.hashCode) +
    (items.hashCode) +
    (originId == null ? 0 : originId!.hashCode) +
    (originType == null ? 0 : originType!.hashCode) +
    (page == null ? 0 : page!.hashCode);

  @override
  String toString() => 'DtoFindPageAndMessagesResponse[databaseId=$databaseId, items=$items, originId=$originId, originType=$originType, page=$page]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.databaseId != null) {
      json[r'database_id'] = this.databaseId;
    } else {
      json[r'database_id'] = null;
    }
      json[r'items'] = this.items;
    if (this.originId != null) {
      json[r'origin_id'] = this.originId;
    } else {
      json[r'origin_id'] = null;
    }
    if (this.originType != null) {
      json[r'origin_type'] = this.originType;
    } else {
      json[r'origin_type'] = null;
    }
    if (this.page != null) {
      json[r'page'] = this.page;
    } else {
      json[r'page'] = null;
    }
    return json;
  }

  /// Returns a new [DtoFindPageAndMessagesResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoFindPageAndMessagesResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoFindPageAndMessagesResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoFindPageAndMessagesResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoFindPageAndMessagesResponse(
        databaseId: mapValueOfType<String>(json, r'database_id'),
        items: DtoMessage.listFromJson(json[r'items'])!,
        originId: mapValueOfType<String>(json, r'origin_id'),
        originType: mapValueOfType<String>(json, r'origin_type'),
        page: json[r'page'] == null
            ? null
            : num.parse(json[r'page'].toString()),
      );
    }
    return null;
  }

  static List<DtoFindPageAndMessagesResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoFindPageAndMessagesResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoFindPageAndMessagesResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoFindPageAndMessagesResponse> mapFromJson(dynamic json) {
    final map = <String, DtoFindPageAndMessagesResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFindPageAndMessagesResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoFindPageAndMessagesResponse-objects as value to a dart map
  static Map<String, List<DtoFindPageAndMessagesResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoFindPageAndMessagesResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFindPageAndMessagesResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'items',
  };
}

