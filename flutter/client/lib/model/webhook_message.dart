//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class WebhookMessage {
  /// Returns a new [WebhookMessage] instance.
  WebhookMessage({
    required this.actor,
    required this.createdAt,
    required this.id,
    this.metadata,
    this.reactions = const [],
    this.serialNo,
    required this.text,
  });

  WebhookCard actor;

  String createdAt;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? metadata;

  List<WebhookReactionDetail> reactions;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? serialNo;

  String text;

  @override
  bool operator ==(Object other) => identical(this, other) || other is WebhookMessage &&
     other.actor == actor &&
     other.createdAt == createdAt &&
     other.id == id &&
     other.metadata == metadata &&
     other.reactions == reactions &&
     other.serialNo == serialNo &&
     other.text == text;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (actor.hashCode) +
    (createdAt.hashCode) +
    (id.hashCode) +
    (metadata == null ? 0 : metadata!.hashCode) +
    (reactions.hashCode) +
    (serialNo == null ? 0 : serialNo!.hashCode) +
    (text.hashCode);

  @override
  String toString() => 'WebhookMessage[actor=$actor, createdAt=$createdAt, id=$id, metadata=$metadata, reactions=$reactions, serialNo=$serialNo, text=$text]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'actor'] = this.actor;
      json[r'created_at'] = this.createdAt;
      json[r'id'] = this.id;
    if (this.metadata != null) {
      json[r'metadata'] = this.metadata;
    } else {
      json[r'metadata'] = null;
    }
      json[r'reactions'] = this.reactions;
    if (this.serialNo != null) {
      json[r'serial_no'] = this.serialNo;
    } else {
      json[r'serial_no'] = null;
    }
      json[r'text'] = this.text;
    return json;
  }

  /// Returns a new [WebhookMessage] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static WebhookMessage? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "WebhookMessage[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "WebhookMessage[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return WebhookMessage(
        actor: WebhookCard.fromJson(json[r'actor'])!,
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        id: mapValueOfType<String>(json, r'id')!,
        metadata: mapValueOfType<Object>(json, r'metadata'),
        reactions: WebhookReactionDetail.listFromJson(json[r'reactions']) ?? const [],
        serialNo: mapValueOfType<int>(json, r'serial_no'),
        text: mapValueOfType<String>(json, r'text')!,
      );
    }
    return null;
  }

  static List<WebhookMessage>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <WebhookMessage>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = WebhookMessage.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, WebhookMessage> mapFromJson(dynamic json) {
    final map = <String, WebhookMessage>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = WebhookMessage.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of WebhookMessage-objects as value to a dart map
  static Map<String, List<WebhookMessage>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<WebhookMessage>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = WebhookMessage.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'actor',
    'created_at',
    'id',
    'text',
  };
}

