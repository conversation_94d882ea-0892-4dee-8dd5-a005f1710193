package service

import (
	"context"

	"github.com/google/uuid"

	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
)

type DatabaseService interface {
	Create(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, databaseReq *dto.PostDatabaseRequest) (*model.Chat, dto.DatabaseResponse, error)
	FindByWorkspaceID(ctx context.Context, requesterID, workspaceID uuid.UUID, q dto.GetDatabasesQueryParams) (dto.DatabasesResponse, error)
	FindByWorkspaceIDAndID(ctx context.Context, requesterID, workspaceID, databaseID uuid.UUID) (dto.DatabaseResponse, error)
	FlexibleUpdate(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, databaseID uuid.UUID, databaseReq *dto.PatchDatabaseRequest) (*model.Chat, dto.DatabaseResponse, *dto.NotificationData, error)
	GetDatabasesResponse(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, databaseIDs []uuid.UUID) ([]dto.DatabaseResponse, error)
	UpdatePosition(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, databaseReq *dto.PutDatabasePositionRequest) (*model.Chat, *dto.NotificationData, error)
	PatchMultiple(ctx context.Context, workspaceID, requesterID uuid.UUID, databasesRequest *dto.PatchDatabasesRequest) (dto.DatabasesResponse, error)
	Delete(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, id uuid.UUID) (*model.Chat, error)
	GetGuestDatabaseService(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID) (dto.DatabaseResponse, error)
	GetDatabaseResponse(ctx context.Context, requesterID, databaseID uuid.UUID) (dto.DatabaseResponse, error)
}
