//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoSeatsRemaining {
  /// Returns a new [DtoSeatsRemaining] instance.
  DtoSeatsRemaining({
    this.numberSeatRemaining,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? numberSeatRemaining;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoSeatsRemaining &&
     other.numberSeatRemaining == numberSeatRemaining;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (numberSeatRemaining == null ? 0 : numberSeatRemaining!.hashCode);

  @override
  String toString() => 'DtoSeatsRemaining[numberSeatRemaining=$numberSeatRemaining]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.numberSeatRemaining != null) {
      json[r'number_seat_remaining'] = this.numberSeatRemaining;
    } else {
      json[r'number_seat_remaining'] = null;
    }
    return json;
  }

  /// Returns a new [DtoSeatsRemaining] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoSeatsRemaining? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoSeatsRemaining[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoSeatsRemaining[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoSeatsRemaining(
        numberSeatRemaining: json[r'number_seat_remaining'] == null
            ? null
            : num.parse(json[r'number_seat_remaining'].toString()),
      );
    }
    return null;
  }

  static List<DtoSeatsRemaining>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoSeatsRemaining>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoSeatsRemaining.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoSeatsRemaining> mapFromJson(dynamic json) {
    final map = <String, DtoSeatsRemaining>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSeatsRemaining.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoSeatsRemaining-objects as value to a dart map
  static Map<String, List<DtoSeatsRemaining>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoSeatsRemaining>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSeatsRemaining.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

