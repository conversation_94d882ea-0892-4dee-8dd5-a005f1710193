//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagGroupsColorResponse {
  /// Returns a new [DtoTagGroupsColorResponse] instance.
  DtoTagGroupsColorResponse({
    this.items = const [],
  });

  List<DtoTagGroupColor> items;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagGroupsColorResponse &&
     other.items == items;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (items.hashCode);

  @override
  String toString() => 'DtoTagGroupsColorResponse[items=$items]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'items'] = this.items;
    return json;
  }

  /// Returns a new [DtoTagGroupsColorResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagGroupsColorResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagGroupsColorResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagGroupsColorResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagGroupsColorResponse(
        items: DtoTagGroupColor.listFromJson(json[r'items']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoTagGroupsColorResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagGroupsColorResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagGroupsColorResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagGroupsColorResponse> mapFromJson(dynamic json) {
    final map = <String, DtoTagGroupsColorResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroupsColorResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagGroupsColorResponse-objects as value to a dart map
  static Map<String, List<DtoTagGroupsColorResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagGroupsColorResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroupsColorResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

