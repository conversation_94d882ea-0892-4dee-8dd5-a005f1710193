//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class StripeApi {
  StripeApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Stripe
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoAddSeatsRequestParams] body (required):
  ///   AddSeatsRequestParams Request
  Future<Response> workspacesWorkspaceIdStripeAddSeatsPostWithHttpInfo(String workspaceId, DtoAddSeatsRequestParams body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/add-seats'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Stripe
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoAddSeatsRequestParams] body (required):
  ///   AddSeatsRequestParams Request
  Future<String?> workspacesWorkspaceIdStripeAddSeatsPost(String workspaceId, DtoAddSeatsRequestParams body,) async {
    final response = await workspacesWorkspaceIdStripeAddSeatsPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Create Billing Portal Session
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdStripeBillingPortalSessionsPostWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/billing_portal/sessions'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Billing Portal Session
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoCreateBillingPortalSessionResponse?> workspacesWorkspaceIdStripeBillingPortalSessionsPost(String workspaceId,) async {
    final response = await workspacesWorkspaceIdStripeBillingPortalSessionsPostWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoCreateBillingPortalSessionResponse',) as DtoCreateBillingPortalSessionResponse;
    
    }
    return null;
  }

  /// Stripe
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdStripeCancelDeleteSeatsPutWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/cancel-delete-seats'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Stripe
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<String?> workspacesWorkspaceIdStripeCancelDeleteSeatsPut(String workspaceId,) async {
    final response = await workspacesWorkspaceIdStripeCancelDeleteSeatsPutWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Stripe
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoCheckoutSessionRequestParams] body (required):
  ///   CheckoutSessionRequestParams Request
  Future<Response> workspacesWorkspaceIdStripeCreateCheckoutSessionPostWithHttpInfo(String workspaceId, DtoCheckoutSessionRequestParams body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/create-checkout-session'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Stripe
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoCheckoutSessionRequestParams] body (required):
  ///   CheckoutSessionRequestParams Request
  Future<DtoCheckoutSessionResponseParams?> workspacesWorkspaceIdStripeCreateCheckoutSessionPost(String workspaceId, DtoCheckoutSessionRequestParams body,) async {
    final response = await workspacesWorkspaceIdStripeCreateCheckoutSessionPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoCheckoutSessionResponseParams',) as DtoCheckoutSessionResponseParams;
    
    }
    return null;
  }

  /// Stripe
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoDeleteSeatsRequestParams] body (required):
  ///   DeleteSeatsRequestParams Request
  Future<Response> workspacesWorkspaceIdStripeDeleteSeatsPutWithHttpInfo(String workspaceId, DtoDeleteSeatsRequestParams body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/delete-seats'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Stripe
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoDeleteSeatsRequestParams] body (required):
  ///   DeleteSeatsRequestParams Request
  Future<String?> workspacesWorkspaceIdStripeDeleteSeatsPut(String workspaceId, DtoDeleteSeatsRequestParams body,) async {
    final response = await workspacesWorkspaceIdStripeDeleteSeatsPutWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Get difference amount for adding seats
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoAddSeatsRequestParams] body (required):
  ///   AddSeatsRequestParams Request
  Future<Response> workspacesWorkspaceIdStripeDifferenceAmountPostWithHttpInfo(String workspaceId, DtoAddSeatsRequestParams body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/difference-amount'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get difference amount for adding seats
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoAddSeatsRequestParams] body (required):
  ///   AddSeatsRequestParams Request
  Future<DtoDifferenceAmountResponse?> workspacesWorkspaceIdStripeDifferenceAmountPost(String workspaceId, DtoAddSeatsRequestParams body,) async {
    final response = await workspacesWorkspaceIdStripeDifferenceAmountPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoDifferenceAmountResponse',) as DtoDifferenceAmountResponse;
    
    }
    return null;
  }

  /// Get Invoice by Invoice ID
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] invoiceId (required):
  ///   Invoice ID
  Future<Response> workspacesWorkspaceIdStripeInvoicesInvoiceIdGetWithHttpInfo(String workspaceId, String invoiceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/invoices/{invoice_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{invoice_id}', invoiceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Invoice by Invoice ID
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] invoiceId (required):
  ///   Invoice ID
  Future<String?> workspacesWorkspaceIdStripeInvoicesInvoiceIdGet(String workspaceId, String invoiceId,) async {
    final response = await workspacesWorkspaceIdStripeInvoicesInvoiceIdGetWithHttpInfo(workspaceId, invoiceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Get Upcoming Invoice
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdStripeInvoicesUpcomingGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/invoices/upcoming'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Upcoming Invoice
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Object?> workspacesWorkspaceIdStripeInvoicesUpcomingGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdStripeInvoicesUpcomingGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'Object',) as Object;
    
    }
    return null;
  }

  /// Get Subscription
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdStripeSubscriptionGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/stripe/subscription'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Subscription
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Object?> workspacesWorkspaceIdStripeSubscriptionGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdStripeSubscriptionGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'Object',) as Object;
    
    }
    return null;
  }
}
