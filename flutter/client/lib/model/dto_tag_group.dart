//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagGroup {
  /// Returns a new [DtoTagGroup] instance.
  DtoTagGroup({
    this.background,
    this.backgroundKey,
    this.chatInfo,
    this.color,
    this.description,
    this.displayOnCard,
    this.facilitator,
    this.hasNewMessage,
    this.id,
    this.illustration,
    this.informationFormItems = const [],
    this.isDefault,
    this.memberBookmarkedItemId,
    this.name,
    this.onlySingleValueIsSelected,
    this.opacity,
    this.primaryColor,
    this.reverseBackground,
    this.secondaryColor,
    this.status,
    this.tagNumber,
    this.tags = const [],
    this.textAlignment,
    this.textColor,
    this.workspaceId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoChatInfo? chatInfo;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? color;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? displayOnCard;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoFacilitatorResponse? facilitator;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasNewMessage;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  List<ModelFormItem> informationFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isDefault;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? memberBookmarkedItemId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? onlySingleValueIsSelected;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? status;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  List<DtoTag> tags;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  DtoTagGroupTextColorEnum? textColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagGroup &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.chatInfo == chatInfo &&
     other.color == color &&
     other.description == description &&
     other.displayOnCard == displayOnCard &&
     other.facilitator == facilitator &&
     other.hasNewMessage == hasNewMessage &&
     other.id == id &&
     other.illustration == illustration &&
     other.informationFormItems == informationFormItems &&
     other.isDefault == isDefault &&
     other.memberBookmarkedItemId == memberBookmarkedItemId &&
     other.name == name &&
     other.onlySingleValueIsSelected == onlySingleValueIsSelected &&
     other.opacity == opacity &&
     other.primaryColor == primaryColor &&
     other.reverseBackground == reverseBackground &&
     other.secondaryColor == secondaryColor &&
     other.status == status &&
     other.tagNumber == tagNumber &&
     other.tags == tags &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (chatInfo == null ? 0 : chatInfo!.hashCode) +
    (color == null ? 0 : color!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (displayOnCard == null ? 0 : displayOnCard!.hashCode) +
    (facilitator == null ? 0 : facilitator!.hashCode) +
    (hasNewMessage == null ? 0 : hasNewMessage!.hashCode) +
    (id == null ? 0 : id!.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (informationFormItems.hashCode) +
    (isDefault == null ? 0 : isDefault!.hashCode) +
    (memberBookmarkedItemId == null ? 0 : memberBookmarkedItemId!.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (onlySingleValueIsSelected == null ? 0 : onlySingleValueIsSelected!.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (tags.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (workspaceId == null ? 0 : workspaceId!.hashCode);

  @override
  String toString() => 'DtoTagGroup[background=$background, backgroundKey=$backgroundKey, chatInfo=$chatInfo, color=$color, description=$description, displayOnCard=$displayOnCard, facilitator=$facilitator, hasNewMessage=$hasNewMessage, id=$id, illustration=$illustration, informationFormItems=$informationFormItems, isDefault=$isDefault, memberBookmarkedItemId=$memberBookmarkedItemId, name=$name, onlySingleValueIsSelected=$onlySingleValueIsSelected, opacity=$opacity, primaryColor=$primaryColor, reverseBackground=$reverseBackground, secondaryColor=$secondaryColor, status=$status, tagNumber=$tagNumber, tags=$tags, textAlignment=$textAlignment, textColor=$textColor, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
    if (this.chatInfo != null) {
      json[r'chat_info'] = this.chatInfo;
    } else {
      json[r'chat_info'] = null;
    }
    if (this.color != null) {
      json[r'color'] = this.color;
    } else {
      json[r'color'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
    if (this.displayOnCard != null) {
      json[r'display_on_card'] = this.displayOnCard;
    } else {
      json[r'display_on_card'] = null;
    }
    if (this.facilitator != null) {
      json[r'facilitator'] = this.facilitator;
    } else {
      json[r'facilitator'] = null;
    }
    if (this.hasNewMessage != null) {
      json[r'has_new_message'] = this.hasNewMessage;
    } else {
      json[r'has_new_message'] = null;
    }
    if (this.id != null) {
      json[r'id'] = this.id;
    } else {
      json[r'id'] = null;
    }
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
      json[r'information_form_items'] = this.informationFormItems;
    if (this.isDefault != null) {
      json[r'is_default'] = this.isDefault;
    } else {
      json[r'is_default'] = null;
    }
    if (this.memberBookmarkedItemId != null) {
      json[r'member_bookmarked_item_id'] = this.memberBookmarkedItemId;
    } else {
      json[r'member_bookmarked_item_id'] = null;
    }
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    if (this.onlySingleValueIsSelected != null) {
      json[r'only_single_value_is_selected'] = this.onlySingleValueIsSelected;
    } else {
      json[r'only_single_value_is_selected'] = null;
    }
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
      json[r'tags'] = this.tags;
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
    if (this.workspaceId != null) {
      json[r'workspace_id'] = this.workspaceId;
    } else {
      json[r'workspace_id'] = null;
    }
    return json;
  }

  /// Returns a new [DtoTagGroup] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagGroup? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagGroup[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagGroup[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagGroup(
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        chatInfo: DtoChatInfo.fromJson(json[r'chat_info']),
        color: mapValueOfType<String>(json, r'color'),
        description: mapValueOfType<String>(json, r'description'),
        displayOnCard: mapValueOfType<bool>(json, r'display_on_card'),
        facilitator: DtoFacilitatorResponse.fromJson(json[r'facilitator']),
        hasNewMessage: mapValueOfType<bool>(json, r'has_new_message'),
        id: mapValueOfType<String>(json, r'id'),
        illustration: mapValueOfType<String>(json, r'illustration'),
        informationFormItems: ModelFormItem.listFromJson(json[r'information_form_items']) ?? const [],
        isDefault: mapValueOfType<bool>(json, r'is_default'),
        memberBookmarkedItemId: mapValueOfType<String>(json, r'member_bookmarked_item_id'),
        name: mapValueOfType<String>(json, r'name'),
        onlySingleValueIsSelected: mapValueOfType<bool>(json, r'only_single_value_is_selected'),
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        status: mapValueOfType<String>(json, r'status'),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        tags: DtoTag.listFromJson(json[r'tags']) ?? const [],
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: DtoTagGroupTextColorEnum.fromJson(json[r'text_color']),
        workspaceId: mapValueOfType<String>(json, r'workspace_id'),
      );
    }
    return null;
  }

  static List<DtoTagGroup>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagGroup>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagGroup.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagGroup> mapFromJson(dynamic json) {
    final map = <String, DtoTagGroup>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroup.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagGroup-objects as value to a dart map
  static Map<String, List<DtoTagGroup>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagGroup>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroup.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}


class DtoTagGroupTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoTagGroupTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = DtoTagGroupTextColorEnum._(r'DARK');
  static const LIGHT = DtoTagGroupTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][DtoTagGroupTextColorEnum].
  static const values = <DtoTagGroupTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static DtoTagGroupTextColorEnum? fromJson(dynamic value) => DtoTagGroupTextColorEnumTypeTransformer().decode(value);

  static List<DtoTagGroupTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagGroupTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagGroupTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoTagGroupTextColorEnum] to String,
/// and [decode] dynamic data back to [DtoTagGroupTextColorEnum].
class DtoTagGroupTextColorEnumTypeTransformer {
  factory DtoTagGroupTextColorEnumTypeTransformer() => _instance ??= const DtoTagGroupTextColorEnumTypeTransformer._();

  const DtoTagGroupTextColorEnumTypeTransformer._();

  String encode(DtoTagGroupTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoTagGroupTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoTagGroupTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return DtoTagGroupTextColorEnum.DARK;
        case r'LIGHT': return DtoTagGroupTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoTagGroupTextColorEnumTypeTransformer] instance.
  static DtoTagGroupTextColorEnumTypeTransformer? _instance;
}


