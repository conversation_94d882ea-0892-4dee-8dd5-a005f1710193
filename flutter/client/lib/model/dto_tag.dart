//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTag {
  /// Returns a new [DtoTag] instance.
  DtoTag({
    required this.createdAt,
    this.deletedAt,
    required this.id,
    this.isDefault,
    this.numberUsing,
    this.position,
    required this.tagGroupId,
    required this.updatedAt,
    required this.value,
  });

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isDefault;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberUsing;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? position;

  String tagGroupId;

  String updatedAt;

  String value;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTag &&
     other.createdAt == createdAt &&
     other.deletedAt == deletedAt &&
     other.id == id &&
     other.isDefault == isDefault &&
     other.numberUsing == numberUsing &&
     other.position == position &&
     other.tagGroupId == tagGroupId &&
     other.updatedAt == updatedAt &&
     other.value == value;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (createdAt.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (id.hashCode) +
    (isDefault == null ? 0 : isDefault!.hashCode) +
    (numberUsing == null ? 0 : numberUsing!.hashCode) +
    (position == null ? 0 : position!.hashCode) +
    (tagGroupId.hashCode) +
    (updatedAt.hashCode) +
    (value.hashCode);

  @override
  String toString() => 'DtoTag[createdAt=$createdAt, deletedAt=$deletedAt, id=$id, isDefault=$isDefault, numberUsing=$numberUsing, position=$position, tagGroupId=$tagGroupId, updatedAt=$updatedAt, value=$value]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'created_at'] = this.createdAt;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'id'] = this.id;
    if (this.isDefault != null) {
      json[r'is_default'] = this.isDefault;
    } else {
      json[r'is_default'] = null;
    }
    if (this.numberUsing != null) {
      json[r'number_using'] = this.numberUsing;
    } else {
      json[r'number_using'] = null;
    }
    if (this.position != null) {
      json[r'position'] = this.position;
    } else {
      json[r'position'] = null;
    }
      json[r'tag_group_id'] = this.tagGroupId;
      json[r'updated_at'] = this.updatedAt;
      json[r'value'] = this.value;
    return json;
  }

  /// Returns a new [DtoTag] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTag? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTag[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTag[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTag(
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        id: mapValueOfType<String>(json, r'id')!,
        isDefault: mapValueOfType<bool>(json, r'is_default'),
        numberUsing: mapValueOfType<int>(json, r'number_using'),
        position: json[r'position'] == null
            ? null
            : num.parse(json[r'position'].toString()),
        tagGroupId: mapValueOfType<String>(json, r'tag_group_id')!,
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        value: mapValueOfType<String>(json, r'value')!,
      );
    }
    return null;
  }

  static List<DtoTag>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTag>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTag.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTag> mapFromJson(dynamic json) {
    final map = <String, DtoTag>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTag.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTag-objects as value to a dart map
  static Map<String, List<DtoTag>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTag>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTag.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'id',
    'tag_group_id',
    'updated_at',
    'value',
  };
}

