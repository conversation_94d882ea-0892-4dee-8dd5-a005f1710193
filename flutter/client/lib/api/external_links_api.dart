//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class ExternalLinksApi {
  ExternalLinksApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get External Link by Tag Number
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<Response> workspacesWorkspaceIdExternalLinksByTagTagNumberGetWithHttpInfo(String workspaceId, String tagNumber,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/by-tag/{tag_number}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_number}', tagNumber);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get External Link by Tag Number
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<DtoExternalLinkResponse?> workspacesWorkspaceIdExternalLinksByTagTagNumberGet(String workspaceId, String tagNumber,) async {
    final response = await workspacesWorkspaceIdExternalLinksByTagTagNumberGetWithHttpInfo(workspaceId, tagNumber,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoExternalLinkResponse',) as DtoExternalLinkResponse;
    
    }
    return null;
  }

  /// Delete Background
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdBackgroundSignedUrlDeleteWithHttpInfo(String workspaceId, String externalLinkId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Background
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  Future<void> workspacesWorkspaceIdExternalLinksExternalLinkIdBackgroundSignedUrlDelete(String workspaceId, String externalLinkId,) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdBackgroundSignedUrlDeleteWithHttpInfo(workspaceId, externalLinkId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create Background signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdBackgroundSignedUrlPostWithHttpInfo(String workspaceId, String externalLinkId, String method,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Background signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdExternalLinksExternalLinkIdBackgroundSignedUrlPost(String workspaceId, String externalLinkId, String method,) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdBackgroundSignedUrlPostWithHttpInfo(workspaceId, externalLinkId, method,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Delete External Link
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdDeleteWithHttpInfo(String workspaceId, String externalLinkId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete External Link
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  Future<void> workspacesWorkspaceIdExternalLinksExternalLinkIdDelete(String workspaceId, String externalLinkId,) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdDeleteWithHttpInfo(workspaceId, externalLinkId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get External Link
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   External Link ID
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdGetWithHttpInfo(String workspaceId, String externalLinkId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get External Link
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   External Link ID
  Future<DtoExternalLinkResponse?> workspacesWorkspaceIdExternalLinksExternalLinkIdGet(String workspaceId, String externalLinkId,) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdGetWithHttpInfo(workspaceId, externalLinkId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoExternalLinkResponse',) as DtoExternalLinkResponse;
    
    }
    return null;
  }

  /// Create icon signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [String] size:
  ///   Size
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdIconSignedUrlPostWithHttpInfo(String workspaceId, String externalLinkId, String method, { String? size, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}/icon-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));
    if (size != null) {
      queryParams.addAll(_queryParams('', 'size', size));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create icon signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   ExternalLink ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [String] size:
  ///   Size
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdExternalLinksExternalLinkIdIconSignedUrlPost(String workspaceId, String externalLinkId, String method, { String? size, }) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdIconSignedUrlPostWithHttpInfo(workspaceId, externalLinkId, method,  size: size, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Update External Link
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   External Link ID
  ///
  /// * [DtoPatchExternalLinkRequest] body (required):
  ///   External Link request object
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdPatchWithHttpInfo(String workspaceId, String externalLinkId, DtoPatchExternalLinkRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update External Link
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   External Link ID
  ///
  /// * [DtoPatchExternalLinkRequest] body (required):
  ///   External Link request object
  Future<DtoExternalLinkResponse?> workspacesWorkspaceIdExternalLinksExternalLinkIdPatch(String workspaceId, String externalLinkId, DtoPatchExternalLinkRequest body,) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdPatchWithHttpInfo(workspaceId, externalLinkId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoExternalLinkResponse',) as DtoExternalLinkResponse;
    
    }
    return null;
  }

  /// Update External Link
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   External Link ID
  ///
  /// * [DtoPutExternalLinkRequest] body (required):
  ///   External Link request object
  Future<Response> workspacesWorkspaceIdExternalLinksExternalLinkIdPutWithHttpInfo(String workspaceId, String externalLinkId, DtoPutExternalLinkRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links/{external_link_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{external_link_id}', externalLinkId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update External Link
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] externalLinkId (required):
  ///   External Link ID
  ///
  /// * [DtoPutExternalLinkRequest] body (required):
  ///   External Link request object
  Future<DtoExternalLinkResponse?> workspacesWorkspaceIdExternalLinksExternalLinkIdPut(String workspaceId, String externalLinkId, DtoPutExternalLinkRequest body,) async {
    final response = await workspacesWorkspaceIdExternalLinksExternalLinkIdPutWithHttpInfo(workspaceId, externalLinkId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoExternalLinkResponse',) as DtoExternalLinkResponse;
    
    }
    return null;
  }

  /// Get External Links
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] bookmark:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  ///
  /// * [List<String>] scopeTags:
  ///
  /// * [String] sort:
  ///
  /// * [List<String>] statusTags:
  ///
  /// * [List<String>] tagIds:
  ///
  /// * [List<String>] typeTags:
  ///
  /// * [bool] webhookTags:
  Future<Response> workspacesWorkspaceIdExternalLinksGetWithHttpInfo(String workspaceId, { bool? bookmark, String? facilitator, bool? onlyUnread, int? page, int? pageSize, String? query, List<String>? scopeTags, String? sort, List<String>? statusTags, List<String>? tagIds, List<String>? typeTags, bool? webhookTags, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (bookmark != null) {
      queryParams.addAll(_queryParams('', 'bookmark', bookmark));
    }
    if (facilitator != null) {
      queryParams.addAll(_queryParams('', 'facilitator', facilitator));
    }
    if (onlyUnread != null) {
      queryParams.addAll(_queryParams('', 'only_unread', onlyUnread));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
    if (scopeTags != null) {
      queryParams.addAll(_queryParams('csv', 'scope_tags', scopeTags));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }
    if (statusTags != null) {
      queryParams.addAll(_queryParams('csv', 'status_tags', statusTags));
    }
    if (tagIds != null) {
      queryParams.addAll(_queryParams('csv', 'tag_ids', tagIds));
    }
    if (typeTags != null) {
      queryParams.addAll(_queryParams('csv', 'type_tags', typeTags));
    }
    if (webhookTags != null) {
      queryParams.addAll(_queryParams('', 'webhook_tags', webhookTags));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get External Links
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] bookmark:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  ///
  /// * [List<String>] scopeTags:
  ///
  /// * [String] sort:
  ///
  /// * [List<String>] statusTags:
  ///
  /// * [List<String>] tagIds:
  ///
  /// * [List<String>] typeTags:
  ///
  /// * [bool] webhookTags:
  Future<DtoGetExternalLinksResponse?> workspacesWorkspaceIdExternalLinksGet(String workspaceId, { bool? bookmark, String? facilitator, bool? onlyUnread, int? page, int? pageSize, String? query, List<String>? scopeTags, String? sort, List<String>? statusTags, List<String>? tagIds, List<String>? typeTags, bool? webhookTags, }) async {
    final response = await workspacesWorkspaceIdExternalLinksGetWithHttpInfo(workspaceId,  bookmark: bookmark, facilitator: facilitator, onlyUnread: onlyUnread, page: page, pageSize: pageSize, query: query, scopeTags: scopeTags, sort: sort, statusTags: statusTags, tagIds: tagIds, typeTags: typeTags, webhookTags: webhookTags, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetExternalLinksResponse',) as DtoGetExternalLinksResponse;
    
    }
    return null;
  }

  /// Create External Link
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostExternalLinkRequest] body (required):
  ///   External Link request object
  Future<Response> workspacesWorkspaceIdExternalLinksPostWithHttpInfo(String workspaceId, DtoPostExternalLinkRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/external-links'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create External Link
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostExternalLinkRequest] body (required):
  ///   External Link request object
  Future<DtoExternalLinkResponse?> workspacesWorkspaceIdExternalLinksPost(String workspaceId, DtoPostExternalLinkRequest body,) async {
    final response = await workspacesWorkspaceIdExternalLinksPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoExternalLinkResponse',) as DtoExternalLinkResponse;
    
    }
    return null;
  }
}
