//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class FormItemsApi {
  FormItemsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Create signed url download FormItem File
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] formItemId (required):
  ///   FormItem ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [DtoFormItemFileSignedURLObjectRequest] body (required):
  ///   Get signed url request
  Future<Response> workspacesWorkspaceIdFormItemsFormItemIdFilesSignedUrlsPostWithHttpInfo(String workspaceId, String formItemId, String method, DtoFormItemFileSignedURLObjectRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/form-items/{form_item_id}/files/signed_urls'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{form_item_id}', formItemId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create signed url download FormItem File
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] formItemId (required):
  ///   FormItem ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [DtoFormItemFileSignedURLObjectRequest] body (required):
  ///   Get signed url request
  Future<DtoGetFileSignedURLResponse?> workspacesWorkspaceIdFormItemsFormItemIdFilesSignedUrlsPost(String workspaceId, String formItemId, String method, DtoFormItemFileSignedURLObjectRequest body,) async {
    final response = await workspacesWorkspaceIdFormItemsFormItemIdFilesSignedUrlsPostWithHttpInfo(workspaceId, formItemId, method, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetFileSignedURLResponse',) as DtoGetFileSignedURLResponse;
    
    }
    return null;
  }

  /// Create signed url download FormItem Image
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] formItemId (required):
  ///   FormItem ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [DtoFormItemImageSignedURLObjectRequest] body (required):
  ///   Get signed url request
  Future<Response> workspacesWorkspaceIdFormItemsFormItemIdImagesSignedUrlsPostWithHttpInfo(String workspaceId, String formItemId, String method, DtoFormItemImageSignedURLObjectRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/form-items/{form_item_id}/images/signed_urls'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{form_item_id}', formItemId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create signed url download FormItem Image
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] formItemId (required):
  ///   FormItem ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [DtoFormItemImageSignedURLObjectRequest] body (required):
  ///   Get signed url request
  Future<DtoGetSignedURLResponse?> workspacesWorkspaceIdFormItemsFormItemIdImagesSignedUrlsPost(String workspaceId, String formItemId, String method, DtoFormItemImageSignedURLObjectRequest body,) async {
    final response = await workspacesWorkspaceIdFormItemsFormItemIdImagesSignedUrlsPostWithHttpInfo(workspaceId, formItemId, method, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetSignedURLResponse',) as DtoGetSignedURLResponse;
    
    }
    return null;
  }
}
