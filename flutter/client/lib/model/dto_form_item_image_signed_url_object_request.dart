//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoFormItemImageSignedURLObjectRequest {
  /// Returns a new [DtoFormItemImageSignedURLObjectRequest] instance.
  DtoFormItemImageSignedURLObjectRequest({
    this.imagePaths = const [],
  });

  List<String> imagePaths;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoFormItemImageSignedURLObjectRequest &&
     other.imagePaths == imagePaths;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (imagePaths.hashCode);

  @override
  String toString() => 'DtoFormItemImageSignedURLObjectRequest[imagePaths=$imagePaths]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'image_paths'] = this.imagePaths;
    return json;
  }

  /// Returns a new [DtoFormItemImageSignedURLObjectRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoFormItemImageSignedURLObjectRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoFormItemImageSignedURLObjectRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoFormItemImageSignedURLObjectRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoFormItemImageSignedURLObjectRequest(
        imagePaths: json[r'image_paths'] is List
            ? (json[r'image_paths'] as List).cast<String>()
            : const [],
      );
    }
    return null;
  }

  static List<DtoFormItemImageSignedURLObjectRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoFormItemImageSignedURLObjectRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoFormItemImageSignedURLObjectRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoFormItemImageSignedURLObjectRequest> mapFromJson(dynamic json) {
    final map = <String, DtoFormItemImageSignedURLObjectRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFormItemImageSignedURLObjectRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoFormItemImageSignedURLObjectRequest-objects as value to a dart map
  static Map<String, List<DtoFormItemImageSignedURLObjectRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoFormItemImageSignedURLObjectRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFormItemImageSignedURLObjectRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'image_paths',
  };
}

