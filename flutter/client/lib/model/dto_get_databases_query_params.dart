//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetDatabasesQueryParams {
  /// Returns a new [DtoGetDatabasesQueryParams] instance.
  DtoGetDatabasesQueryParams({
    this.forGuest,
    this.query,
    this.roleTag,
    this.tagIds = const [],
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? forGuest;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? query;

  DtoGetDatabasesQueryParamsRoleTagEnum? roleTag;

  List<String> tagIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetDatabasesQueryParams &&
     other.forGuest == forGuest &&
     other.query == query &&
     other.roleTag == roleTag &&
     other.tagIds == tagIds;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (forGuest == null ? 0 : forGuest!.hashCode) +
    (query == null ? 0 : query!.hashCode) +
    (roleTag == null ? 0 : roleTag!.hashCode) +
    (tagIds.hashCode);

  @override
  String toString() => 'DtoGetDatabasesQueryParams[forGuest=$forGuest, query=$query, roleTag=$roleTag, tagIds=$tagIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.forGuest != null) {
      json[r'for_guest'] = this.forGuest;
    } else {
      json[r'for_guest'] = null;
    }
    if (this.query != null) {
      json[r'query'] = this.query;
    } else {
      json[r'query'] = null;
    }
    if (this.roleTag != null) {
      json[r'role_tag'] = this.roleTag;
    } else {
      json[r'role_tag'] = null;
    }
      json[r'tag_ids'] = this.tagIds;
    return json;
  }

  /// Returns a new [DtoGetDatabasesQueryParams] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetDatabasesQueryParams? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetDatabasesQueryParams[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetDatabasesQueryParams[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetDatabasesQueryParams(
        forGuest: mapValueOfType<bool>(json, r'for_guest'),
        query: mapValueOfType<String>(json, r'query'),
        roleTag: DtoGetDatabasesQueryParamsRoleTagEnum.fromJson(json[r'role_tag']),
        tagIds: json[r'tag_ids'] is List
            ? (json[r'tag_ids'] as List).cast<String>()
            : const [],
      );
    }
    return null;
  }

  static List<DtoGetDatabasesQueryParams>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetDatabasesQueryParams>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetDatabasesQueryParams.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetDatabasesQueryParams> mapFromJson(dynamic json) {
    final map = <String, DtoGetDatabasesQueryParams>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetDatabasesQueryParams.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetDatabasesQueryParams-objects as value to a dart map
  static Map<String, List<DtoGetDatabasesQueryParams>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetDatabasesQueryParams>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetDatabasesQueryParams.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}


class DtoGetDatabasesQueryParamsRoleTagEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoGetDatabasesQueryParamsRoleTagEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const MANAGER = DtoGetDatabasesQueryParamsRoleTagEnum._(r'MANAGER');
  static const STAFF = DtoGetDatabasesQueryParamsRoleTagEnum._(r'STAFF');

  /// List of all possible values in this [enum][DtoGetDatabasesQueryParamsRoleTagEnum].
  static const values = <DtoGetDatabasesQueryParamsRoleTagEnum>[
    MANAGER,
    STAFF,
  ];

  static DtoGetDatabasesQueryParamsRoleTagEnum? fromJson(dynamic value) => DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer().decode(value);

  static List<DtoGetDatabasesQueryParamsRoleTagEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetDatabasesQueryParamsRoleTagEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetDatabasesQueryParamsRoleTagEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoGetDatabasesQueryParamsRoleTagEnum] to String,
/// and [decode] dynamic data back to [DtoGetDatabasesQueryParamsRoleTagEnum].
class DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer {
  factory DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer() => _instance ??= const DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer._();

  const DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer._();

  String encode(DtoGetDatabasesQueryParamsRoleTagEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoGetDatabasesQueryParamsRoleTagEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoGetDatabasesQueryParamsRoleTagEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'MANAGER': return DtoGetDatabasesQueryParamsRoleTagEnum.MANAGER;
        case r'STAFF': return DtoGetDatabasesQueryParamsRoleTagEnum.STAFF;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer] instance.
  static DtoGetDatabasesQueryParamsRoleTagEnumTypeTransformer? _instance;
}


