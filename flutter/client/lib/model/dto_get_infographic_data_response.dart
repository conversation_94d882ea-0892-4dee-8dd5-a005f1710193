//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetInfographicDataResponse {
  /// Returns a new [DtoGetInfographicDataResponse] instance.
  DtoGetInfographicDataResponse({
    this.recordExpiredCount,
    this.recordIncompleteCount,
    this.recordNoPicCount,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? recordExpiredCount;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? recordIncompleteCount;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? recordNoPicCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetInfographicDataResponse &&
     other.recordExpiredCount == recordExpiredCount &&
     other.recordIncompleteCount == recordIncompleteCount &&
     other.recordNoPicCount == recordNoPicCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (recordExpiredCount == null ? 0 : recordExpiredCount!.hashCode) +
    (recordIncompleteCount == null ? 0 : recordIncompleteCount!.hashCode) +
    (recordNoPicCount == null ? 0 : recordNoPicCount!.hashCode);

  @override
  String toString() => 'DtoGetInfographicDataResponse[recordExpiredCount=$recordExpiredCount, recordIncompleteCount=$recordIncompleteCount, recordNoPicCount=$recordNoPicCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.recordExpiredCount != null) {
      json[r'record_expired_count'] = this.recordExpiredCount;
    } else {
      json[r'record_expired_count'] = null;
    }
    if (this.recordIncompleteCount != null) {
      json[r'record_incomplete_count'] = this.recordIncompleteCount;
    } else {
      json[r'record_incomplete_count'] = null;
    }
    if (this.recordNoPicCount != null) {
      json[r'record_no_pic_count'] = this.recordNoPicCount;
    } else {
      json[r'record_no_pic_count'] = null;
    }
    return json;
  }

  /// Returns a new [DtoGetInfographicDataResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetInfographicDataResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetInfographicDataResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetInfographicDataResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetInfographicDataResponse(
        recordExpiredCount: mapValueOfType<int>(json, r'record_expired_count'),
        recordIncompleteCount: mapValueOfType<int>(json, r'record_incomplete_count'),
        recordNoPicCount: mapValueOfType<int>(json, r'record_no_pic_count'),
      );
    }
    return null;
  }

  static List<DtoGetInfographicDataResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetInfographicDataResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetInfographicDataResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetInfographicDataResponse> mapFromJson(dynamic json) {
    final map = <String, DtoGetInfographicDataResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetInfographicDataResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetInfographicDataResponse-objects as value to a dart map
  static Map<String, List<DtoGetInfographicDataResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetInfographicDataResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetInfographicDataResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

