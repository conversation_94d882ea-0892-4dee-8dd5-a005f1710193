//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoWorkspaceInvitingMember {
  /// Returns a new [DtoWorkspaceInvitingMember] instance.
  DtoWorkspaceInvitingMember({
    this.member,
    this.workspace,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoMember? member;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoWorkspaceResponse? workspace;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoWorkspaceInvitingMember &&
     other.member == member &&
     other.workspace == workspace;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (member == null ? 0 : member!.hashCode) +
    (workspace == null ? 0 : workspace!.hashCode);

  @override
  String toString() => 'DtoWorkspaceInvitingMember[member=$member, workspace=$workspace]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.member != null) {
      json[r'member'] = this.member;
    } else {
      json[r'member'] = null;
    }
    if (this.workspace != null) {
      json[r'workspace'] = this.workspace;
    } else {
      json[r'workspace'] = null;
    }
    return json;
  }

  /// Returns a new [DtoWorkspaceInvitingMember] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoWorkspaceInvitingMember? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoWorkspaceInvitingMember[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoWorkspaceInvitingMember[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoWorkspaceInvitingMember(
        member: DtoMember.fromJson(json[r'member']),
        workspace: DtoWorkspaceResponse.fromJson(json[r'workspace']),
      );
    }
    return null;
  }

  static List<DtoWorkspaceInvitingMember>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoWorkspaceInvitingMember>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoWorkspaceInvitingMember.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoWorkspaceInvitingMember> mapFromJson(dynamic json) {
    final map = <String, DtoWorkspaceInvitingMember>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoWorkspaceInvitingMember.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoWorkspaceInvitingMember-objects as value to a dart map
  static Map<String, List<DtoWorkspaceInvitingMember>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoWorkspaceInvitingMember>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoWorkspaceInvitingMember.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

