//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class MemberBookmarkedItemsApi {
  MemberBookmarkedItemsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get MemberBookmarkedItems
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] queryType:
  ///
  /// * [String] sort:
  Future<Response> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsGetWithHttpInfo(String workspaceId, String memberId, { int? page, int? pageSize, String? queryType, String? sort, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/member-bookmarked-items'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (queryType != null) {
      queryParams.addAll(_queryParams('', 'query_type', queryType));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get MemberBookmarkedItems
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] queryType:
  ///
  /// * [String] sort:
  Future<DtoGetMemberBookmarkedItemsResponse?> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsGet(String workspaceId, String memberId, { int? page, int? pageSize, String? queryType, String? sort, }) async {
    final response = await workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsGetWithHttpInfo(workspaceId, memberId,  page: page, pageSize: pageSize, queryType: queryType, sort: sort, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMemberBookmarkedItemsResponse',) as DtoGetMemberBookmarkedItemsResponse;
    
    }
    return null;
  }

  /// Delete MemberBookmarkedItem
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] memberBookmarkedItemId (required):
  ///   Member Bookmarked Item ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsMemberBookmarkedItemIdDeleteWithHttpInfo(String workspaceId, String memberId, String memberBookmarkedItemId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/member-bookmarked-items/{member_bookmarked_item_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId)
      .replaceAll('{member_bookmarked_item_id}', memberBookmarkedItemId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete MemberBookmarkedItem
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] memberBookmarkedItemId (required):
  ///   Member Bookmarked Item ID
  Future<void> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsMemberBookmarkedItemIdDelete(String workspaceId, String memberId, String memberBookmarkedItemId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsMemberBookmarkedItemIdDeleteWithHttpInfo(workspaceId, memberId, memberBookmarkedItemId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get MemberBookmarkedItem
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] memberBookmarkedItemId (required):
  ///   Bookmark ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsMemberBookmarkedItemIdGetWithHttpInfo(String workspaceId, String memberId, String memberBookmarkedItemId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/member-bookmarked-items/{member_bookmarked_item_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId)
      .replaceAll('{member_bookmarked_item_id}', memberBookmarkedItemId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get MemberBookmarkedItem
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] memberBookmarkedItemId (required):
  ///   Bookmark ID
  Future<ModelMemberBookmarkedItem?> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsMemberBookmarkedItemIdGet(String workspaceId, String memberId, String memberBookmarkedItemId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsMemberBookmarkedItemIdGetWithHttpInfo(workspaceId, memberId, memberBookmarkedItemId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'ModelMemberBookmarkedItem',) as ModelMemberBookmarkedItem;
    
    }
    return null;
  }

  /// Create MemberBookmarkedItem
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPostMemberBookmarkedItemRequest] body (required):
  ///   Member Bookmarked Item request Object
  Future<Response> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsPostWithHttpInfo(String workspaceId, String memberId, DtoPostMemberBookmarkedItemRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/member-bookmarked-items'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create MemberBookmarkedItem
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPostMemberBookmarkedItemRequest] body (required):
  ///   Member Bookmarked Item request Object
  Future<DtoMemberBookmarkedItem?> workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsPost(String workspaceId, String memberId, DtoPostMemberBookmarkedItemRequest body,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdMemberBookmarkedItemsPostWithHttpInfo(workspaceId, memberId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMemberBookmarkedItem',) as DtoMemberBookmarkedItem;
    
    }
    return null;
  }
}
