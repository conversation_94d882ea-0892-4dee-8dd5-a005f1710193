//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoSeatConsumingInfo {
  /// Returns a new [DtoSeatConsumingInfo] instance.
  DtoSeatConsumingInfo({
    this.externalLinkConsumingSeats,
    this.guestConsumingSeats,
    this.managerConsumingSeats,
    this.numberExternalLink,
    this.numberGuest,
    this.numberManager,
    this.numberStaff,
    this.staffConsumingSeats,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? externalLinkConsumingSeats;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? guestConsumingSeats;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? managerConsumingSeats;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberExternalLink;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberGuest;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberManager;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberStaff;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? staffConsumingSeats;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoSeatConsumingInfo &&
     other.externalLinkConsumingSeats == externalLinkConsumingSeats &&
     other.guestConsumingSeats == guestConsumingSeats &&
     other.managerConsumingSeats == managerConsumingSeats &&
     other.numberExternalLink == numberExternalLink &&
     other.numberGuest == numberGuest &&
     other.numberManager == numberManager &&
     other.numberStaff == numberStaff &&
     other.staffConsumingSeats == staffConsumingSeats;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (externalLinkConsumingSeats == null ? 0 : externalLinkConsumingSeats!.hashCode) +
    (guestConsumingSeats == null ? 0 : guestConsumingSeats!.hashCode) +
    (managerConsumingSeats == null ? 0 : managerConsumingSeats!.hashCode) +
    (numberExternalLink == null ? 0 : numberExternalLink!.hashCode) +
    (numberGuest == null ? 0 : numberGuest!.hashCode) +
    (numberManager == null ? 0 : numberManager!.hashCode) +
    (numberStaff == null ? 0 : numberStaff!.hashCode) +
    (staffConsumingSeats == null ? 0 : staffConsumingSeats!.hashCode);

  @override
  String toString() => 'DtoSeatConsumingInfo[externalLinkConsumingSeats=$externalLinkConsumingSeats, guestConsumingSeats=$guestConsumingSeats, managerConsumingSeats=$managerConsumingSeats, numberExternalLink=$numberExternalLink, numberGuest=$numberGuest, numberManager=$numberManager, numberStaff=$numberStaff, staffConsumingSeats=$staffConsumingSeats]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.externalLinkConsumingSeats != null) {
      json[r'external_link_consuming_seats'] = this.externalLinkConsumingSeats;
    } else {
      json[r'external_link_consuming_seats'] = null;
    }
    if (this.guestConsumingSeats != null) {
      json[r'guest_consuming_seats'] = this.guestConsumingSeats;
    } else {
      json[r'guest_consuming_seats'] = null;
    }
    if (this.managerConsumingSeats != null) {
      json[r'manager_consuming_seats'] = this.managerConsumingSeats;
    } else {
      json[r'manager_consuming_seats'] = null;
    }
    if (this.numberExternalLink != null) {
      json[r'number_external_link'] = this.numberExternalLink;
    } else {
      json[r'number_external_link'] = null;
    }
    if (this.numberGuest != null) {
      json[r'number_guest'] = this.numberGuest;
    } else {
      json[r'number_guest'] = null;
    }
    if (this.numberManager != null) {
      json[r'number_manager'] = this.numberManager;
    } else {
      json[r'number_manager'] = null;
    }
    if (this.numberStaff != null) {
      json[r'number_staff'] = this.numberStaff;
    } else {
      json[r'number_staff'] = null;
    }
    if (this.staffConsumingSeats != null) {
      json[r'staff_consuming_seats'] = this.staffConsumingSeats;
    } else {
      json[r'staff_consuming_seats'] = null;
    }
    return json;
  }

  /// Returns a new [DtoSeatConsumingInfo] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoSeatConsumingInfo? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoSeatConsumingInfo[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoSeatConsumingInfo[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoSeatConsumingInfo(
        externalLinkConsumingSeats: json[r'external_link_consuming_seats'] == null
            ? null
            : num.parse(json[r'external_link_consuming_seats'].toString()),
        guestConsumingSeats: json[r'guest_consuming_seats'] == null
            ? null
            : num.parse(json[r'guest_consuming_seats'].toString()),
        managerConsumingSeats: json[r'manager_consuming_seats'] == null
            ? null
            : num.parse(json[r'manager_consuming_seats'].toString()),
        numberExternalLink: mapValueOfType<int>(json, r'number_external_link'),
        numberGuest: mapValueOfType<int>(json, r'number_guest'),
        numberManager: mapValueOfType<int>(json, r'number_manager'),
        numberStaff: mapValueOfType<int>(json, r'number_staff'),
        staffConsumingSeats: json[r'staff_consuming_seats'] == null
            ? null
            : num.parse(json[r'staff_consuming_seats'].toString()),
      );
    }
    return null;
  }

  static List<DtoSeatConsumingInfo>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoSeatConsumingInfo>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoSeatConsumingInfo.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoSeatConsumingInfo> mapFromJson(dynamic json) {
    final map = <String, DtoSeatConsumingInfo>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSeatConsumingInfo.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoSeatConsumingInfo-objects as value to a dart map
  static Map<String, List<DtoSeatConsumingInfo>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoSeatConsumingInfo>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSeatConsumingInfo.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

