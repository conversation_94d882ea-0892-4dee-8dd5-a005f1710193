# openapi.api.AuthApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to */api/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**authCallbacksSignInWithApplePost**](AuthApi.md#authcallbackssigninwithapplepost) | **POST** /auth/callbacks/sign-in-with-apple | Sign in with Apple Callback
[**authCustomTokenPost**](AuthApi.md#authcustomtokenpost) | **POST** /auth/custom-token | Create custom token
[**authForgotPasswordPut**](AuthApi.md#authforgotpasswordput) | **PUT** /auth/forgot-password | forgot password
[**authInvitationsPost**](AuthApi.md#authinvitationspost) | **POST** /auth/invitations | Get Inviting Member Info
[**authRevokeTokensPost**](AuthApi.md#authrevoketokenspost) | **POST** /auth/revoke-tokens | Revoke Tokens
[**authSignInWithOauthPost**](AuthApi.md#authsigninwithoauthpost) | **POST** /auth/sign-in-with-oauth | Sign in with OAuth


# **authCallbacksSignInWithApplePost**
> authCallbacksSignInWithApplePost(body)

Sign in with Apple Callback

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = AuthApi();
final body = DtoSignInWithOAuthRequest(); // DtoSignInWithOAuthRequest | Sign in with Apple request object

try {
    api_instance.authCallbacksSignInWithApplePost(body);
} catch (e) {
    print('Exception when calling AuthApi->authCallbacksSignInWithApplePost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | [**DtoSignInWithOAuthRequest**](DtoSignInWithOAuthRequest.md)| Sign in with Apple request object | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authCustomTokenPost**
> DtoPostAuthCustomTokenResponse authCustomTokenPost(workspaceId)

Create custom token

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = AuthApi();
final workspaceId = workspaceId_example; // String | Workspace ID

try {
    final result = api_instance.authCustomTokenPost(workspaceId);
    print(result);
} catch (e) {
    print('Exception when calling AuthApi->authCustomTokenPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 

### Return type

[**DtoPostAuthCustomTokenResponse**](DtoPostAuthCustomTokenResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authForgotPasswordPut**
> authForgotPasswordPut(body)

forgot password

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = AuthApi();
final body = DtoForgotPasswordRequest(); // DtoForgotPasswordRequest | ChangePassword object

try {
    api_instance.authForgotPasswordPut(body);
} catch (e) {
    print('Exception when calling AuthApi->authForgotPasswordPut: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | [**DtoForgotPasswordRequest**](DtoForgotPasswordRequest.md)| ChangePassword object | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authInvitationsPost**
> DtoWorkspaceInvitingMember authInvitationsPost(body)

Get Inviting Member Info

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = AuthApi();
final body = DtoInvitingMemberInfoRequest(); // DtoInvitingMemberInfoRequest | Inviting Member Info Request

try {
    final result = api_instance.authInvitationsPost(body);
    print(result);
} catch (e) {
    print('Exception when calling AuthApi->authInvitationsPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | [**DtoInvitingMemberInfoRequest**](DtoInvitingMemberInfoRequest.md)| Inviting Member Info Request | 

### Return type

[**DtoWorkspaceInvitingMember**](DtoWorkspaceInvitingMember.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authRevokeTokensPost**
> authRevokeTokensPost()

Revoke Tokens

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = AuthApi();

try {
    api_instance.authRevokeTokensPost();
} catch (e) {
    print('Exception when calling AuthApi->authRevokeTokensPost: $e\n');
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authSignInWithOauthPost**
> DtoPostAuthCustomTokenResponse authSignInWithOauthPost(body)

Sign in with OAuth

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = AuthApi();
final body = DtoSignInWithOAuthRequest(); // DtoSignInWithOAuthRequest | Sign in with OAuth request object

try {
    final result = api_instance.authSignInWithOauthPost(body);
    print(result);
} catch (e) {
    print('Exception when calling AuthApi->authSignInWithOauthPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | [**DtoSignInWithOAuthRequest**](DtoSignInWithOAuthRequest.md)| Sign in with OAuth request object | 

### Return type

[**DtoPostAuthCustomTokenResponse**](DtoPostAuthCustomTokenResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

