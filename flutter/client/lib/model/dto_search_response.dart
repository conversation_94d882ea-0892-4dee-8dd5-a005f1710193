//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoSearchResponse {
  /// Returns a new [DtoSearchResponse] instance.
  DtoSearchResponse({
    this.chat,
    this.record,
    this.template,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoMessage? chat;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoRecord? record;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoTemplateResponse? template;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoSearchResponse &&
     other.chat == chat &&
     other.record == record &&
     other.template == template;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (chat == null ? 0 : chat!.hashCode) +
    (record == null ? 0 : record!.hashCode) +
    (template == null ? 0 : template!.hashCode);

  @override
  String toString() => 'DtoSearchResponse[chat=$chat, record=$record, template=$template]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.chat != null) {
      json[r'chat'] = this.chat;
    } else {
      json[r'chat'] = null;
    }
    if (this.record != null) {
      json[r'record'] = this.record;
    } else {
      json[r'record'] = null;
    }
    if (this.template != null) {
      json[r'template'] = this.template;
    } else {
      json[r'template'] = null;
    }
    return json;
  }

  /// Returns a new [DtoSearchResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoSearchResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoSearchResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoSearchResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoSearchResponse(
        chat: DtoMessage.fromJson(json[r'chat']),
        record: DtoRecord.fromJson(json[r'record']),
        template: DtoTemplateResponse.fromJson(json[r'template']),
      );
    }
    return null;
  }

  static List<DtoSearchResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoSearchResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoSearchResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoSearchResponse> mapFromJson(dynamic json) {
    final map = <String, DtoSearchResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSearchResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoSearchResponse-objects as value to a dart map
  static Map<String, List<DtoSearchResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoSearchResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSearchResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

