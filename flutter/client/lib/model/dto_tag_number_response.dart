//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagNumberResponse {
  /// Returns a new [DtoTagNumberResponse] instance.
  DtoTagNumberResponse({
    this.entityId,
    required this.entityType,
    this.externalLink,
    this.member,
    this.record,
    this.tagGroup,
    this.tagNumber,
    this.template,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? entityId;

  DtoTagNumberResponseEntityTypeEnum entityType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoExternalLinkResponse? externalLink;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoMember? member;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoRecord? record;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoTagGroup? tagGroup;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoTemplateResponse? template;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagNumberResponse &&
     other.entityId == entityId &&
     other.entityType == entityType &&
     other.externalLink == externalLink &&
     other.member == member &&
     other.record == record &&
     other.tagGroup == tagGroup &&
     other.tagNumber == tagNumber &&
     other.template == template;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (entityId == null ? 0 : entityId!.hashCode) +
    (entityType.hashCode) +
    (externalLink == null ? 0 : externalLink!.hashCode) +
    (member == null ? 0 : member!.hashCode) +
    (record == null ? 0 : record!.hashCode) +
    (tagGroup == null ? 0 : tagGroup!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (template == null ? 0 : template!.hashCode);

  @override
  String toString() => 'DtoTagNumberResponse[entityId=$entityId, entityType=$entityType, externalLink=$externalLink, member=$member, record=$record, tagGroup=$tagGroup, tagNumber=$tagNumber, template=$template]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.entityId != null) {
      json[r'entity_id'] = this.entityId;
    } else {
      json[r'entity_id'] = null;
    }
      json[r'entity_type'] = this.entityType;
    if (this.externalLink != null) {
      json[r'external_link'] = this.externalLink;
    } else {
      json[r'external_link'] = null;
    }
    if (this.member != null) {
      json[r'member'] = this.member;
    } else {
      json[r'member'] = null;
    }
    if (this.record != null) {
      json[r'record'] = this.record;
    } else {
      json[r'record'] = null;
    }
    if (this.tagGroup != null) {
      json[r'tag_group'] = this.tagGroup;
    } else {
      json[r'tag_group'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
    if (this.template != null) {
      json[r'template'] = this.template;
    } else {
      json[r'template'] = null;
    }
    return json;
  }

  /// Returns a new [DtoTagNumberResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagNumberResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagNumberResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagNumberResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagNumberResponse(
        entityId: mapValueOfType<String>(json, r'entity_id'),
        entityType: DtoTagNumberResponseEntityTypeEnum.fromJson(json[r'entity_type'])!,
        externalLink: DtoExternalLinkResponse.fromJson(json[r'external_link']),
        member: DtoMember.fromJson(json[r'member']),
        record: DtoRecord.fromJson(json[r'record']),
        tagGroup: DtoTagGroup.fromJson(json[r'tag_group']),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        template: DtoTemplateResponse.fromJson(json[r'template']),
      );
    }
    return null;
  }

  static List<DtoTagNumberResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagNumberResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagNumberResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagNumberResponse> mapFromJson(dynamic json) {
    final map = <String, DtoTagNumberResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagNumberResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagNumberResponse-objects as value to a dart map
  static Map<String, List<DtoTagNumberResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagNumberResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagNumberResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'entity_type',
  };
}


class DtoTagNumberResponseEntityTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoTagNumberResponseEntityTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const record = DtoTagNumberResponseEntityTypeEnum._(r'record');
  static const template = DtoTagNumberResponseEntityTypeEnum._(r'template');
  static const tagGroup = DtoTagNumberResponseEntityTypeEnum._(r'tag_group');
  static const member = DtoTagNumberResponseEntityTypeEnum._(r'member');
  static const externalLink = DtoTagNumberResponseEntityTypeEnum._(r'external_link');

  /// List of all possible values in this [enum][DtoTagNumberResponseEntityTypeEnum].
  static const values = <DtoTagNumberResponseEntityTypeEnum>[
    record,
    template,
    tagGroup,
    member,
    externalLink,
  ];

  static DtoTagNumberResponseEntityTypeEnum? fromJson(dynamic value) => DtoTagNumberResponseEntityTypeEnumTypeTransformer().decode(value);

  static List<DtoTagNumberResponseEntityTypeEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagNumberResponseEntityTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagNumberResponseEntityTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoTagNumberResponseEntityTypeEnum] to String,
/// and [decode] dynamic data back to [DtoTagNumberResponseEntityTypeEnum].
class DtoTagNumberResponseEntityTypeEnumTypeTransformer {
  factory DtoTagNumberResponseEntityTypeEnumTypeTransformer() => _instance ??= const DtoTagNumberResponseEntityTypeEnumTypeTransformer._();

  const DtoTagNumberResponseEntityTypeEnumTypeTransformer._();

  String encode(DtoTagNumberResponseEntityTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoTagNumberResponseEntityTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoTagNumberResponseEntityTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'record': return DtoTagNumberResponseEntityTypeEnum.record;
        case r'template': return DtoTagNumberResponseEntityTypeEnum.template;
        case r'tag_group': return DtoTagNumberResponseEntityTypeEnum.tagGroup;
        case r'member': return DtoTagNumberResponseEntityTypeEnum.member;
        case r'external_link': return DtoTagNumberResponseEntityTypeEnum.externalLink;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoTagNumberResponseEntityTypeEnumTypeTransformer] instance.
  static DtoTagNumberResponseEntityTypeEnumTypeTransformer? _instance;
}


