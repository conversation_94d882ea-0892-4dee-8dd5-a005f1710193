//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoUpdateEmailRequest {
  /// Returns a new [DtoUpdateEmailRequest] instance.
  DtoUpdateEmailRequest({
    required this.newIdToken,
  });

  String newIdToken;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoUpdateEmailRequest &&
     other.newIdToken == newIdToken;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (newIdToken.hashCode);

  @override
  String toString() => 'DtoUpdateEmailRequest[newIdToken=$newIdToken]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'newIdToken'] = this.newIdToken;
    return json;
  }

  /// Returns a new [DtoUpdateEmailRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoUpdateEmailRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoUpdateEmailRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoUpdateEmailRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoUpdateEmailRequest(
        newIdToken: mapValueOfType<String>(json, r'newIdToken')!,
      );
    }
    return null;
  }

  static List<DtoUpdateEmailRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoUpdateEmailRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoUpdateEmailRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoUpdateEmailRequest> mapFromJson(dynamic json) {
    final map = <String, DtoUpdateEmailRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoUpdateEmailRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoUpdateEmailRequest-objects as value to a dart map
  static Map<String, List<DtoUpdateEmailRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoUpdateEmailRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoUpdateEmailRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'newIdToken',
  };
}

