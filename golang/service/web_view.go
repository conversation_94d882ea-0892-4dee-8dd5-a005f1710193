package service

import (
	"context"

	"github.com/google/uuid"

	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
	"clipcrow/essentialworkware/repository"
)

type WebViewService interface {
	Create(ctx context.Context, tx repository.Repository, databaseID uuid.UUID, webViewReq *dto.PostWebView) (*model.WebView, error)
	FindByID(ctx context.Context, databaseID uuid.UUID) (dto.WebViewResponse, error)
	FindByIDs(ctx context.Context, databaseIDs []uuid.UUID) (map[uuid.UUID]dto.WebViewResponse, error)
	FlexibleUpdate(ctx context.Context, tx repository.Repository, databaseID uuid.UUID, webView *dto.PatchWebView) (*model.WebView, error)
}
