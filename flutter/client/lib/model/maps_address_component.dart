//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MapsAddressComponent {
  /// Returns a new [MapsAddressComponent] instance.
  MapsAddressComponent({
    this.longName,
    this.shortName,
    this.types = const [],
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? longName;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? shortName;

  List<String> types;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MapsAddressComponent &&
     other.longName == longName &&
     other.shortName == shortName &&
     other.types == types;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (longName == null ? 0 : longName!.hashCode) +
    (shortName == null ? 0 : shortName!.hashCode) +
    (types.hashCode);

  @override
  String toString() => 'MapsAddressComponent[longName=$longName, shortName=$shortName, types=$types]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.longName != null) {
      json[r'long_name'] = this.longName;
    } else {
      json[r'long_name'] = null;
    }
    if (this.shortName != null) {
      json[r'short_name'] = this.shortName;
    } else {
      json[r'short_name'] = null;
    }
      json[r'types'] = this.types;
    return json;
  }

  /// Returns a new [MapsAddressComponent] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MapsAddressComponent? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MapsAddressComponent[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MapsAddressComponent[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MapsAddressComponent(
        longName: mapValueOfType<String>(json, r'long_name'),
        shortName: mapValueOfType<String>(json, r'short_name'),
        types: json[r'types'] is List
            ? (json[r'types'] as List).cast<String>()
            : const [],
      );
    }
    return null;
  }

  static List<MapsAddressComponent>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MapsAddressComponent>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MapsAddressComponent.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MapsAddressComponent> mapFromJson(dynamic json) {
    final map = <String, MapsAddressComponent>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsAddressComponent.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MapsAddressComponent-objects as value to a dart map
  static Map<String, List<MapsAddressComponent>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MapsAddressComponent>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsAddressComponent.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

