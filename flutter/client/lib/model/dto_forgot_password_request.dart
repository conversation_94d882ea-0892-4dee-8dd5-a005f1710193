//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoForgotPasswordRequest {
  /// Returns a new [DtoForgotPasswordRequest] instance.
  DtoForgotPasswordRequest({
    required this.email,
    required this.newPassword,
    required this.otpCode,
  });

  String email;

  String newPassword;

  String otpCode;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoForgotPasswordRequest &&
     other.email == email &&
     other.newPassword == newPassword &&
     other.otpCode == otpCode;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (email.hashCode) +
    (newPassword.hashCode) +
    (otpCode.hashCode);

  @override
  String toString() => 'DtoForgotPasswordRequest[email=$email, newPassword=$newPassword, otpCode=$otpCode]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'email'] = this.email;
      json[r'new_password'] = this.newPassword;
      json[r'otp_code'] = this.otpCode;
    return json;
  }

  /// Returns a new [DtoForgotPasswordRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoForgotPasswordRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoForgotPasswordRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoForgotPasswordRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoForgotPasswordRequest(
        email: mapValueOfType<String>(json, r'email')!,
        newPassword: mapValueOfType<String>(json, r'new_password')!,
        otpCode: mapValueOfType<String>(json, r'otp_code')!,
      );
    }
    return null;
  }

  static List<DtoForgotPasswordRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoForgotPasswordRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoForgotPasswordRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoForgotPasswordRequest> mapFromJson(dynamic json) {
    final map = <String, DtoForgotPasswordRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoForgotPasswordRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoForgotPasswordRequest-objects as value to a dart map
  static Map<String, List<DtoForgotPasswordRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoForgotPasswordRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoForgotPasswordRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'email',
    'new_password',
    'otp_code',
  };
}

