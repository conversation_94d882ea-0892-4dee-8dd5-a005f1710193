# openapi.model.ClipcrowEssentialworkwarePkgMapsGeocodingResultPlusCode

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**compoundCode** | **String** | CompoundCode is a 6 character or longer local code with an explicit location (CWC8+R9, Mountain View, CA, USA). | [optional] 
**globalCode** | **String** | GlobalCode is a 4 character area code and 6 character or longer local code (849VCWC8+R9). | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


