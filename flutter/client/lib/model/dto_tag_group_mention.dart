//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagGroupMention {
  /// Returns a new [DtoTagGroupMention] instance.
  DtoTagGroupMention({
    this.color,
    this.description,
    required this.id,
    required this.name,
    this.tag = const [],
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? color;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  String id;

  String name;

  List<DtoTagMention> tag;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagGroupMention &&
     other.color == color &&
     other.description == description &&
     other.id == id &&
     other.name == name &&
     other.tag == tag;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (color == null ? 0 : color!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (id.hashCode) +
    (name.hashCode) +
    (tag.hashCode);

  @override
  String toString() => 'DtoTagGroupMention[color=$color, description=$description, id=$id, name=$name, tag=$tag]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.color != null) {
      json[r'color'] = this.color;
    } else {
      json[r'color'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'id'] = this.id;
      json[r'name'] = this.name;
      json[r'tag'] = this.tag;
    return json;
  }

  /// Returns a new [DtoTagGroupMention] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagGroupMention? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagGroupMention[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagGroupMention[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagGroupMention(
        color: mapValueOfType<String>(json, r'color'),
        description: mapValueOfType<String>(json, r'description'),
        id: mapValueOfType<String>(json, r'id')!,
        name: mapValueOfType<String>(json, r'name')!,
        tag: DtoTagMention.listFromJson(json[r'tag']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoTagGroupMention>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagGroupMention>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagGroupMention.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagGroupMention> mapFromJson(dynamic json) {
    final map = <String, DtoTagGroupMention>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroupMention.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagGroupMention-objects as value to a dart map
  static Map<String, List<DtoTagGroupMention>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagGroupMention>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroupMention.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
    'name',
  };
}

