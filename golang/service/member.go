package service

import (
	"context"

	"github.com/google/uuid"

	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
	"clipcrow/essentialworkware/pkg/auth"
	"clipcrow/essentialworkware/service/imageservice"
)

// MemberService is interface
type MemberService interface {
	imageservice.MemberImageService
	FindByEmailAndWorkspaceID(ctx context.Context, email string, workspaceID uuid.UUID) (*model.Member, error)
	MapAuthTokenClaimsToMember(ctx context.Context, claims *auth.AuthTokenClaims) *model.Member
	CreateFirstMemberOfWorkspace(ctx context.Context, claims *auth.AuthTokenClaims, workspace model.Workspace, tenantID string) (dto.Member, error)
	Delete(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, memberID uuid.UUID) error
	CreateGuest(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, memberReq dto.PostInviteMemberRequest) (dto.Member, *model.Chat, error)
	Create(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, memberReq dto.PostInviteMemberRequest) (dto.Member, *model.Chat, error)
	FindByWorkspaceIDAndID(ctx context.Context, workspaceID uuid.UUID, requesterID, memberID uuid.UUID, isContainRelationInfo bool) (dto.Member, error)
	FindByWorkspaceIDAndTagNumber(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, tagNumber string, isContainRelationInfo bool) (dto.Member, error)
	UpdateSelf(ctx context.Context, workspaceID uuid.UUID, memberID uuid.UUID, memberReq dto.PutSelfMemberRequest) (dto.Member, error)
	UpdateEmail(ctx context.Context, workspaceID uuid.UUID, memberID uuid.UUID, email string) (dto.Member, *model.Chat, error)
	UpdateIconSource(ctx context.Context, workspaceID uuid.UUID, memberID uuid.UUID, iconSource string) (dto.Member, error)
	HandleFirstSignIn(ctx context.Context, claims *auth.AuthTokenClaims, member *model.Member) (*model.Member, error)
	FindByWorkspaceIDAndRequesterIDandRequesterRoleAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersQueryParams) ([]*dto.Member, bool, error)
	GetDefaultIconURL(ctx context.Context, email string) (string, error)
	MapMemberFormItemsWithWorkspace(ctx context.Context, member *model.Member) error
	UpdateToggleSuspended(ctx context.Context, requeterID uuid.UUID, tenantID string, workspaceID uuid.UUID, memberID uuid.UUID, status string) (dto.Member, *model.Chat, error)
	MapDisplayOnCardMemberFormItemsWithWorkspace(ctx context.Context, member *model.Member) error
	FlexUpdateSelf(ctx context.Context, workspaceID uuid.UUID, requesterID, memberID uuid.UUID, memberReq dto.PatchSelfMemberRequest) (dto.Member, *model.Chat, error)
	FlexibleUpdateOther(ctx context.Context, workspaceID uuid.UUID, requesterID, memberID uuid.UUID, memberReq dto.PatchOtherMemberRequest) (dto.Member, *dto.NotificationData, *model.Chat, error)
	FindByWorkspaceIDAndRequesterIDandRequesterRoleAndQueryParamsViewByManagement(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersBodyViewByManagement) ([]*dto.Member, bool, error)
	FindByWorkspaceIDAndGetTeamBodyRequest(ctx context.Context, workspaceID, requesterID uuid.UUID, requesterRole string, q dto.GetTeamBodyRequest) (*dto.GetTeamResponse, error)
	FindByWorkspaceIDAndIDAndAdditionData(ctx context.Context, workspaceID uuid.UUID, requesterID, memberID uuid.UUID, q dto.GetMemberRequest) (*dto.GetMemberResponse, error)
	FindByWorkspaceIDAndIDAndIncludeGhostMember(ctx context.Context, workspaceID uuid.UUID, memberID uuid.UUID) (*model.Member, error)
	FillIconByMemberIds(ctx context.Context, ids ...uuid.UUID) []*model.Member
	FindByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersByQueryParamsAndFacilitatorAndShareScope) ([]*dto.Member, bool, error)
	GetMemberResponse(ctx context.Context, requesterID, memberID uuid.UUID, displayOnCard bool, allowGhost bool) (dto.Member, error)
	GetMembersResponse(ctx context.Context, requesterID uuid.UUID, memberIDs []uuid.UUID, displayOnCard bool, allowGhost bool) ([]dto.Member, error)
	ValidateUpdatingData(oldMember model.Member, memberReq dto.PatchSelfMemberRequest) error
	GetTargetIDsHasNewMessage(ctx context.Context, requesterID uuid.UUID, targetIDs []uuid.UUID) ([]string, error)
	GetMemberChatInfoByTargetIDs(ctx context.Context, workspaceID, requesterID uuid.UUID, targetIDs []uuid.UUID) (map[string]dto.ChatInfo, error)
	FindInvitingMemberByInvitedCode(ctx context.Context, invitedCode string) (*model.WorkspaceInvite, error)
	SendMailToInvitingMember(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) error
	GetMemberResponseForTaskDetailScreen(ctx context.Context, requesterID, memberID uuid.UUID) (dto.Member, error)
	AddEmail(ctx context.Context, dtoEmail *dto.AddingEmailRequest) (*dto.Member, *model.Chat, error)
	FindTeammate(ctx context.Context, workspaceID, requesterID uuid.UUID, requesterRole string) ([]*dto.Member, error)
	GetChatMemberVisible(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) ([]uuid.UUID, error)
	FindMemberIconsByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersByQueryParamsAndFacilitatorAndShareScope) ([]*dto.MemberIcon, error)
	SuspendMembersWhenChangePlatToBusiness(ctx context.Context, workspaceID uuid.UUID, excludeMemberID uuid.UUID) error
	CanModifyMember(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) error
}
