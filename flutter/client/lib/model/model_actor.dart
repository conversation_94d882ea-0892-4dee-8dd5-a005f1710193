//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelActor {
  /// Returns a new [ModelActor] instance.
  ModelActor({
    this.externalLink,
    this.isUnread,
    this.member,
    this.seatBot,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelExternalLink? externalLink;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isUnread;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelMember? member;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelClipCrowActor? seatBot;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelActor &&
     other.externalLink == externalLink &&
     other.isUnread == isUnread &&
     other.member == member &&
     other.seatBot == seatBot;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (externalLink == null ? 0 : externalLink!.hashCode) +
    (isUnread == null ? 0 : isUnread!.hashCode) +
    (member == null ? 0 : member!.hashCode) +
    (seatBot == null ? 0 : seatBot!.hashCode);

  @override
  String toString() => 'ModelActor[externalLink=$externalLink, isUnread=$isUnread, member=$member, seatBot=$seatBot]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.externalLink != null) {
      json[r'external_link'] = this.externalLink;
    } else {
      json[r'external_link'] = null;
    }
    if (this.isUnread != null) {
      json[r'is_unread'] = this.isUnread;
    } else {
      json[r'is_unread'] = null;
    }
    if (this.member != null) {
      json[r'member'] = this.member;
    } else {
      json[r'member'] = null;
    }
    if (this.seatBot != null) {
      json[r'seat_bot'] = this.seatBot;
    } else {
      json[r'seat_bot'] = null;
    }
    return json;
  }

  /// Returns a new [ModelActor] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelActor? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelActor[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelActor[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelActor(
        externalLink: ModelExternalLink.fromJson(json[r'external_link']),
        isUnread: mapValueOfType<bool>(json, r'is_unread'),
        member: ModelMember.fromJson(json[r'member']),
        seatBot: ModelClipCrowActor.fromJson(json[r'seat_bot']),
      );
    }
    return null;
  }

  static List<ModelActor>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelActor>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelActor.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelActor> mapFromJson(dynamic json) {
    final map = <String, ModelActor>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelActor.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelActor-objects as value to a dart map
  static Map<String, List<ModelActor>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelActor>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelActor.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

