//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class TagGroupsApi {
  TagGroupsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get Tag Group
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<Response> workspacesWorkspaceIdTagGroupsByTagTagNumberGetWithHttpInfo(String workspaceId, String tagNumber,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/by-tag/{tag_number}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_number}', tagNumber);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Tag Group
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<DtoTagGroup?> workspacesWorkspaceIdTagGroupsByTagTagNumberGet(String workspaceId, String tagNumber,) async {
    final response = await workspacesWorkspaceIdTagGroupsByTagTagNumberGetWithHttpInfo(workspaceId, tagNumber,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTagGroup',) as DtoTagGroup;
    
    }
    return null;
  }

  /// Returns a list of tag group color
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdTagGroupsColorGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/color'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of tag group color
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoTagGroupsColorResponse?> workspacesWorkspaceIdTagGroupsColorGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdTagGroupsColorGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTagGroupsColorResponse',) as DtoTagGroupsColorResponse;
    
    }
    return null;
  }

  /// Returns a list of tag group color
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdTagGroupsDefaultGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/default'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of tag group color
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoTagResponse?> workspacesWorkspaceIdTagGroupsDefaultGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdTagGroupsDefaultGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTagResponse',) as DtoTagResponse;
    
    }
    return null;
  }

  /// Returns a list of tag group
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] bookmark:
  ///
  /// * [List<String>] color:
  ///
  /// * [List<String>] displayType:
  ///
  /// * [bool] excludeDefault:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  ///
  /// * [String] sort:
  ///
  /// * [List<String>] usageType:
  Future<Response> workspacesWorkspaceIdTagGroupsGetWithHttpInfo(String workspaceId, { bool? bookmark, List<String>? color, List<String>? displayType, bool? excludeDefault, String? facilitator, bool? onlyUnread, int? page, int? pageSize, String? query, String? sort, List<String>? usageType, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (bookmark != null) {
      queryParams.addAll(_queryParams('', 'bookmark', bookmark));
    }
    if (color != null) {
      queryParams.addAll(_queryParams('csv', 'color', color));
    }
    if (displayType != null) {
      queryParams.addAll(_queryParams('csv', 'display_type', displayType));
    }
    if (excludeDefault != null) {
      queryParams.addAll(_queryParams('', 'exclude_default', excludeDefault));
    }
    if (facilitator != null) {
      queryParams.addAll(_queryParams('', 'facilitator', facilitator));
    }
    if (onlyUnread != null) {
      queryParams.addAll(_queryParams('', 'only_unread', onlyUnread));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }
    if (usageType != null) {
      queryParams.addAll(_queryParams('csv', 'usage_type', usageType));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of tag group
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] bookmark:
  ///
  /// * [List<String>] color:
  ///
  /// * [List<String>] displayType:
  ///
  /// * [bool] excludeDefault:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  ///
  /// * [String] sort:
  ///
  /// * [List<String>] usageType:
  Future<DtoGetTagGroupsResponse?> workspacesWorkspaceIdTagGroupsGet(String workspaceId, { bool? bookmark, List<String>? color, List<String>? displayType, bool? excludeDefault, String? facilitator, bool? onlyUnread, int? page, int? pageSize, String? query, String? sort, List<String>? usageType, }) async {
    final response = await workspacesWorkspaceIdTagGroupsGetWithHttpInfo(workspaceId,  bookmark: bookmark, color: color, displayType: displayType, excludeDefault: excludeDefault, facilitator: facilitator, onlyUnread: onlyUnread, page: page, pageSize: pageSize, query: query, sort: sort, usageType: usageType, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetTagGroupsResponse',) as DtoGetTagGroupsResponse;
    
    }
    return null;
  }

  /// Create Tag Group
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostTagGroupRequest] body (required):
  ///   TagGroup object
  Future<Response> workspacesWorkspaceIdTagGroupsPostWithHttpInfo(String workspaceId, DtoPostTagGroupRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Tag Group
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostTagGroupRequest] body (required):
  ///   TagGroup object
  Future<DtoTagGroup?> workspacesWorkspaceIdTagGroupsPost(String workspaceId, DtoPostTagGroupRequest body,) async {
    final response = await workspacesWorkspaceIdTagGroupsPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTagGroup',) as DtoTagGroup;
    
    }
    return null;
  }

  /// Delete Background
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag Group ID
  Future<Response> workspacesWorkspaceIdTagGroupsTagGroupIdBackgroundSignedUrlDeleteWithHttpInfo(String workspaceId, String tagGroupId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/{tag_group_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_group_id}', tagGroupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Background
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag Group ID
  Future<void> workspacesWorkspaceIdTagGroupsTagGroupIdBackgroundSignedUrlDelete(String workspaceId, String tagGroupId,) async {
    final response = await workspacesWorkspaceIdTagGroupsTagGroupIdBackgroundSignedUrlDeleteWithHttpInfo(workspaceId, tagGroupId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create Background signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag group ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<Response> workspacesWorkspaceIdTagGroupsTagGroupIdBackgroundSignedUrlPostWithHttpInfo(String workspaceId, String tagGroupId, String method,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/{tag_group_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_group_id}', tagGroupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Background signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag group ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdTagGroupsTagGroupIdBackgroundSignedUrlPost(String workspaceId, String tagGroupId, String method,) async {
    final response = await workspacesWorkspaceIdTagGroupsTagGroupIdBackgroundSignedUrlPostWithHttpInfo(workspaceId, tagGroupId, method,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Delete Tag Group
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag Group ID
  Future<Response> workspacesWorkspaceIdTagGroupsTagGroupIdDeleteWithHttpInfo(String workspaceId, String tagGroupId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/{tag_group_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_group_id}', tagGroupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Tag Group
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag Group ID
  Future<void> workspacesWorkspaceIdTagGroupsTagGroupIdDelete(String workspaceId, String tagGroupId,) async {
    final response = await workspacesWorkspaceIdTagGroupsTagGroupIdDeleteWithHttpInfo(workspaceId, tagGroupId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get Tag Group
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag group ID
  Future<Response> workspacesWorkspaceIdTagGroupsTagGroupIdGetWithHttpInfo(String workspaceId, String tagGroupId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/{tag_group_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_group_id}', tagGroupId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Tag Group
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag group ID
  Future<DtoTagGroup?> workspacesWorkspaceIdTagGroupsTagGroupIdGet(String workspaceId, String tagGroupId,) async {
    final response = await workspacesWorkspaceIdTagGroupsTagGroupIdGetWithHttpInfo(workspaceId, tagGroupId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTagGroup',) as DtoTagGroup;
    
    }
    return null;
  }

  /// Patch Tag Group
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag Group ID
  ///
  /// * [DtoPatchTagGroupRequest] body (required):
  ///   TagGroup object
  Future<Response> workspacesWorkspaceIdTagGroupsTagGroupIdPatchWithHttpInfo(String workspaceId, String tagGroupId, DtoPatchTagGroupRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tag-groups/{tag_group_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_group_id}', tagGroupId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Patch Tag Group
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagGroupId (required):
  ///   Tag Group ID
  ///
  /// * [DtoPatchTagGroupRequest] body (required):
  ///   TagGroup object
  Future<DtoTagGroup?> workspacesWorkspaceIdTagGroupsTagGroupIdPatch(String workspaceId, String tagGroupId, DtoPatchTagGroupRequest body,) async {
    final response = await workspacesWorkspaceIdTagGroupsTagGroupIdPatchWithHttpInfo(workspaceId, tagGroupId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTagGroup',) as DtoTagGroup;
    
    }
    return null;
  }

  /// Get Tag
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagId (required):
  ///   Tag ID
  Future<Response> workspacesWorkspaceIdTagsTagIdGetWithHttpInfo(String workspaceId, String tagId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/tags/{tag_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_id}', tagId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Tag
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagId (required):
  ///   Tag ID
  Future<DtoTag?> workspacesWorkspaceIdTagsTagIdGet(String workspaceId, String tagId,) async {
    final response = await workspacesWorkspaceIdTagsTagIdGetWithHttpInfo(workspaceId, tagId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTag',) as DtoTag;
    
    }
    return null;
  }
}
