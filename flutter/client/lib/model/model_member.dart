//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelMember {
  /// Returns a new [ModelMember] instance.
  ModelMember({
    this.background,
    this.backgroundKey,
    this.backgroundText,
    required this.createdAt,
    this.deletedAt,
    this.email,
    this.icon,
    this.iconKey,
    required this.id,
    this.illustration,
    this.invitedAt,
    this.inviterId,
    this.memberFormItems = const [],
    this.name,
    this.opacity,
    this.optionalFormItems = const [],
    this.primaryColor,
    this.reverseBackground,
    this.role,
    this.secondaryColor,
    this.status,
    this.tagNumber,
    this.tags = const [],
    this.textAlignment,
    this.textColor,
    required this.updatedAt,
    required this.workspaceId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundText;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? email;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? icon;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? iconKey;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? invitedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? inviterId;

  List<ModelMemberFormItem> memberFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  List<ModelFormItem> optionalFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ModelMemberRoleEnum? role;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  ModelMemberStatusEnum? status;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  List<ModelTag> tags;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  ModelMemberTextColorEnum? textColor;

  String updatedAt;

  String workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelMember &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.backgroundText == backgroundText &&
     other.createdAt == createdAt &&
     other.deletedAt == deletedAt &&
     other.email == email &&
     other.icon == icon &&
     other.iconKey == iconKey &&
     other.id == id &&
     other.illustration == illustration &&
     other.invitedAt == invitedAt &&
     other.inviterId == inviterId &&
     other.memberFormItems == memberFormItems &&
     other.name == name &&
     other.opacity == opacity &&
     other.optionalFormItems == optionalFormItems &&
     other.primaryColor == primaryColor &&
     other.reverseBackground == reverseBackground &&
     other.role == role &&
     other.secondaryColor == secondaryColor &&
     other.status == status &&
     other.tagNumber == tagNumber &&
     other.tags == tags &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.updatedAt == updatedAt &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (backgroundText == null ? 0 : backgroundText!.hashCode) +
    (createdAt.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (email == null ? 0 : email!.hashCode) +
    (icon == null ? 0 : icon!.hashCode) +
    (iconKey == null ? 0 : iconKey!.hashCode) +
    (id.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (invitedAt == null ? 0 : invitedAt!.hashCode) +
    (inviterId == null ? 0 : inviterId!.hashCode) +
    (memberFormItems.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (optionalFormItems.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (role == null ? 0 : role!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (tags.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (updatedAt.hashCode) +
    (workspaceId.hashCode);

  @override
  String toString() => 'ModelMember[background=$background, backgroundKey=$backgroundKey, backgroundText=$backgroundText, createdAt=$createdAt, deletedAt=$deletedAt, email=$email, icon=$icon, iconKey=$iconKey, id=$id, illustration=$illustration, invitedAt=$invitedAt, inviterId=$inviterId, memberFormItems=$memberFormItems, name=$name, opacity=$opacity, optionalFormItems=$optionalFormItems, primaryColor=$primaryColor, reverseBackground=$reverseBackground, role=$role, secondaryColor=$secondaryColor, status=$status, tagNumber=$tagNumber, tags=$tags, textAlignment=$textAlignment, textColor=$textColor, updatedAt=$updatedAt, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
    if (this.backgroundText != null) {
      json[r'background_text'] = this.backgroundText;
    } else {
      json[r'background_text'] = null;
    }
      json[r'created_at'] = this.createdAt;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.email != null) {
      json[r'email'] = this.email;
    } else {
      json[r'email'] = null;
    }
    if (this.icon != null) {
      json[r'icon'] = this.icon;
    } else {
      json[r'icon'] = null;
    }
    if (this.iconKey != null) {
      json[r'icon_key'] = this.iconKey;
    } else {
      json[r'icon_key'] = null;
    }
      json[r'id'] = this.id;
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
    if (this.invitedAt != null) {
      json[r'invited_at'] = this.invitedAt;
    } else {
      json[r'invited_at'] = null;
    }
    if (this.inviterId != null) {
      json[r'inviter_id'] = this.inviterId;
    } else {
      json[r'inviter_id'] = null;
    }
      json[r'member_form_items'] = this.memberFormItems;
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
      json[r'optional_form_items'] = this.optionalFormItems;
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.role != null) {
      json[r'role'] = this.role;
    } else {
      json[r'role'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
      json[r'tags'] = this.tags;
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
      json[r'workspace_id'] = this.workspaceId;
    return json;
  }

  /// Returns a new [ModelMember] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelMember? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelMember[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelMember[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelMember(
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        backgroundText: mapValueOfType<String>(json, r'background_text'),
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        email: mapValueOfType<String>(json, r'email'),
        icon: mapValueOfType<String>(json, r'icon'),
        iconKey: mapValueOfType<String>(json, r'icon_key'),
        id: mapValueOfType<String>(json, r'id')!,
        illustration: mapValueOfType<String>(json, r'illustration'),
        invitedAt: mapValueOfType<String>(json, r'invited_at'),
        inviterId: mapValueOfType<String>(json, r'inviter_id'),
        memberFormItems: ModelMemberFormItem.listFromJson(json[r'member_form_items']) ?? const [],
        name: mapValueOfType<String>(json, r'name'),
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        optionalFormItems: ModelFormItem.listFromJson(json[r'optional_form_items']) ?? const [],
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        role: ModelMemberRoleEnum.fromJson(json[r'role']),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        status: ModelMemberStatusEnum.fromJson(json[r'status']),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        tags: ModelTag.listFromJson(json[r'tags']) ?? const [],
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: ModelMemberTextColorEnum.fromJson(json[r'text_color']),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        workspaceId: mapValueOfType<String>(json, r'workspace_id')!,
      );
    }
    return null;
  }

  static List<ModelMember>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMember>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMember.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelMember> mapFromJson(dynamic json) {
    final map = <String, ModelMember>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMember.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelMember-objects as value to a dart map
  static Map<String, List<ModelMember>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelMember>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMember.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'id',
    'updated_at',
    'workspace_id',
  };
}


class ModelMemberRoleEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelMemberRoleEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const MANAGER = ModelMemberRoleEnum._(r'MANAGER');
  static const STAFF = ModelMemberRoleEnum._(r'STAFF');
  static const GUEST = ModelMemberRoleEnum._(r'GUEST');

  /// List of all possible values in this [enum][ModelMemberRoleEnum].
  static const values = <ModelMemberRoleEnum>[
    MANAGER,
    STAFF,
    GUEST,
  ];

  static ModelMemberRoleEnum? fromJson(dynamic value) => ModelMemberRoleEnumTypeTransformer().decode(value);

  static List<ModelMemberRoleEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMemberRoleEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMemberRoleEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelMemberRoleEnum] to String,
/// and [decode] dynamic data back to [ModelMemberRoleEnum].
class ModelMemberRoleEnumTypeTransformer {
  factory ModelMemberRoleEnumTypeTransformer() => _instance ??= const ModelMemberRoleEnumTypeTransformer._();

  const ModelMemberRoleEnumTypeTransformer._();

  String encode(ModelMemberRoleEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelMemberRoleEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelMemberRoleEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'MANAGER': return ModelMemberRoleEnum.MANAGER;
        case r'STAFF': return ModelMemberRoleEnum.STAFF;
        case r'GUEST': return ModelMemberRoleEnum.GUEST;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelMemberRoleEnumTypeTransformer] instance.
  static ModelMemberRoleEnumTypeTransformer? _instance;
}



class ModelMemberStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelMemberStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const ACTIVE = ModelMemberStatusEnum._(r'ACTIVE');
  static const INVITING = ModelMemberStatusEnum._(r'INVITING');
  static const SUSPENDED = ModelMemberStatusEnum._(r'SUSPENDED');

  /// List of all possible values in this [enum][ModelMemberStatusEnum].
  static const values = <ModelMemberStatusEnum>[
    ACTIVE,
    INVITING,
    SUSPENDED,
  ];

  static ModelMemberStatusEnum? fromJson(dynamic value) => ModelMemberStatusEnumTypeTransformer().decode(value);

  static List<ModelMemberStatusEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMemberStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMemberStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelMemberStatusEnum] to String,
/// and [decode] dynamic data back to [ModelMemberStatusEnum].
class ModelMemberStatusEnumTypeTransformer {
  factory ModelMemberStatusEnumTypeTransformer() => _instance ??= const ModelMemberStatusEnumTypeTransformer._();

  const ModelMemberStatusEnumTypeTransformer._();

  String encode(ModelMemberStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelMemberStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelMemberStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'ACTIVE': return ModelMemberStatusEnum.ACTIVE;
        case r'INVITING': return ModelMemberStatusEnum.INVITING;
        case r'SUSPENDED': return ModelMemberStatusEnum.SUSPENDED;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelMemberStatusEnumTypeTransformer] instance.
  static ModelMemberStatusEnumTypeTransformer? _instance;
}



class ModelMemberTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelMemberTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = ModelMemberTextColorEnum._(r'DARK');
  static const LIGHT = ModelMemberTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][ModelMemberTextColorEnum].
  static const values = <ModelMemberTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static ModelMemberTextColorEnum? fromJson(dynamic value) => ModelMemberTextColorEnumTypeTransformer().decode(value);

  static List<ModelMemberTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMemberTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMemberTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelMemberTextColorEnum] to String,
/// and [decode] dynamic data back to [ModelMemberTextColorEnum].
class ModelMemberTextColorEnumTypeTransformer {
  factory ModelMemberTextColorEnumTypeTransformer() => _instance ??= const ModelMemberTextColorEnumTypeTransformer._();

  const ModelMemberTextColorEnumTypeTransformer._();

  String encode(ModelMemberTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelMemberTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelMemberTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return ModelMemberTextColorEnum.DARK;
        case r'LIGHT': return ModelMemberTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelMemberTextColorEnumTypeTransformer] instance.
  static ModelMemberTextColorEnumTypeTransformer? _instance;
}


