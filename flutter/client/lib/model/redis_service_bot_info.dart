//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class RedisServiceBotInfo {
  /// Returns a new [RedisServiceBotInfo] instance.
  RedisServiceBotInfo({
    this.botId,
    this.botName,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? botId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? botName;

  @override
  bool operator ==(Object other) => identical(this, other) || other is RedisServiceBotInfo &&
     other.botId == botId &&
     other.botName == botName;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (botId == null ? 0 : botId!.hashCode) +
    (botName == null ? 0 : botName!.hashCode);

  @override
  String toString() => 'RedisServiceBotInfo[botId=$botId, botName=$botName]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.botId != null) {
      json[r'bot_id'] = this.botId;
    } else {
      json[r'bot_id'] = null;
    }
    if (this.botName != null) {
      json[r'bot_name'] = this.botName;
    } else {
      json[r'bot_name'] = null;
    }
    return json;
  }

  /// Returns a new [RedisServiceBotInfo] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static RedisServiceBotInfo? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "RedisServiceBotInfo[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "RedisServiceBotInfo[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return RedisServiceBotInfo(
        botId: mapValueOfType<String>(json, r'bot_id'),
        botName: mapValueOfType<String>(json, r'bot_name'),
      );
    }
    return null;
  }

  static List<RedisServiceBotInfo>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <RedisServiceBotInfo>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = RedisServiceBotInfo.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, RedisServiceBotInfo> mapFromJson(dynamic json) {
    final map = <String, RedisServiceBotInfo>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = RedisServiceBotInfo.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of RedisServiceBotInfo-objects as value to a dart map
  static Map<String, List<RedisServiceBotInfo>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<RedisServiceBotInfo>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = RedisServiceBotInfo.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

