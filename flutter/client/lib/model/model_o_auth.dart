//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelOAuth {
  /// Returns a new [ModelOAuth] instance.
  ModelOAuth({
    required this.createdAt,
    this.creatorId,
    this.deletedAt,
    required this.id,
    this.isActive,
    this.scopes = const [],
    required this.token,
    required this.updatedAt,
    required this.workspaceId,
  });

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? creatorId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isActive;

  List<String> scopes;

  String token;

  String updatedAt;

  String workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelOAuth &&
     other.createdAt == createdAt &&
     other.creatorId == creatorId &&
     other.deletedAt == deletedAt &&
     other.id == id &&
     other.isActive == isActive &&
     other.scopes == scopes &&
     other.token == token &&
     other.updatedAt == updatedAt &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (createdAt.hashCode) +
    (creatorId == null ? 0 : creatorId!.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (id.hashCode) +
    (isActive == null ? 0 : isActive!.hashCode) +
    (scopes.hashCode) +
    (token.hashCode) +
    (updatedAt.hashCode) +
    (workspaceId.hashCode);

  @override
  String toString() => 'ModelOAuth[createdAt=$createdAt, creatorId=$creatorId, deletedAt=$deletedAt, id=$id, isActive=$isActive, scopes=$scopes, token=$token, updatedAt=$updatedAt, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'created_at'] = this.createdAt;
    if (this.creatorId != null) {
      json[r'creator_id'] = this.creatorId;
    } else {
      json[r'creator_id'] = null;
    }
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'id'] = this.id;
    if (this.isActive != null) {
      json[r'is_active'] = this.isActive;
    } else {
      json[r'is_active'] = null;
    }
      json[r'scopes'] = this.scopes;
      json[r'token'] = this.token;
      json[r'updated_at'] = this.updatedAt;
      json[r'workspace_id'] = this.workspaceId;
    return json;
  }

  /// Returns a new [ModelOAuth] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelOAuth? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelOAuth[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelOAuth[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelOAuth(
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        creatorId: mapValueOfType<String>(json, r'creator_id'),
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        id: mapValueOfType<String>(json, r'id')!,
        isActive: mapValueOfType<bool>(json, r'is_active'),
        scopes: json[r'scopes'] is List
            ? (json[r'scopes'] as List).cast<String>()
            : const [],
        token: mapValueOfType<String>(json, r'token')!,
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        workspaceId: mapValueOfType<String>(json, r'workspace_id')!,
      );
    }
    return null;
  }

  static List<ModelOAuth>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelOAuth>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelOAuth.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelOAuth> mapFromJson(dynamic json) {
    final map = <String, ModelOAuth>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelOAuth.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelOAuth-objects as value to a dart map
  static Map<String, List<ModelOAuth>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelOAuth>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelOAuth.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'id',
    'scopes',
    'token',
    'updated_at',
    'workspace_id',
  };
}

