//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoUpdatePICRequest {
  /// Returns a new [DtoUpdatePICRequest] instance.
  DtoUpdatePICRequest({
    this.isRemove,
    this.picId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isRemove;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? picId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoUpdatePICRequest &&
     other.isRemove == isRemove &&
     other.picId == picId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (isRemove == null ? 0 : isRemove!.hashCode) +
    (picId == null ? 0 : picId!.hashCode);

  @override
  String toString() => 'DtoUpdatePICRequest[isRemove=$isRemove, picId=$picId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.isRemove != null) {
      json[r'is_remove'] = this.isRemove;
    } else {
      json[r'is_remove'] = null;
    }
    if (this.picId != null) {
      json[r'pic_id'] = this.picId;
    } else {
      json[r'pic_id'] = null;
    }
    return json;
  }

  /// Returns a new [DtoUpdatePICRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoUpdatePICRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoUpdatePICRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoUpdatePICRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoUpdatePICRequest(
        isRemove: mapValueOfType<bool>(json, r'is_remove'),
        picId: mapValueOfType<String>(json, r'pic_id'),
      );
    }
    return null;
  }

  static List<DtoUpdatePICRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoUpdatePICRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoUpdatePICRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoUpdatePICRequest> mapFromJson(dynamic json) {
    final map = <String, DtoUpdatePICRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoUpdatePICRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoUpdatePICRequest-objects as value to a dart map
  static Map<String, List<DtoUpdatePICRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoUpdatePICRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoUpdatePICRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

