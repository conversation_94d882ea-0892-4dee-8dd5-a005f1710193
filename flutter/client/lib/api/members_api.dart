//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class MembersApi {
  MembersApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// add member's email
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoAddingEmailRequest] body (required):
  ///   Adding email request
  Future<Response> updateMemberEmailPutWithHttpInfo(DtoAddingEmailRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/update-member-email';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// add member's email
  ///
  /// Parameters:
  ///
  /// * [DtoAddingEmailRequest] body (required):
  ///   Adding email request
  Future<void> updateMemberEmailPut(DtoAddingEmailRequest body,) async {
    final response = await updateMemberEmailPutWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get Member
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<Response> workspacesWorkspaceIdMembersByTagTagNumberGetWithHttpInfo(String workspaceId, String tagNumber,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/by-tag/{tag_number}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_number}', tagNumber);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Member
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<DtoMember?> workspacesWorkspaceIdMembersByTagTagNumberGet(String workspaceId, String tagNumber,) async {
    final response = await workspacesWorkspaceIdMembersByTagTagNumberGetWithHttpInfo(workspaceId, tagNumber,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Returns a list of members
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] ignoreCurrentUser:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  ///
  /// * [List<String>] roleTags:
  ///
  /// * [String] sort:
  ///
  /// * [List<String>] statusTags:
  ///
  /// * [List<String>] tagIds:
  Future<Response> workspacesWorkspaceIdMembersGetWithHttpInfo(String workspaceId, { bool? ignoreCurrentUser, int? page, int? pageSize, String? query, List<String>? roleTags, String? sort, List<String>? statusTags, List<String>? tagIds, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (ignoreCurrentUser != null) {
      queryParams.addAll(_queryParams('', 'ignore_current_user', ignoreCurrentUser));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
    if (roleTags != null) {
      queryParams.addAll(_queryParams('csv', 'role_tags', roleTags));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }
    if (statusTags != null) {
      queryParams.addAll(_queryParams('csv', 'status_tags', statusTags));
    }
    if (tagIds != null) {
      queryParams.addAll(_queryParams('csv', 'tag_ids', tagIds));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of members
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] ignoreCurrentUser:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  ///
  /// * [List<String>] roleTags:
  ///
  /// * [String] sort:
  ///
  /// * [List<String>] statusTags:
  ///
  /// * [List<String>] tagIds:
  Future<DtoGetMembersResponse?> workspacesWorkspaceIdMembersGet(String workspaceId, { bool? ignoreCurrentUser, int? page, int? pageSize, String? query, List<String>? roleTags, String? sort, List<String>? statusTags, List<String>? tagIds, }) async {
    final response = await workspacesWorkspaceIdMembersGetWithHttpInfo(workspaceId,  ignoreCurrentUser: ignoreCurrentUser, page: page, pageSize: pageSize, query: query, roleTags: roleTags, sort: sort, statusTags: statusTags, tagIds: tagIds, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMembersResponse',) as DtoGetMembersResponse;
    
    }
    return null;
  }

  /// Delete Background
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdBackgroundSignedUrlDeleteWithHttpInfo(String workspaceId, String memberId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Background
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<void> workspacesWorkspaceIdMembersMemberIdBackgroundSignedUrlDelete(String workspaceId, String memberId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdBackgroundSignedUrlDeleteWithHttpInfo(workspaceId, memberId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create Background signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<Response> workspacesWorkspaceIdMembersMemberIdBackgroundSignedUrlPostWithHttpInfo(String workspaceId, String memberId, String method,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Background signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdMembersMemberIdBackgroundSignedUrlPost(String workspaceId, String memberId, String method,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdBackgroundSignedUrlPostWithHttpInfo(workspaceId, memberId, method,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Get Member default icon URL
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdDefaultIconUrlGetWithHttpInfo(String workspaceId, String memberId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/default-icon-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Member default icon URL
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<DtoGetMemberDefaultIconURLResponse?> workspacesWorkspaceIdMembersMemberIdDefaultIconUrlGet(String workspaceId, String memberId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdDefaultIconUrlGetWithHttpInfo(workspaceId, memberId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMemberDefaultIconURLResponse',) as DtoGetMemberDefaultIconURLResponse;
    
    }
    return null;
  }

  /// Delete Member of Workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdDeleteWithHttpInfo(String workspaceId, String memberId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Member of Workspace
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<void> workspacesWorkspaceIdMembersMemberIdDelete(String workspaceId, String memberId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdDeleteWithHttpInfo(workspaceId, memberId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Update member's email
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoUpdateEmailRequest] body (required):
  ///   Body request update email member
  Future<Response> workspacesWorkspaceIdMembersMemberIdEmailPutWithHttpInfo(String workspaceId, String memberId, DtoUpdateEmailRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/email'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update member's email
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoUpdateEmailRequest] body (required):
  ///   Body request update email member
  Future<DtoMember?> workspacesWorkspaceIdMembersMemberIdEmailPut(String workspaceId, String memberId, DtoUpdateEmailRequest body,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdEmailPutWithHttpInfo(workspaceId, memberId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Get Member
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdGetWithHttpInfo(String workspaceId, String memberId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Member
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<DtoMember?> workspacesWorkspaceIdMembersMemberIdGet(String workspaceId, String memberId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdGetWithHttpInfo(workspaceId, memberId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Delete Icon
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<Response> workspacesWorkspaceIdMembersMemberIdIconSignedUrlDeleteWithHttpInfo(String workspaceId, String memberId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/icon-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Icon
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  Future<void> workspacesWorkspaceIdMembersMemberIdIconSignedUrlDelete(String workspaceId, String memberId,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdIconSignedUrlDeleteWithHttpInfo(workspaceId, memberId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create icon signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [String] size:
  ///   Size
  Future<Response> workspacesWorkspaceIdMembersMemberIdIconSignedUrlPostWithHttpInfo(String workspaceId, String memberId, String method, { String? size, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/icon-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));
    if (size != null) {
      queryParams.addAll(_queryParams('', 'size', size));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create icon signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [String] size:
  ///   Size
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdMembersMemberIdIconSignedUrlPost(String workspaceId, String memberId, String method, { String? size, }) async {
    final response = await workspacesWorkspaceIdMembersMemberIdIconSignedUrlPostWithHttpInfo(workspaceId, memberId, method,  size: size, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Update member icon source
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPutMemberIconSourceRequest] body (required):
  ///   Icon source
  Future<Response> workspacesWorkspaceIdMembersMemberIdIconSourcePutWithHttpInfo(String workspaceId, String memberId, DtoPutMemberIconSourceRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/icon-source'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update member icon source
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPutMemberIconSourceRequest] body (required):
  ///   Icon source
  Future<DtoMember?> workspacesWorkspaceIdMembersMemberIdIconSourcePut(String workspaceId, String memberId, DtoPutMemberIconSourceRequest body,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdIconSourcePutWithHttpInfo(workspaceId, memberId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Update Member Idle
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPutMemberIdleRequest] body (required):
  ///   Member Update object
  Future<Response> workspacesWorkspaceIdMembersMemberIdIdlePutWithHttpInfo(String workspaceId, String memberId, DtoPutMemberIdleRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/idle'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Member Idle
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPutMemberIdleRequest] body (required):
  ///   Member Update object
  Future<DtoMember?> workspacesWorkspaceIdMembersMemberIdIdlePut(String workspaceId, String memberId, DtoPutMemberIdleRequest body,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdIdlePutWithHttpInfo(workspaceId, memberId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Get Member with Main and Sub
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] mainMemberId:
  ///
  /// * [String] subMemberId:
  Future<Response> workspacesWorkspaceIdMembersMemberIdInfoGetWithHttpInfo(String workspaceId, String memberId, { String? mainMemberId, String? subMemberId, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/info'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (mainMemberId != null) {
      queryParams.addAll(_queryParams('', 'main_member_id', mainMemberId));
    }
    if (subMemberId != null) {
      queryParams.addAll(_queryParams('', 'sub_member_id', subMemberId));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Member with Main and Sub
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [String] mainMemberId:
  ///
  /// * [String] subMemberId:
  Future<DtoGetMemberResponse?> workspacesWorkspaceIdMembersMemberIdInfoGet(String workspaceId, String memberId, { String? mainMemberId, String? subMemberId, }) async {
    final response = await workspacesWorkspaceIdMembersMemberIdInfoGetWithHttpInfo(workspaceId, memberId,  mainMemberId: mainMemberId, subMemberId: subMemberId, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMemberResponse',) as DtoGetMemberResponse;
    
    }
    return null;
  }

  /// Delete Member's notification token
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] memberId (required):
  ///   memberID
  ///
  /// * [DtoNotificationTokenRequestForDelete] body (required):
  ///   NotificationTokenReq object
  Future<Response> workspacesWorkspaceIdMembersMemberIdNotificationTokenDeleteWithHttpInfo(String workspaceId, String memberId, DtoNotificationTokenRequestForDelete body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/notification-token'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Member's notification token
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] memberId (required):
  ///   memberID
  ///
  /// * [DtoNotificationTokenRequestForDelete] body (required):
  ///   NotificationTokenReq object
  Future<void> workspacesWorkspaceIdMembersMemberIdNotificationTokenDelete(String workspaceId, String memberId, DtoNotificationTokenRequestForDelete body,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdNotificationTokenDeleteWithHttpInfo(workspaceId, memberId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Update Member's notification token
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] memberId (required):
  ///   memberID
  ///
  /// * [DtoNotificationTokenRequest] body (required):
  ///   NotificationTokenReq object
  Future<Response> workspacesWorkspaceIdMembersMemberIdNotificationTokenPutWithHttpInfo(String workspaceId, String memberId, DtoNotificationTokenRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}/notification-token'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Member's notification token
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] memberId (required):
  ///   memberID
  ///
  /// * [DtoNotificationTokenRequest] body (required):
  ///   NotificationTokenReq object
  Future<void> workspacesWorkspaceIdMembersMemberIdNotificationTokenPut(String workspaceId, String memberId, DtoNotificationTokenRequest body,) async {
    final response = await workspacesWorkspaceIdMembersMemberIdNotificationTokenPutWithHttpInfo(workspaceId, memberId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Update member
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPatchMemberRequest] body:
  ///   Member Update object
  Future<Response> workspacesWorkspaceIdMembersMemberIdPatchWithHttpInfo(String workspaceId, String memberId, { DtoPatchMemberRequest? body, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/{member_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update member
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] memberId (required):
  ///   Member ID
  ///
  /// * [DtoPatchMemberRequest] body:
  ///   Member Update object
  Future<DtoMember?> workspacesWorkspaceIdMembersMemberIdPatch(String workspaceId, String memberId, { DtoPatchMemberRequest? body, }) async {
    final response = await workspacesWorkspaceIdMembersMemberIdPatchWithHttpInfo(workspaceId, memberId,  body: body, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Returns a list of members
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetMembersByQueryParamsAndFacilitatorAndShareScope] body (required):
  ///   Members body view manager request
  Future<Response> workspacesWorkspaceIdMembersPicMentionPostWithHttpInfo(String workspaceId, DtoGetMembersByQueryParamsAndFacilitatorAndShareScope body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/pic-mention'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of members
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetMembersByQueryParamsAndFacilitatorAndShareScope] body (required):
  ///   Members body view manager request
  Future<DtoGetMembersResponse?> workspacesWorkspaceIdMembersPicMentionPost(String workspaceId, DtoGetMembersByQueryParamsAndFacilitatorAndShareScope body,) async {
    final response = await workspacesWorkspaceIdMembersPicMentionPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMembersResponse',) as DtoGetMembersResponse;
    
    }
    return null;
  }

  /// Inviting member to workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostInviteMemberRequest] body (required):
  ///   MemberReq object
  Future<Response> workspacesWorkspaceIdMembersPostWithHttpInfo(String workspaceId, DtoPostInviteMemberRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Inviting member to workspace
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostInviteMemberRequest] body (required):
  ///   MemberReq object
  Future<DtoMember?> workspacesWorkspaceIdMembersPost(String workspaceId, DtoPostInviteMemberRequest body,) async {
    final response = await workspacesWorkspaceIdMembersPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMember',) as DtoMember;
    
    }
    return null;
  }

  /// Returns a list of members
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetMembersBodyViewByManagement] body (required):
  ///   Members body view manager request
  Future<Response> workspacesWorkspaceIdMembersQueryPostWithHttpInfo(String workspaceId, DtoGetMembersBodyViewByManagement body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/query'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of members
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetMembersBodyViewByManagement] body (required):
  ///   Members body view manager request
  Future<DtoGetMembersManagementResponse?> workspacesWorkspaceIdMembersQueryPost(String workspaceId, DtoGetMembersBodyViewByManagement body,) async {
    final response = await workspacesWorkspaceIdMembersQueryPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMembersManagementResponse',) as DtoGetMembersManagementResponse;
    
    }
    return null;
  }

  /// Returns a team
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetTeamBodyRequest] body (required):
  ///   Members request
  Future<Response> workspacesWorkspaceIdMembersTeamPostWithHttpInfo(String workspaceId, DtoGetTeamBodyRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/team'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a team
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetTeamBodyRequest] body (required):
  ///   Members request
  Future<DtoGetTeamResponse?> workspacesWorkspaceIdMembersTeamPost(String workspaceId, DtoGetTeamBodyRequest body,) async {
    final response = await workspacesWorkspaceIdMembersTeamPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetTeamResponse',) as DtoGetTeamResponse;
    
    }
    return null;
  }

  /// Returns visible members
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdMembersVisibleGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/members/visible'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns visible members
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoGetMembersResponse?> workspacesWorkspaceIdMembersVisibleGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdMembersVisibleGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMembersResponse',) as DtoGetMembersResponse;
    
    }
    return null;
  }
}
