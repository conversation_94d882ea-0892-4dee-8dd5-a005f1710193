//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoWorkspaceResponse {
  /// Returns a new [DtoWorkspaceResponse] instance.
  DtoWorkspaceResponse({
    this.background,
    this.backgroundKey,
    this.chatInfo,
    this.commonReactions = const [],
    required this.createdAt,
    this.databases = const [],
    this.dateChangeTime,
    this.description,
    this.disabledFeatures = const [],
    this.facilitator,
    this.formItems = const [],
    this.hostedInvoiceUrl,
    this.icon,
    this.iconKey,
    required this.id,
    this.illustration,
    required this.name,
    this.numberOfActualMonths,
    this.numberOfIntendedMonths,
    this.numberSeats,
    this.opacity,
    this.paymentMetadata,
    this.pricingPlan,
    this.primaryColor,
    required this.primaryLanguage,
    this.quickDeletable,
    this.reverseBackground,
    this.secondaryColor,
    required this.tenantId,
    this.textAlignment,
    this.textColor,
    this.timezone,
    required this.updatedAt,
    this.workspaceFormItems = const [],
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoChatInfo? chatInfo;

  List<String> commonReactions;

  String createdAt;

  List<DtoDatabaseResponse> databases;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? dateChangeTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  List<String> disabledFeatures;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoFacilitatorResponse? facilitator;

  List<ModelFormItem> formItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? hostedInvoiceUrl;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? icon;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? iconKey;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  String name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberOfActualMonths;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberOfIntendedMonths;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? numberSeats;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? paymentMetadata;

  DtoWorkspaceResponsePricingPlanEnum? pricingPlan;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  DtoWorkspaceResponsePrimaryLanguageEnum primaryLanguage;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? quickDeletable;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  String tenantId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  DtoWorkspaceResponseTextColorEnum? textColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? timezone;

  String updatedAt;

  List<ModelFormItem> workspaceFormItems;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoWorkspaceResponse &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.chatInfo == chatInfo &&
     other.commonReactions == commonReactions &&
     other.createdAt == createdAt &&
     other.databases == databases &&
     other.dateChangeTime == dateChangeTime &&
     other.description == description &&
     other.disabledFeatures == disabledFeatures &&
     other.facilitator == facilitator &&
     other.formItems == formItems &&
     other.hostedInvoiceUrl == hostedInvoiceUrl &&
     other.icon == icon &&
     other.iconKey == iconKey &&
     other.id == id &&
     other.illustration == illustration &&
     other.name == name &&
     other.numberOfActualMonths == numberOfActualMonths &&
     other.numberOfIntendedMonths == numberOfIntendedMonths &&
     other.numberSeats == numberSeats &&
     other.opacity == opacity &&
     other.paymentMetadata == paymentMetadata &&
     other.pricingPlan == pricingPlan &&
     other.primaryColor == primaryColor &&
     other.primaryLanguage == primaryLanguage &&
     other.quickDeletable == quickDeletable &&
     other.reverseBackground == reverseBackground &&
     other.secondaryColor == secondaryColor &&
     other.tenantId == tenantId &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.timezone == timezone &&
     other.updatedAt == updatedAt &&
     other.workspaceFormItems == workspaceFormItems;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (chatInfo == null ? 0 : chatInfo!.hashCode) +
    (commonReactions.hashCode) +
    (createdAt.hashCode) +
    (databases.hashCode) +
    (dateChangeTime == null ? 0 : dateChangeTime!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (disabledFeatures.hashCode) +
    (facilitator == null ? 0 : facilitator!.hashCode) +
    (formItems.hashCode) +
    (hostedInvoiceUrl == null ? 0 : hostedInvoiceUrl!.hashCode) +
    (icon == null ? 0 : icon!.hashCode) +
    (iconKey == null ? 0 : iconKey!.hashCode) +
    (id.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (name.hashCode) +
    (numberOfActualMonths == null ? 0 : numberOfActualMonths!.hashCode) +
    (numberOfIntendedMonths == null ? 0 : numberOfIntendedMonths!.hashCode) +
    (numberSeats == null ? 0 : numberSeats!.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (paymentMetadata == null ? 0 : paymentMetadata!.hashCode) +
    (pricingPlan == null ? 0 : pricingPlan!.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (primaryLanguage.hashCode) +
    (quickDeletable == null ? 0 : quickDeletable!.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (tenantId.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (timezone == null ? 0 : timezone!.hashCode) +
    (updatedAt.hashCode) +
    (workspaceFormItems.hashCode);

  @override
  String toString() => 'DtoWorkspaceResponse[background=$background, backgroundKey=$backgroundKey, chatInfo=$chatInfo, commonReactions=$commonReactions, createdAt=$createdAt, databases=$databases, dateChangeTime=$dateChangeTime, description=$description, disabledFeatures=$disabledFeatures, facilitator=$facilitator, formItems=$formItems, hostedInvoiceUrl=$hostedInvoiceUrl, icon=$icon, iconKey=$iconKey, id=$id, illustration=$illustration, name=$name, numberOfActualMonths=$numberOfActualMonths, numberOfIntendedMonths=$numberOfIntendedMonths, numberSeats=$numberSeats, opacity=$opacity, paymentMetadata=$paymentMetadata, pricingPlan=$pricingPlan, primaryColor=$primaryColor, primaryLanguage=$primaryLanguage, quickDeletable=$quickDeletable, reverseBackground=$reverseBackground, secondaryColor=$secondaryColor, tenantId=$tenantId, textAlignment=$textAlignment, textColor=$textColor, timezone=$timezone, updatedAt=$updatedAt, workspaceFormItems=$workspaceFormItems]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
    if (this.chatInfo != null) {
      json[r'chat_info'] = this.chatInfo;
    } else {
      json[r'chat_info'] = null;
    }
      json[r'common_reactions'] = this.commonReactions;
      json[r'created_at'] = this.createdAt;
      json[r'databases'] = this.databases;
    if (this.dateChangeTime != null) {
      json[r'date_change_time'] = this.dateChangeTime;
    } else {
      json[r'date_change_time'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'disabled_features'] = this.disabledFeatures;
    if (this.facilitator != null) {
      json[r'facilitator'] = this.facilitator;
    } else {
      json[r'facilitator'] = null;
    }
      json[r'form_items'] = this.formItems;
    if (this.hostedInvoiceUrl != null) {
      json[r'hosted_invoice_url'] = this.hostedInvoiceUrl;
    } else {
      json[r'hosted_invoice_url'] = null;
    }
    if (this.icon != null) {
      json[r'icon'] = this.icon;
    } else {
      json[r'icon'] = null;
    }
    if (this.iconKey != null) {
      json[r'icon_key'] = this.iconKey;
    } else {
      json[r'icon_key'] = null;
    }
      json[r'id'] = this.id;
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
      json[r'name'] = this.name;
    if (this.numberOfActualMonths != null) {
      json[r'number_of_actual_months'] = this.numberOfActualMonths;
    } else {
      json[r'number_of_actual_months'] = null;
    }
    if (this.numberOfIntendedMonths != null) {
      json[r'number_of_intended_months'] = this.numberOfIntendedMonths;
    } else {
      json[r'number_of_intended_months'] = null;
    }
    if (this.numberSeats != null) {
      json[r'number_seats'] = this.numberSeats;
    } else {
      json[r'number_seats'] = null;
    }
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
    if (this.paymentMetadata != null) {
      json[r'payment_metadata'] = this.paymentMetadata;
    } else {
      json[r'payment_metadata'] = null;
    }
    if (this.pricingPlan != null) {
      json[r'pricing_plan'] = this.pricingPlan;
    } else {
      json[r'pricing_plan'] = null;
    }
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
      json[r'primary_language'] = this.primaryLanguage;
    if (this.quickDeletable != null) {
      json[r'quick_deletable'] = this.quickDeletable;
    } else {
      json[r'quick_deletable'] = null;
    }
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
      json[r'tenant_id'] = this.tenantId;
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
    if (this.timezone != null) {
      json[r'timezone'] = this.timezone;
    } else {
      json[r'timezone'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
      json[r'workspace_form_items'] = this.workspaceFormItems;
    return json;
  }

  /// Returns a new [DtoWorkspaceResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoWorkspaceResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoWorkspaceResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoWorkspaceResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoWorkspaceResponse(
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        chatInfo: DtoChatInfo.fromJson(json[r'chat_info']),
        commonReactions: json[r'common_reactions'] is List
            ? (json[r'common_reactions'] as List).cast<String>()
            : const [],
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        databases: DtoDatabaseResponse.listFromJson(json[r'databases']) ?? const [],
        dateChangeTime: mapValueOfType<String>(json, r'date_change_time'),
        description: mapValueOfType<String>(json, r'description'),
        disabledFeatures: json[r'disabled_features'] is List
            ? (json[r'disabled_features'] as List).cast<String>()
            : const [],
        facilitator: DtoFacilitatorResponse.fromJson(json[r'facilitator']),
        formItems: ModelFormItem.listFromJson(json[r'form_items']) ?? const [],
        hostedInvoiceUrl: mapValueOfType<String>(json, r'hosted_invoice_url'),
        icon: mapValueOfType<String>(json, r'icon'),
        iconKey: mapValueOfType<String>(json, r'icon_key'),
        id: mapValueOfType<String>(json, r'id')!,
        illustration: mapValueOfType<String>(json, r'illustration'),
        name: mapValueOfType<String>(json, r'name')!,
        numberOfActualMonths: mapValueOfType<int>(json, r'number_of_actual_months'),
        numberOfIntendedMonths: mapValueOfType<int>(json, r'number_of_intended_months'),
        numberSeats: json[r'number_seats'] == null
            ? null
            : num.parse(json[r'number_seats'].toString()),
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        paymentMetadata: mapValueOfType<Object>(json, r'payment_metadata'),
        pricingPlan: DtoWorkspaceResponsePricingPlanEnum.fromJson(json[r'pricing_plan']),
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        primaryLanguage: DtoWorkspaceResponsePrimaryLanguageEnum.fromJson(json[r'primary_language'])!,
        quickDeletable: mapValueOfType<bool>(json, r'quick_deletable'),
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        tenantId: mapValueOfType<String>(json, r'tenant_id')!,
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: DtoWorkspaceResponseTextColorEnum.fromJson(json[r'text_color']),
        timezone: mapValueOfType<String>(json, r'timezone'),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        workspaceFormItems: ModelFormItem.listFromJson(json[r'workspace_form_items']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoWorkspaceResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoWorkspaceResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoWorkspaceResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoWorkspaceResponse> mapFromJson(dynamic json) {
    final map = <String, DtoWorkspaceResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoWorkspaceResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoWorkspaceResponse-objects as value to a dart map
  static Map<String, List<DtoWorkspaceResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoWorkspaceResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoWorkspaceResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'id',
    'name',
    'primary_language',
    'tenant_id',
    'updated_at',
  };
}


class DtoWorkspaceResponsePricingPlanEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoWorkspaceResponsePricingPlanEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const FREE = DtoWorkspaceResponsePricingPlanEnum._(r'FREE');
  static const PAID = DtoWorkspaceResponsePricingPlanEnum._(r'PAID');

  /// List of all possible values in this [enum][DtoWorkspaceResponsePricingPlanEnum].
  static const values = <DtoWorkspaceResponsePricingPlanEnum>[
    FREE,
    PAID,
  ];

  static DtoWorkspaceResponsePricingPlanEnum? fromJson(dynamic value) => DtoWorkspaceResponsePricingPlanEnumTypeTransformer().decode(value);

  static List<DtoWorkspaceResponsePricingPlanEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoWorkspaceResponsePricingPlanEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoWorkspaceResponsePricingPlanEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoWorkspaceResponsePricingPlanEnum] to String,
/// and [decode] dynamic data back to [DtoWorkspaceResponsePricingPlanEnum].
class DtoWorkspaceResponsePricingPlanEnumTypeTransformer {
  factory DtoWorkspaceResponsePricingPlanEnumTypeTransformer() => _instance ??= const DtoWorkspaceResponsePricingPlanEnumTypeTransformer._();

  const DtoWorkspaceResponsePricingPlanEnumTypeTransformer._();

  String encode(DtoWorkspaceResponsePricingPlanEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoWorkspaceResponsePricingPlanEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoWorkspaceResponsePricingPlanEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'FREE': return DtoWorkspaceResponsePricingPlanEnum.FREE;
        case r'PAID': return DtoWorkspaceResponsePricingPlanEnum.PAID;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoWorkspaceResponsePricingPlanEnumTypeTransformer] instance.
  static DtoWorkspaceResponsePricingPlanEnumTypeTransformer? _instance;
}



class DtoWorkspaceResponsePrimaryLanguageEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoWorkspaceResponsePrimaryLanguageEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const vi = DtoWorkspaceResponsePrimaryLanguageEnum._(r'vi');
  static const en = DtoWorkspaceResponsePrimaryLanguageEnum._(r'en');
  static const ja = DtoWorkspaceResponsePrimaryLanguageEnum._(r'ja');

  /// List of all possible values in this [enum][DtoWorkspaceResponsePrimaryLanguageEnum].
  static const values = <DtoWorkspaceResponsePrimaryLanguageEnum>[
    vi,
    en,
    ja,
  ];

  static DtoWorkspaceResponsePrimaryLanguageEnum? fromJson(dynamic value) => DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer().decode(value);

  static List<DtoWorkspaceResponsePrimaryLanguageEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoWorkspaceResponsePrimaryLanguageEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoWorkspaceResponsePrimaryLanguageEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoWorkspaceResponsePrimaryLanguageEnum] to String,
/// and [decode] dynamic data back to [DtoWorkspaceResponsePrimaryLanguageEnum].
class DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer {
  factory DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer() => _instance ??= const DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer._();

  const DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer._();

  String encode(DtoWorkspaceResponsePrimaryLanguageEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoWorkspaceResponsePrimaryLanguageEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoWorkspaceResponsePrimaryLanguageEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'vi': return DtoWorkspaceResponsePrimaryLanguageEnum.vi;
        case r'en': return DtoWorkspaceResponsePrimaryLanguageEnum.en;
        case r'ja': return DtoWorkspaceResponsePrimaryLanguageEnum.ja;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer] instance.
  static DtoWorkspaceResponsePrimaryLanguageEnumTypeTransformer? _instance;
}



class DtoWorkspaceResponseTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoWorkspaceResponseTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = DtoWorkspaceResponseTextColorEnum._(r'DARK');
  static const LIGHT = DtoWorkspaceResponseTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][DtoWorkspaceResponseTextColorEnum].
  static const values = <DtoWorkspaceResponseTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static DtoWorkspaceResponseTextColorEnum? fromJson(dynamic value) => DtoWorkspaceResponseTextColorEnumTypeTransformer().decode(value);

  static List<DtoWorkspaceResponseTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoWorkspaceResponseTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoWorkspaceResponseTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoWorkspaceResponseTextColorEnum] to String,
/// and [decode] dynamic data back to [DtoWorkspaceResponseTextColorEnum].
class DtoWorkspaceResponseTextColorEnumTypeTransformer {
  factory DtoWorkspaceResponseTextColorEnumTypeTransformer() => _instance ??= const DtoWorkspaceResponseTextColorEnumTypeTransformer._();

  const DtoWorkspaceResponseTextColorEnumTypeTransformer._();

  String encode(DtoWorkspaceResponseTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoWorkspaceResponseTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoWorkspaceResponseTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return DtoWorkspaceResponseTextColorEnum.DARK;
        case r'LIGHT': return DtoWorkspaceResponseTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoWorkspaceResponseTextColorEnumTypeTransformer] instance.
  static DtoWorkspaceResponseTextColorEnumTypeTransformer? _instance;
}


