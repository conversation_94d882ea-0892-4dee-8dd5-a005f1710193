package service

import (
	"context"

	"github.com/google/uuid"

	"clipcrow/essentialworkware/model/dto"
	"clipcrow/essentialworkware/repository"
)

// TemplateService is interface
type RecordQueryService interface {
	Create(ctx context.Context, tx repository.Repository, databaseID uuid.UUID, recordQueryReq *dto.RecordQueryPost) error
	FindByID(ctx context.Context, databaseID uuid.UUID) (dto.RecordQueryResponse, error)
	FindByIDs(ctx context.Context, databaseIDs []uuid.UUID) (map[uuid.UUID]dto.RecordQueryResponse, error)
	FlexibleUpdate(ctx context.Context, tx repository.Repository, databaseID uuid.UUID, recordQueryReq *dto.RecordQueryPatch) error
}
