//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class WebhookReaction {
  /// Returns a new [WebhookReaction] instance.
  WebhookReaction({
    required this.actor,
    required this.emoji,
  });

  WebhookCard actor;

  String emoji;

  @override
  bool operator ==(Object other) => identical(this, other) || other is WebhookReaction &&
     other.actor == actor &&
     other.emoji == emoji;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (actor.hashCode) +
    (emoji.hashCode);

  @override
  String toString() => 'WebhookReaction[actor=$actor, emoji=$emoji]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'actor'] = this.actor;
      json[r'emoji'] = this.emoji;
    return json;
  }

  /// Returns a new [WebhookReaction] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static WebhookReaction? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "WebhookReaction[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "WebhookReaction[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return WebhookReaction(
        actor: WebhookCard.fromJson(json[r'actor'])!,
        emoji: mapValueOfType<String>(json, r'emoji')!,
      );
    }
    return null;
  }

  static List<WebhookReaction>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <WebhookReaction>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = WebhookReaction.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, WebhookReaction> mapFromJson(dynamic json) {
    final map = <String, WebhookReaction>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = WebhookReaction.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of WebhookReaction-objects as value to a dart map
  static Map<String, List<WebhookReaction>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<WebhookReaction>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = WebhookReaction.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'actor',
    'emoji',
  };
}

