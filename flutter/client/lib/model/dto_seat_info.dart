//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoSeatInfo {
  /// Returns a new [DtoSeatInfo] instance.
  DtoSeatInfo({
    this.numberSeatsWorkspace,
    this.seatConsumingInfo,
    this.targetId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? numberSeatsWorkspace;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoSeatConsumingInfo? seatConsumingInfo;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? targetId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoSeatInfo &&
     other.numberSeatsWorkspace == numberSeatsWorkspace &&
     other.seatConsumingInfo == seatConsumingInfo &&
     other.targetId == targetId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (numberSeatsWorkspace == null ? 0 : numberSeatsWorkspace!.hashCode) +
    (seatConsumingInfo == null ? 0 : seatConsumingInfo!.hashCode) +
    (targetId == null ? 0 : targetId!.hashCode);

  @override
  String toString() => 'DtoSeatInfo[numberSeatsWorkspace=$numberSeatsWorkspace, seatConsumingInfo=$seatConsumingInfo, targetId=$targetId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.numberSeatsWorkspace != null) {
      json[r'number_seats_workspace'] = this.numberSeatsWorkspace;
    } else {
      json[r'number_seats_workspace'] = null;
    }
    if (this.seatConsumingInfo != null) {
      json[r'seat_consuming_info'] = this.seatConsumingInfo;
    } else {
      json[r'seat_consuming_info'] = null;
    }
    if (this.targetId != null) {
      json[r'target_id'] = this.targetId;
    } else {
      json[r'target_id'] = null;
    }
    return json;
  }

  /// Returns a new [DtoSeatInfo] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoSeatInfo? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoSeatInfo[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoSeatInfo[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoSeatInfo(
        numberSeatsWorkspace: json[r'number_seats_workspace'] == null
            ? null
            : num.parse(json[r'number_seats_workspace'].toString()),
        seatConsumingInfo: DtoSeatConsumingInfo.fromJson(json[r'seat_consuming_info']),
        targetId: mapValueOfType<String>(json, r'target_id'),
      );
    }
    return null;
  }

  static List<DtoSeatInfo>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoSeatInfo>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoSeatInfo.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoSeatInfo> mapFromJson(dynamic json) {
    final map = <String, DtoSeatInfo>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSeatInfo.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoSeatInfo-objects as value to a dart map
  static Map<String, List<DtoSeatInfo>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoSeatInfo>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSeatInfo.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

