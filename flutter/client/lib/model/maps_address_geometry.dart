//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MapsAddressGeometry {
  /// Returns a new [MapsAddressGeometry] instance.
  MapsAddressGeometry({
    this.bounds,
    this.location,
    this.locationType,
    this.types = const [],
    this.viewport,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  MapsLatLngBounds? bounds;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  GooglemapsGithubIoMapsLatLng? location;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? locationType;

  List<String> types;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  MapsLatLngBounds? viewport;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MapsAddressGeometry &&
     other.bounds == bounds &&
     other.location == location &&
     other.locationType == locationType &&
     other.types == types &&
     other.viewport == viewport;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (bounds == null ? 0 : bounds!.hashCode) +
    (location == null ? 0 : location!.hashCode) +
    (locationType == null ? 0 : locationType!.hashCode) +
    (types.hashCode) +
    (viewport == null ? 0 : viewport!.hashCode);

  @override
  String toString() => 'MapsAddressGeometry[bounds=$bounds, location=$location, locationType=$locationType, types=$types, viewport=$viewport]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.bounds != null) {
      json[r'bounds'] = this.bounds;
    } else {
      json[r'bounds'] = null;
    }
    if (this.location != null) {
      json[r'location'] = this.location;
    } else {
      json[r'location'] = null;
    }
    if (this.locationType != null) {
      json[r'location_type'] = this.locationType;
    } else {
      json[r'location_type'] = null;
    }
      json[r'types'] = this.types;
    if (this.viewport != null) {
      json[r'viewport'] = this.viewport;
    } else {
      json[r'viewport'] = null;
    }
    return json;
  }

  /// Returns a new [MapsAddressGeometry] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MapsAddressGeometry? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MapsAddressGeometry[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MapsAddressGeometry[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MapsAddressGeometry(
        bounds: MapsLatLngBounds.fromJson(json[r'bounds']),
        location: GooglemapsGithubIoMapsLatLng.fromJson(json[r'location']),
        locationType: mapValueOfType<String>(json, r'location_type'),
        types: json[r'types'] is List
            ? (json[r'types'] as List).cast<String>()
            : const [],
        viewport: MapsLatLngBounds.fromJson(json[r'viewport']),
      );
    }
    return null;
  }

  static List<MapsAddressGeometry>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MapsAddressGeometry>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MapsAddressGeometry.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MapsAddressGeometry> mapFromJson(dynamic json) {
    final map = <String, MapsAddressGeometry>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsAddressGeometry.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MapsAddressGeometry-objects as value to a dart map
  static Map<String, List<MapsAddressGeometry>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MapsAddressGeometry>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsAddressGeometry.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

