# openapi.api.ChatsApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to */api/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**workspacesWorkspaceIdChatsBotReplyStatusGet**](ChatsApi.md#workspacesworkspaceidchatsbotreplystatusget) | **GET** /workspaces/{workspace_id}/chats/bot_reply_status | Check if bot is replying
[**workspacesWorkspaceIdChatsChatIdDelete**](ChatsApi.md#workspacesworkspaceidchatschatiddelete) | **DELETE** /workspaces/{workspace_id}/chats/{chat_id} | Delete message
[**workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPost**](ChatsApi.md#workspacesworkspaceidchatschatidfilessignedurlspost) | **POST** /workspaces/{workspace_id}/chats/{chat_id}/files/signed_urls | Create signed url download Chat Image
[**workspacesWorkspaceIdChatsChatIdFindPageGet**](ChatsApi.md#workspacesworkspaceidchatschatidfindpageget) | **GET** /workspaces/{workspace_id}/chats/{chat_id}/find-page | Returns number of page and a message list by message ID
[**workspacesWorkspaceIdChatsChatIdGet**](ChatsApi.md#workspacesworkspaceidchatschatidget) | **GET** /workspaces/{workspace_id}/chats/{chat_id} | Returns Message
[**workspacesWorkspaceIdChatsChatIdPatch**](ChatsApi.md#workspacesworkspaceidchatschatidpatch) | **PATCH** /workspaces/{workspace_id}/chats/{chat_id} | Update Chat
[**workspacesWorkspaceIdChatsChatIdReactionsPut**](ChatsApi.md#workspacesworkspaceidchatschatidreactionsput) | **PUT** /workspaces/{workspace_id}/chats/{chat_id}/reactions | React Chat
[**workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGet**](ChatsApi.md#workspacesworkspaceidchatschatidreactionsreactionidmembersget) | **GET** /workspaces/{workspace_id}/chats/{chat_id}/reactions/{reaction_id}/members | Returns members who reacted to the reaction
[**workspacesWorkspaceIdChatsGet**](ChatsApi.md#workspacesworkspaceidchatsget) | **GET** /workspaces/{workspace_id}/chats | Returns a list of Message
[**workspacesWorkspaceIdChatsGetUnreadFeaturesGet**](ChatsApi.md#workspacesworkspaceidchatsgetunreadfeaturesget) | **GET** /workspaces/{workspace_id}/chats/get-unread-features | Return all features has unread messages
[**workspacesWorkspaceIdChatsLogsPost**](ChatsApi.md#workspacesworkspaceidchatslogspost) | **POST** /workspaces/{workspace_id}/chats/logs | Post log
[**workspacesWorkspaceIdChatsMentionNameGet**](ChatsApi.md#workspacesworkspaceidchatsmentionnameget) | **GET** /workspaces/{workspace_id}/chats/mention-name | Returns mention names
[**workspacesWorkspaceIdChatsMentionPost**](ChatsApi.md#workspacesworkspaceidchatsmentionpost) | **POST** /workspaces/{workspace_id}/chats/mention | Returns a list of members or external links
[**workspacesWorkspaceIdChatsPost**](ChatsApi.md#workspacesworkspaceidchatspost) | **POST** /workspaces/{workspace_id}/chats | Send message
[**workspacesWorkspaceIdChatsTypingPost**](ChatsApi.md#workspacesworkspaceidchatstypingpost) | **POST** /workspaces/{workspace_id}/chats/typing | Typing message
[**workspacesWorkspaceIdChatsUpdateHasViewedPatch**](ChatsApi.md#workspacesworkspaceidchatsupdatehasviewedpatch) | **PATCH** /workspaces/{workspace_id}/chats/update-has-viewed | Set has viewed


# **workspacesWorkspaceIdChatsBotReplyStatusGet**
> DtoBotReplyStatusResponse workspacesWorkspaceIdChatsBotReplyStatusGet(workspaceId, targetType, targetId)

Check if bot is replying

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | WorkspaceID
final targetType = targetType_example; // String | Target type
final targetId = targetId_example; // String | Target ID

try {
    final result = api_instance.workspacesWorkspaceIdChatsBotReplyStatusGet(workspaceId, targetType, targetId);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsBotReplyStatusGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| WorkspaceID | 
 **targetType** | **String**| Target type | 
 **targetId** | **String**| Target ID | 

### Return type

[**DtoBotReplyStatusResponse**](DtoBotReplyStatusResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdDelete**
> workspacesWorkspaceIdChatsChatIdDelete(workspaceId, chatId)

Delete message

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | WorkspaceID
final chatId = chatId_example; // String | ChatID

try {
    api_instance.workspacesWorkspaceIdChatsChatIdDelete(workspaceId, chatId);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdDelete: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| WorkspaceID | 
 **chatId** | **String**| ChatID | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPost**
> DtoGetChatSignedURLResponse workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPost(workspaceId, chatId, method, body)

Create signed url download Chat Image

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final chatId = chatId_example; // String | Chat ID
final method = method_example; // String | Method is GET or PUT
final body = DtoImageSignedURLObjectRequest(); // DtoImageSignedURLObjectRequest | Get signed url request

try {
    final result = api_instance.workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPost(workspaceId, chatId, method, body);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **chatId** | **String**| Chat ID | 
 **method** | **String**| Method is GET or PUT | 
 **body** | [**DtoImageSignedURLObjectRequest**](DtoImageSignedURLObjectRequest.md)| Get signed url request | 

### Return type

[**DtoGetChatSignedURLResponse**](DtoGetChatSignedURLResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdFindPageGet**
> DtoFindPageAndMessagesResponse workspacesWorkspaceIdChatsChatIdFindPageGet(workspaceId, chatId, pageSize, targetId, targetType, doGetPrivate)

Returns number of page and a message list by message ID

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final chatId = chatId_example; // String | chat id
final pageSize = 56; // int | 
final targetId = targetId_example; // String | 
final targetType = targetType_example; // String | 
final doGetPrivate = true; // bool | 

try {
    final result = api_instance.workspacesWorkspaceIdChatsChatIdFindPageGet(workspaceId, chatId, pageSize, targetId, targetType, doGetPrivate);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdFindPageGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **chatId** | **String**| chat id | 
 **pageSize** | **int**|  | 
 **targetId** | **String**|  | 
 **targetType** | **String**|  | 
 **doGetPrivate** | **bool**|  | [optional] 

### Return type

[**DtoFindPageAndMessagesResponse**](DtoFindPageAndMessagesResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdGet**
> DtoMessage workspacesWorkspaceIdChatsChatIdGet(workspaceId, chatId, hasViewed)

Returns Message

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final chatId = chatId_example; // String | chat id
final hasViewed = true; // bool | 

try {
    final result = api_instance.workspacesWorkspaceIdChatsChatIdGet(workspaceId, chatId, hasViewed);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **chatId** | **String**| chat id | 
 **hasViewed** | **bool**|  | [optional] 

### Return type

[**DtoMessage**](DtoMessage.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdPatch**
> DtoMessage workspacesWorkspaceIdChatsChatIdPatch(workspaceId, chatId, body)

Update Chat

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final chatId = chatId_example; // String | chat id
final body = DtoPatchMessageRequest(); // DtoPatchMessageRequest | PatchMessageRequest object

try {
    final result = api_instance.workspacesWorkspaceIdChatsChatIdPatch(workspaceId, chatId, body);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdPatch: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **chatId** | **String**| chat id | 
 **body** | [**DtoPatchMessageRequest**](DtoPatchMessageRequest.md)| PatchMessageRequest object | 

### Return type

[**DtoMessage**](DtoMessage.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdReactionsPut**
> workspacesWorkspaceIdChatsChatIdReactionsPut(workspaceId, chatId, body)

React Chat

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final chatId = chatId_example; // String | chat id
final body = DtoReactionUpdateRequest(); // DtoReactionUpdateRequest | ReactionUpdateRequest object

try {
    api_instance.workspacesWorkspaceIdChatsChatIdReactionsPut(workspaceId, chatId, body);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdReactionsPut: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **chatId** | **String**| chat id | 
 **body** | [**DtoReactionUpdateRequest**](DtoReactionUpdateRequest.md)| ReactionUpdateRequest object | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGet**
> DtoGetMembersResponse workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGet(workspaceId, chatId, reactionId, page, pageSize)

Returns members who reacted to the reaction

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final chatId = chatId_example; // String | Chat ID
final reactionId = reactionId_example; // String | Reaction ID
final page = 56; // int | 
final pageSize = 56; // int | 

try {
    final result = api_instance.workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGet(workspaceId, chatId, reactionId, page, pageSize);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **chatId** | **String**| Chat ID | 
 **reactionId** | **String**| Reaction ID | 
 **page** | **int**|  | [optional] 
 **pageSize** | **int**|  | [optional] 

### Return type

[**DtoGetMembersResponse**](DtoGetMembersResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsGet**
> DtoGetMessagesResponse workspacesWorkspaceIdChatsGet(workspaceId, targetId, targetType, bookmark, doGetPrivate, facilitator, hasViewed, isArchive, manageProgressAsTask, messageType, onlyUnread, page, pageSize, query)

Returns a list of Message

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final targetId = targetId_example; // String | 
final targetType = targetType_example; // String | 
final bookmark = bookmark_example; // String | 
final doGetPrivate = true; // bool | 
final facilitator = facilitator_example; // String | 
final hasViewed = true; // bool | 
final isArchive = true; // bool | 
final manageProgressAsTask = true; // bool | 
final messageType = []; // List<String> | 
final onlyUnread = true; // bool | 
final page = 56; // int | 
final pageSize = 56; // int | 
final query = query_example; // String | 

try {
    final result = api_instance.workspacesWorkspaceIdChatsGet(workspaceId, targetId, targetType, bookmark, doGetPrivate, facilitator, hasViewed, isArchive, manageProgressAsTask, messageType, onlyUnread, page, pageSize, query);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **targetId** | **String**|  | 
 **targetType** | **String**|  | 
 **bookmark** | **String**|  | [optional] 
 **doGetPrivate** | **bool**|  | [optional] 
 **facilitator** | **String**|  | [optional] 
 **hasViewed** | **bool**|  | [optional] 
 **isArchive** | **bool**|  | [optional] 
 **manageProgressAsTask** | **bool**|  | [optional] 
 **messageType** | [**List<String>**](String.md)|  | [optional] [default to const []]
 **onlyUnread** | **bool**|  | [optional] 
 **page** | **int**|  | [optional] 
 **pageSize** | **int**|  | [optional] 
 **query** | **String**|  | [optional] 

### Return type

[**DtoGetMessagesResponse**](DtoGetMessagesResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsGetUnreadFeaturesGet**
> List<DtoFeatureUnread> workspacesWorkspaceIdChatsGetUnreadFeaturesGet(workspaceId)

Return all features has unread messages

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID

try {
    final result = api_instance.workspacesWorkspaceIdChatsGetUnreadFeaturesGet(workspaceId);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsGetUnreadFeaturesGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 

### Return type

[**List<DtoFeatureUnread>**](DtoFeatureUnread.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsLogsPost**
> DtoMessage workspacesWorkspaceIdChatsLogsPost(workspaceId, body)

Post log

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | WorkspaceID
final body = DtoPostLogRequest(); // DtoPostLogRequest | PostLogRequest object

try {
    final result = api_instance.workspacesWorkspaceIdChatsLogsPost(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsLogsPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| WorkspaceID | 
 **body** | [**DtoPostLogRequest**](DtoPostLogRequest.md)| PostLogRequest object | 

### Return type

[**DtoMessage**](DtoMessage.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsMentionNameGet**
> DtoMentionNameResponse workspacesWorkspaceIdChatsMentionNameGet(workspaceId, targetId, targetType)

Returns mention names

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final targetId = targetId_example; // String | 
final targetType = targetType_example; // String | 

try {
    final result = api_instance.workspacesWorkspaceIdChatsMentionNameGet(workspaceId, targetId, targetType);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsMentionNameGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **targetId** | **String**|  | 
 **targetType** | **String**|  | 

### Return type

[**DtoMentionNameResponse**](DtoMentionNameResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsMentionPost**
> DtoGetMentionResponse workspacesWorkspaceIdChatsMentionPost(workspaceId, body)

Returns a list of members or external links

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final body = DtoGetMentionRequest(); // DtoGetMentionRequest | Query Parameters

try {
    final result = api_instance.workspacesWorkspaceIdChatsMentionPost(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsMentionPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **body** | [**DtoGetMentionRequest**](DtoGetMentionRequest.md)| Query Parameters | 

### Return type

[**DtoGetMentionResponse**](DtoGetMentionResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsPost**
> DtoMessage workspacesWorkspaceIdChatsPost(workspaceId, body)

Send message

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | WorkspaceID
final body = DtoPostChatRequest(); // DtoPostChatRequest | PostChatRequest object

try {
    final result = api_instance.workspacesWorkspaceIdChatsPost(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| WorkspaceID | 
 **body** | [**DtoPostChatRequest**](DtoPostChatRequest.md)| PostChatRequest object | 

### Return type

[**DtoMessage**](DtoMessage.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsTypingPost**
> String workspacesWorkspaceIdChatsTypingPost(workspaceId, body)

Typing message

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | WorkspaceID
final body = DtoPostTypingRequest(); // DtoPostTypingRequest | PostTypingRequest object

try {
    final result = api_instance.workspacesWorkspaceIdChatsTypingPost(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsTypingPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| WorkspaceID | 
 **body** | [**DtoPostTypingRequest**](DtoPostTypingRequest.md)| PostTypingRequest object | 

### Return type

**String**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdChatsUpdateHasViewedPatch**
> workspacesWorkspaceIdChatsUpdateHasViewedPatch(workspaceId, targetId, targetType)

Set has viewed

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = ChatsApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final targetId = targetId_example; // String | 
final targetType = targetType_example; // String | 

try {
    api_instance.workspacesWorkspaceIdChatsUpdateHasViewedPatch(workspaceId, targetId, targetType);
} catch (e) {
    print('Exception when calling ChatsApi->workspacesWorkspaceIdChatsUpdateHasViewedPatch: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **targetId** | **String**|  | 
 **targetType** | **String**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

