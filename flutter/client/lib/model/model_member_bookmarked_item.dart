//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelMemberBookmarkedItem {
  /// Returns a new [ModelMemberBookmarkedItem] instance.
  ModelMemberBookmarkedItem({
    required this.bookmarkedAt,
    required this.createdAt,
    this.deletedAt,
    required this.id,
    required this.memberId,
    this.record,
    required this.targetId,
    this.targetType,
    required this.updatedAt,
  });

  String bookmarkedAt;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String id;

  String memberId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelRecord? record;

  String targetId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? targetType;

  String updatedAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelMemberBookmarkedItem &&
     other.bookmarkedAt == bookmarkedAt &&
     other.createdAt == createdAt &&
     other.deletedAt == deletedAt &&
     other.id == id &&
     other.memberId == memberId &&
     other.record == record &&
     other.targetId == targetId &&
     other.targetType == targetType &&
     other.updatedAt == updatedAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (bookmarkedAt.hashCode) +
    (createdAt.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (id.hashCode) +
    (memberId.hashCode) +
    (record == null ? 0 : record!.hashCode) +
    (targetId.hashCode) +
    (targetType == null ? 0 : targetType!.hashCode) +
    (updatedAt.hashCode);

  @override
  String toString() => 'ModelMemberBookmarkedItem[bookmarkedAt=$bookmarkedAt, createdAt=$createdAt, deletedAt=$deletedAt, id=$id, memberId=$memberId, record=$record, targetId=$targetId, targetType=$targetType, updatedAt=$updatedAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'bookmarked_at'] = this.bookmarkedAt;
      json[r'created_at'] = this.createdAt;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'id'] = this.id;
      json[r'member_id'] = this.memberId;
    if (this.record != null) {
      json[r'record'] = this.record;
    } else {
      json[r'record'] = null;
    }
      json[r'target_id'] = this.targetId;
    if (this.targetType != null) {
      json[r'target_type'] = this.targetType;
    } else {
      json[r'target_type'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
    return json;
  }

  /// Returns a new [ModelMemberBookmarkedItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelMemberBookmarkedItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelMemberBookmarkedItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelMemberBookmarkedItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelMemberBookmarkedItem(
        bookmarkedAt: mapValueOfType<String>(json, r'bookmarked_at')!,
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        id: mapValueOfType<String>(json, r'id')!,
        memberId: mapValueOfType<String>(json, r'member_id')!,
        record: ModelRecord.fromJson(json[r'record']),
        targetId: mapValueOfType<String>(json, r'target_id')!,
        targetType: mapValueOfType<String>(json, r'target_type'),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
      );
    }
    return null;
  }

  static List<ModelMemberBookmarkedItem>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMemberBookmarkedItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMemberBookmarkedItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelMemberBookmarkedItem> mapFromJson(dynamic json) {
    final map = <String, ModelMemberBookmarkedItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMemberBookmarkedItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelMemberBookmarkedItem-objects as value to a dart map
  static Map<String, List<ModelMemberBookmarkedItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelMemberBookmarkedItem>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMemberBookmarkedItem.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'bookmarked_at',
    'created_at',
    'id',
    'member_id',
    'target_id',
    'updated_at',
  };
}

