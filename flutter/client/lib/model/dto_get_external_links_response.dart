//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetExternalLinksResponse {
  /// Returns a new [DtoGetExternalLinksResponse] instance.
  DtoGetExternalLinksResponse({
    this.hasMore,
    this.items = const [],
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasMore;

  List<DtoExternalLinkResponse> items;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetExternalLinksResponse &&
     other.hasMore == hasMore &&
     other.items == items;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (hasMore == null ? 0 : hasMore!.hashCode) +
    (items.hashCode);

  @override
  String toString() => 'DtoGetExternalLinksResponse[hasMore=$hasMore, items=$items]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.hasMore != null) {
      json[r'has_more'] = this.hasMore;
    } else {
      json[r'has_more'] = null;
    }
      json[r'items'] = this.items;
    return json;
  }

  /// Returns a new [DtoGetExternalLinksResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetExternalLinksResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetExternalLinksResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetExternalLinksResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetExternalLinksResponse(
        hasMore: mapValueOfType<bool>(json, r'has_more'),
        items: DtoExternalLinkResponse.listFromJson(json[r'items']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoGetExternalLinksResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetExternalLinksResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetExternalLinksResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetExternalLinksResponse> mapFromJson(dynamic json) {
    final map = <String, DtoGetExternalLinksResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetExternalLinksResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetExternalLinksResponse-objects as value to a dart map
  static Map<String, List<DtoGetExternalLinksResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetExternalLinksResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetExternalLinksResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

