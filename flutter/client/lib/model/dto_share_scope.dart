//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoShareScope {
  /// Returns a new [DtoShareScope] instance.
  DtoShareScope({
    this.memberIds = const [],
    this.tagIds = const [],
  });

  List<String> memberIds;

  List<String> tagIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoShareScope &&
     other.memberIds == memberIds &&
     other.tagIds == tagIds;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (memberIds.hashCode) +
    (tagIds.hashCode);

  @override
  String toString() => 'DtoShareScope[memberIds=$memberIds, tagIds=$tagIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'member_ids'] = this.memberIds;
      json[r'tag_ids'] = this.tagIds;
    return json;
  }

  /// Returns a new [DtoShareScope] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoShareScope? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoShareScope[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoShareScope[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoShareScope(
        memberIds: json[r'member_ids'] is List
            ? (json[r'member_ids'] as List).cast<String>()
            : const [],
        tagIds: json[r'tag_ids'] is List
            ? (json[r'tag_ids'] as List).cast<String>()
            : const [],
      );
    }
    return null;
  }

  static List<DtoShareScope>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoShareScope>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoShareScope.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoShareScope> mapFromJson(dynamic json) {
    final map = <String, DtoShareScope>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoShareScope.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoShareScope-objects as value to a dart map
  static Map<String, List<DtoShareScope>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoShareScope>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoShareScope.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

