//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoFormItemFileSignedURLObjectRequest {
  /// Returns a new [DtoFormItemFileSignedURLObjectRequest] instance.
  DtoFormItemFileSignedURLObjectRequest({
    required this.filePath,
  });

  String filePath;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoFormItemFileSignedURLObjectRequest &&
     other.filePath == filePath;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (filePath.hashCode);

  @override
  String toString() => 'DtoFormItemFileSignedURLObjectRequest[filePath=$filePath]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'file_path'] = this.filePath;
    return json;
  }

  /// Returns a new [DtoFormItemFileSignedURLObjectRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoFormItemFileSignedURLObjectRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoFormItemFileSignedURLObjectRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoFormItemFileSignedURLObjectRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoFormItemFileSignedURLObjectRequest(
        filePath: mapValueOfType<String>(json, r'file_path')!,
      );
    }
    return null;
  }

  static List<DtoFormItemFileSignedURLObjectRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoFormItemFileSignedURLObjectRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoFormItemFileSignedURLObjectRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoFormItemFileSignedURLObjectRequest> mapFromJson(dynamic json) {
    final map = <String, DtoFormItemFileSignedURLObjectRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFormItemFileSignedURLObjectRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoFormItemFileSignedURLObjectRequest-objects as value to a dart map
  static Map<String, List<DtoFormItemFileSignedURLObjectRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoFormItemFileSignedURLObjectRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFormItemFileSignedURLObjectRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'file_path',
  };
}

