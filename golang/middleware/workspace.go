package middleware

import (
	"net/http"
	"strings"

	"clipcrow/essentialworkware/internal/container"
	"clipcrow/essentialworkware/pkg/apihelper"
)

func WorkspaceMiddleware(c container.Container) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			apiHelper := apihelper.NewAPIHelper(c, w, r)

			if strings.HasSuffix(r.URL.Path, "/pricing-plan") {
				apiHelper.Next(next)
				return
			}
			if strings.HasSuffix(r.URL.Path, "/files/signed_urls") {
				apiHelper.Next(next)
				return
			}
			workspaceID := apiHelper.URLParam("workspace_id")
			authTokenClaims := apiHelper.GetAuthTokenClaims()
			url := strings.Replace(r.URL.Path, "/api/v1", "", 1)
			if !(r.Method == http.MethodGet && url == "/workspaces/"+workspaceID.String() && authTokenClaims.Firebase.Tenant == "") {
				// if authTokenClaims.WorkspaceID != workspaceID.String() {
				// 	apiHelper.Failure(errors.Wrap(apihelper.ErrForbidden, "workspace_id mismatch"))
				// 	return
				// }
			}

			apiHelper.Next(next)
		})
	}
}
