# openapi.model.DtoExternalLinkResponse

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**background** | **String** |  | [optional] 
**backgroundKey** | **String** |  | [optional] 
**chatInfo** | [**DtoChatInfo**](DtoChatInfo.md) |  | [optional] 
**createdAt** | **String** |  | 
**creatorId** | **String** |  | [optional] 
**description** | **String** |  | [optional] 
**facilitator** | [**DtoFacilitatorResponse**](DtoFacilitatorResponse.md) |  | [optional] 
**formItems** | [**List<ModelFormItem>**](ModelFormItem.md) |  | [optional] [default to const []]
**hasNewMessage** | **bool** |  | [optional] 
**icon** | **String** | Scopes         pq.StringArray `json:\"scopes,omitempty\" swaggertype:\"array,string\"` | [optional] 
**iconKey** | **String** |  | [optional] 
**id** | **String** |  | 
**illustration** | **String** |  | [optional] 
**memberBookmarkedItemId** | **String** |  | [optional] 
**name** | **String** |  | 
**oAuth** | [**ModelOAuth**](ModelOAuth.md) |  | [optional] 
**oAuthEnabled** | **bool** |  | 
**oAuthId** | **String** |  | [optional] 
**opacity** | **num** |  | [optional] 
**primaryColor** | **String** |  | [optional] 
**reverseBackground** | **bool** |  | [optional] 
**secondaryColor** | **String** |  | [optional] 
**shareScope** | [**DtoShareScopeResponse**](DtoShareScopeResponse.md) |  | [optional] 
**status** | **String** |  | [optional] 
**tagNumber** | **int** |  | [optional] 
**textAlignment** | **String** |  | [optional] 
**textColor** | **String** |  | [optional] 
**updatedAt** | **String** |  | 
**webhook** | [**ModelWebhook**](ModelWebhook.md) |  | [optional] 
**webhookEnabled** | **bool** |  | 
**webhookId** | **String** |  | [optional] 
**workspaceId** | **String** |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


