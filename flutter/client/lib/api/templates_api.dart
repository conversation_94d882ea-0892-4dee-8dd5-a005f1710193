//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class TemplatesApi {
  TemplatesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get Template by Tag Number
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  ///
  /// * [String] requestPage:
  Future<Response> workspacesWorkspaceIdTemplatesByTagTagNumberGetWithHttpInfo(String workspaceId, String tagNumber, { String? requestPage, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/by-tag/{tag_number}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_number}', tagNumber);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (requestPage != null) {
      queryParams.addAll(_queryParams('', 'request_page', requestPage));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Template by Tag Number
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  ///
  /// * [String] requestPage:
  Future<DtoTemplateResponse?> workspacesWorkspaceIdTemplatesByTagTagNumberGet(String workspaceId, String tagNumber, { String? requestPage, }) async {
    final response = await workspacesWorkspaceIdTemplatesByTagTagNumberGetWithHttpInfo(workspaceId, tagNumber,  requestPage: requestPage, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTemplateResponse',) as DtoTemplateResponse;
    
    }
    return null;
  }

  /// Get Templates
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] bookmark:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] includeDefaultTemplate:
  ///
  /// * [List<String>] numberUsing:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [List<String>] pic:
  ///
  /// * [String] query:
  ///
  /// * [String] requestPage:
  ///
  /// * [String] sort:
  ///
  /// * [String] status:
  ///
  /// * [List<String>] tagIds:
  ///
  /// * [bool] taskEnabled:
  Future<Response> workspacesWorkspaceIdTemplatesGetWithHttpInfo(String workspaceId, { bool? bookmark, String? facilitator, bool? includeDefaultTemplate, List<String>? numberUsing, bool? onlyUnread, int? page, int? pageSize, List<String>? pic, String? query, String? requestPage, String? sort, String? status, List<String>? tagIds, bool? taskEnabled, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (bookmark != null) {
      queryParams.addAll(_queryParams('', 'bookmark', bookmark));
    }
    if (facilitator != null) {
      queryParams.addAll(_queryParams('', 'facilitator', facilitator));
    }
    if (includeDefaultTemplate != null) {
      queryParams.addAll(_queryParams('', 'include_default_template', includeDefaultTemplate));
    }
    if (numberUsing != null) {
      queryParams.addAll(_queryParams('csv', 'number_using', numberUsing));
    }
    if (onlyUnread != null) {
      queryParams.addAll(_queryParams('', 'only_unread', onlyUnread));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (pic != null) {
      queryParams.addAll(_queryParams('csv', 'pic', pic));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
    if (requestPage != null) {
      queryParams.addAll(_queryParams('', 'request_page', requestPage));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }
    if (status != null) {
      queryParams.addAll(_queryParams('', 'status', status));
    }
    if (tagIds != null) {
      queryParams.addAll(_queryParams('csv', 'tag_ids', tagIds));
    }
    if (taskEnabled != null) {
      queryParams.addAll(_queryParams('', 'task_enabled', taskEnabled));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Templates
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [bool] bookmark:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] includeDefaultTemplate:
  ///
  /// * [List<String>] numberUsing:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [List<String>] pic:
  ///
  /// * [String] query:
  ///
  /// * [String] requestPage:
  ///
  /// * [String] sort:
  ///
  /// * [String] status:
  ///
  /// * [List<String>] tagIds:
  ///
  /// * [bool] taskEnabled:
  Future<DtoGetTemplatesResponse?> workspacesWorkspaceIdTemplatesGet(String workspaceId, { bool? bookmark, String? facilitator, bool? includeDefaultTemplate, List<String>? numberUsing, bool? onlyUnread, int? page, int? pageSize, List<String>? pic, String? query, String? requestPage, String? sort, String? status, List<String>? tagIds, bool? taskEnabled, }) async {
    final response = await workspacesWorkspaceIdTemplatesGetWithHttpInfo(workspaceId,  bookmark: bookmark, facilitator: facilitator, includeDefaultTemplate: includeDefaultTemplate, numberUsing: numberUsing, onlyUnread: onlyUnread, page: page, pageSize: pageSize, pic: pic, query: query, requestPage: requestPage, sort: sort, status: status, tagIds: tagIds, taskEnabled: taskEnabled, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetTemplatesResponse',) as DtoGetTemplatesResponse;
    
    }
    return null;
  }

  /// Create Template
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostTemplateRequest] body (required):
  ///   Template request object
  Future<Response> workspacesWorkspaceIdTemplatesPostWithHttpInfo(String workspaceId, DtoPostTemplateRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Template
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostTemplateRequest] body (required):
  ///   Template request object
  Future<DtoTemplateResponse?> workspacesWorkspaceIdTemplatesPost(String workspaceId, DtoPostTemplateRequest body,) async {
    final response = await workspacesWorkspaceIdTemplatesPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTemplateResponse',) as DtoTemplateResponse;
    
    }
    return null;
  }

  /// Get Templates for filter task
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] dueDate:
  ///
  /// * [String] facilitator:
  ///
  /// * [List<String>] measureTimes:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [List<String>] pic:
  ///
  /// * [String] query:
  ///
  /// * [String] sort:
  ///
  /// * [String] status:
  ///
  /// * [List<String>] tagIds:
  Future<Response> workspacesWorkspaceIdTemplatesTasksGetWithHttpInfo(String workspaceId, { String? dueDate, String? facilitator, List<String>? measureTimes, int? page, int? pageSize, List<String>? pic, String? query, String? sort, String? status, List<String>? tagIds, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/tasks'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (dueDate != null) {
      queryParams.addAll(_queryParams('', 'due_date', dueDate));
    }
    if (facilitator != null) {
      queryParams.addAll(_queryParams('', 'facilitator', facilitator));
    }
    if (measureTimes != null) {
      queryParams.addAll(_queryParams('csv', 'measure_times', measureTimes));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (pic != null) {
      queryParams.addAll(_queryParams('csv', 'pic', pic));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }
    if (status != null) {
      queryParams.addAll(_queryParams('', 'status', status));
    }
    if (tagIds != null) {
      queryParams.addAll(_queryParams('csv', 'tag_ids', tagIds));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Templates for filter task
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] dueDate:
  ///
  /// * [String] facilitator:
  ///
  /// * [List<String>] measureTimes:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [List<String>] pic:
  ///
  /// * [String] query:
  ///
  /// * [String] sort:
  ///
  /// * [String] status:
  ///
  /// * [List<String>] tagIds:
  Future<DtoGetTemplatesResponse?> workspacesWorkspaceIdTemplatesTasksGet(String workspaceId, { String? dueDate, String? facilitator, List<String>? measureTimes, int? page, int? pageSize, List<String>? pic, String? query, String? sort, String? status, List<String>? tagIds, }) async {
    final response = await workspacesWorkspaceIdTemplatesTasksGetWithHttpInfo(workspaceId,  dueDate: dueDate, facilitator: facilitator, measureTimes: measureTimes, page: page, pageSize: pageSize, pic: pic, query: query, sort: sort, status: status, tagIds: tagIds, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetTemplatesResponse',) as DtoGetTemplatesResponse;
    
    }
    return null;
  }

  /// Delete Background
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  Future<Response> workspacesWorkspaceIdTemplatesTemplateIdBackgroundSignedUrlDeleteWithHttpInfo(String workspaceId, String templateId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/{template_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{template_id}', templateId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Background
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  Future<void> workspacesWorkspaceIdTemplatesTemplateIdBackgroundSignedUrlDelete(String workspaceId, String templateId,) async {
    final response = await workspacesWorkspaceIdTemplatesTemplateIdBackgroundSignedUrlDeleteWithHttpInfo(workspaceId, templateId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create Background signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<Response> workspacesWorkspaceIdTemplatesTemplateIdBackgroundSignedUrlPostWithHttpInfo(String workspaceId, String templateId, String method,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/{template_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{template_id}', templateId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Background signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdTemplatesTemplateIdBackgroundSignedUrlPost(String workspaceId, String templateId, String method,) async {
    final response = await workspacesWorkspaceIdTemplatesTemplateIdBackgroundSignedUrlPostWithHttpInfo(workspaceId, templateId, method,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Delete Template
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  Future<Response> workspacesWorkspaceIdTemplatesTemplateIdDeleteWithHttpInfo(String workspaceId, String templateId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/{template_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{template_id}', templateId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Template
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  Future<void> workspacesWorkspaceIdTemplatesTemplateIdDelete(String workspaceId, String templateId,) async {
    final response = await workspacesWorkspaceIdTemplatesTemplateIdDeleteWithHttpInfo(workspaceId, templateId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get Template
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  ///
  /// * [String] requestPage:
  Future<Response> workspacesWorkspaceIdTemplatesTemplateIdGetWithHttpInfo(String workspaceId, String templateId, { String? requestPage, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/{template_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{template_id}', templateId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (requestPage != null) {
      queryParams.addAll(_queryParams('', 'request_page', requestPage));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Template
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  ///
  /// * [String] requestPage:
  Future<DtoTemplateResponse?> workspacesWorkspaceIdTemplatesTemplateIdGet(String workspaceId, String templateId, { String? requestPage, }) async {
    final response = await workspacesWorkspaceIdTemplatesTemplateIdGetWithHttpInfo(workspaceId, templateId,  requestPage: requestPage, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTemplateResponse',) as DtoTemplateResponse;
    
    }
    return null;
  }

  /// Update Template
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  ///
  /// * [DtoPatchTemplateRequest] body (required):
  ///   Template request object
  Future<Response> workspacesWorkspaceIdTemplatesTemplateIdPatchWithHttpInfo(String workspaceId, String templateId, DtoPatchTemplateRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/templates/{template_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{template_id}', templateId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Template
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] templateId (required):
  ///   Template ID
  ///
  /// * [DtoPatchTemplateRequest] body (required):
  ///   Template request object
  Future<DtoTemplateResponse?> workspacesWorkspaceIdTemplatesTemplateIdPatch(String workspaceId, String templateId, DtoPatchTemplateRequest body,) async {
    final response = await workspacesWorkspaceIdTemplatesTemplateIdPatchWithHttpInfo(workspaceId, templateId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoTemplateResponse',) as DtoTemplateResponse;
    
    }
    return null;
  }
}
