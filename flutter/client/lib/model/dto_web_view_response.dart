//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoWebViewResponse {
  /// Returns a new [DtoWebViewResponse] instance.
  DtoWebViewResponse({
    this.chatRoomsResponse = const [],
    this.cors,
    required this.endpointUrl,
    this.passUserIdDuringLogin,
    this.secret,
  });

  List<DtoChatRoomResponse> chatRoomsResponse;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cors;

  String endpointUrl;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? passUserIdDuringLogin;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secret;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoWebViewResponse &&
     other.chatRoomsResponse == chatRoomsResponse &&
     other.cors == cors &&
     other.endpointUrl == endpointUrl &&
     other.passUserIdDuringLogin == passUserIdDuringLogin &&
     other.secret == secret;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (chatRoomsResponse.hashCode) +
    (cors == null ? 0 : cors!.hashCode) +
    (endpointUrl.hashCode) +
    (passUserIdDuringLogin == null ? 0 : passUserIdDuringLogin!.hashCode) +
    (secret == null ? 0 : secret!.hashCode);

  @override
  String toString() => 'DtoWebViewResponse[chatRoomsResponse=$chatRoomsResponse, cors=$cors, endpointUrl=$endpointUrl, passUserIdDuringLogin=$passUserIdDuringLogin, secret=$secret]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'chat_rooms_response'] = this.chatRoomsResponse;
    if (this.cors != null) {
      json[r'cors'] = this.cors;
    } else {
      json[r'cors'] = null;
    }
      json[r'endpoint_url'] = this.endpointUrl;
    if (this.passUserIdDuringLogin != null) {
      json[r'pass_user_id_during_login'] = this.passUserIdDuringLogin;
    } else {
      json[r'pass_user_id_during_login'] = null;
    }
    if (this.secret != null) {
      json[r'secret'] = this.secret;
    } else {
      json[r'secret'] = null;
    }
    return json;
  }

  /// Returns a new [DtoWebViewResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoWebViewResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoWebViewResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoWebViewResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoWebViewResponse(
        chatRoomsResponse: DtoChatRoomResponse.listFromJson(json[r'chat_rooms_response']) ?? const [],
        cors: mapValueOfType<String>(json, r'cors'),
        endpointUrl: mapValueOfType<String>(json, r'endpoint_url')!,
        passUserIdDuringLogin: mapValueOfType<bool>(json, r'pass_user_id_during_login'),
        secret: mapValueOfType<String>(json, r'secret'),
      );
    }
    return null;
  }

  static List<DtoWebViewResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoWebViewResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoWebViewResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoWebViewResponse> mapFromJson(dynamic json) {
    final map = <String, DtoWebViewResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoWebViewResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoWebViewResponse-objects as value to a dart map
  static Map<String, List<DtoWebViewResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoWebViewResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoWebViewResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'endpoint_url',
  };
}

