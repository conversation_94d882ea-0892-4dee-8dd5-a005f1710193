package database

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"clipcrow/essentialworkware/model/dto"
)

// ExampleBatchUsage demonstrates how to use the new GetDatabasesResponse method
// instead of making individual GetDatabaseResponse calls in a loop
func ExampleBatchUsage(s *databaseService, ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, databaseIDs []uuid.UUID) {

	// OLD WAY - Multiple individual database queries (inefficient)
	// This approach makes N database round trips where N = len(databaseIDs)
	fmt.Println("=== OLD WAY (Inefficient) ===")
	oldResponses := []dto.DatabaseResponse{}
	for _, databaseID := range databaseIDs {
		response, err := s.GetDatabaseResponse(ctx, requesterID, databaseID)
		if err != nil {
			fmt.Printf("Error getting database %s: %v\n", databaseID, err)
			continue
		}
		oldResponses = append(oldResponses, response)
	}
	fmt.Printf("Old way: Made %d individual database queries\n", len(databaseIDs))

	// NEW WAY - Single batch query (efficient)
	// This approach makes 1 database round trip regardless of len(databaseIDs)
	fmt.Println("\n=== NEW WAY (Efficient) ===")
	newResponses, err := s.GetDatabasesResponse(ctx, workspaceID, requesterID, databaseIDs)
	if err != nil {
		fmt.Printf("Error getting databases in batch: %v\n", err)
		return
	}
	fmt.Printf("New way: Made 1 batch database query for %d databases\n", len(newResponses))

	// Performance comparison
	fmt.Println("\n=== PERFORMANCE COMPARISON ===")
	fmt.Printf("Database IDs requested: %d\n", len(databaseIDs))
	fmt.Printf("Old method queries: %d (1 per database)\n", len(databaseIDs))
	fmt.Printf("New method queries: 1 (batch query)\n")
	if len(databaseIDs) > 1 {
		fmt.Printf("Performance improvement: %dx fewer database round trips\n", len(databaseIDs))
	}
}

// RefactoredFindByWorkspaceID shows how the original method was refactored
// to use the batch approach
func RefactoredFindByWorkspaceID() {
	fmt.Println(`
=== REFACTORING EXAMPLE ===

BEFORE (in FindByWorkspaceID method):
	response := dto.DatabasesResponse{}
	for idx := range databases {
		idx := idx
		databaseR, err := s.GetDatabaseResponse(ctx, requesterID, databases[idx].ID)
		if err != nil {
			s.c.GetLogger().WithContext(ctx).Error("error GetDatabaseResponse", logger.FieldMap{logger.ErrorKey: err})
			continue
		}
		response.Items = append(response.Items, &databaseR)
	}

AFTER (optimized version):
	// Extract database IDs for batch processing
	databaseIDs := make([]uuid.UUID, len(databases))
	for i, db := range databases {
		databaseIDs[i] = db.ID
	}

	// Use batch method to get all database responses
	databaseResponses, err := s.GetDatabasesResponse(ctx, workspaceID, requesterID, databaseIDs)
	if err != nil {
		return dto.DatabasesResponse{}, err
	}

	// Build the final response
	response := dto.DatabasesResponse{}
	for i := range databaseResponses {
		response.Items = append(response.Items, &databaseResponses[i])
	}

BENEFITS:
- Reduces database round trips from N to 1 (where N = number of databases)
- Improves performance especially when fetching many databases
- Maintains the same functionality and data structure
- Better resource utilization and reduced latency`)
}
