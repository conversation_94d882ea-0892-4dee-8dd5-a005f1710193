//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoScope {
  /// Returns a new [DtoScope] instance.
  DtoScope({
    this.externalLinkIds = const [],
    this.memberIds = const [],
    this.roleTags = const [],
    this.tagIds = const [],
  });

  List<String> externalLinkIds;

  List<String> memberIds;

  List<String> roleTags;

  List<String> tagIds;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoScope &&
     other.externalLinkIds == externalLinkIds &&
     other.memberIds == memberIds &&
     other.roleTags == roleTags &&
     other.tagIds == tagIds;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (externalLinkIds.hashCode) +
    (memberIds.hashCode) +
    (roleTags.hashCode) +
    (tagIds.hashCode);

  @override
  String toString() => 'DtoScope[externalLinkIds=$externalLinkIds, memberIds=$memberIds, roleTags=$roleTags, tagIds=$tagIds]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'external_link_ids'] = this.externalLinkIds;
      json[r'member_ids'] = this.memberIds;
      json[r'role_tags'] = this.roleTags;
      json[r'tag_ids'] = this.tagIds;
    return json;
  }

  /// Returns a new [DtoScope] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoScope? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoScope[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoScope[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoScope(
        externalLinkIds: json[r'external_link_ids'] is List
            ? (json[r'external_link_ids'] as List).cast<String>()
            : const [],
        memberIds: json[r'member_ids'] is List
            ? (json[r'member_ids'] as List).cast<String>()
            : const [],
        roleTags: json[r'role_tags'] is List
            ? (json[r'role_tags'] as List).cast<String>()
            : const [],
        tagIds: json[r'tag_ids'] is List
            ? (json[r'tag_ids'] as List).cast<String>()
            : const [],
      );
    }
    return null;
  }

  static List<DtoScope>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoScope>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoScope.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoScope> mapFromJson(dynamic json) {
    final map = <String, DtoScope>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoScope.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoScope-objects as value to a dart map
  static Map<String, List<DtoScope>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoScope>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoScope.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

