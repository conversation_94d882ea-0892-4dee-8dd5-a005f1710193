//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class MapsApi {
  MapsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get Geocode
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] latitude (required):
  ///
  /// * [String] longitude (required):
  ///
  /// * [String] language:
  Future<Response> mapsGeocodeGetWithHttpInfo(String latitude, String longitude, { String? language, }) async {
    // ignore: prefer_const_declarations
    final path = r'/maps/geocode';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
      queryParams.addAll(_queryParams('', 'latitude', latitude));
      queryParams.addAll(_queryParams('', 'longitude', longitude));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Geocode
  ///
  /// Parameters:
  ///
  /// * [String] latitude (required):
  ///
  /// * [String] longitude (required):
  ///
  /// * [String] language:
  Future<DtoGetMapsGeocodeResponse?> mapsGeocodeGet(String latitude, String longitude, { String? language, }) async {
    final response = await mapsGeocodeGetWithHttpInfo(latitude, longitude,  language: language, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMapsGeocodeResponse',) as DtoGetMapsGeocodeResponse;
    
    }
    return null;
  }

  /// Maps API - PlacesSearch
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] latitude (required):
  ///
  /// * [String] longitude (required):
  ///
  /// * [String] query (required):
  ///
  /// * [String] language:
  Future<Response> mapsPlacesSearchGetWithHttpInfo(String latitude, String longitude, String query, { String? language, }) async {
    // ignore: prefer_const_declarations
    final path = r'/maps/places-search';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (language != null) {
      queryParams.addAll(_queryParams('', 'language', language));
    }
      queryParams.addAll(_queryParams('', 'latitude', latitude));
      queryParams.addAll(_queryParams('', 'longitude', longitude));
      queryParams.addAll(_queryParams('', 'query', query));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Maps API - PlacesSearch
  ///
  /// Parameters:
  ///
  /// * [String] latitude (required):
  ///
  /// * [String] longitude (required):
  ///
  /// * [String] query (required):
  ///
  /// * [String] language:
  Future<DtoGetMapsPlacesSearchResponse?> mapsPlacesSearchGet(String latitude, String longitude, String query, { String? language, }) async {
    final response = await mapsPlacesSearchGetWithHttpInfo(latitude, longitude, query,  language: language, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMapsPlacesSearchResponse',) as DtoGetMapsPlacesSearchResponse;
    
    }
    return null;
  }
}
