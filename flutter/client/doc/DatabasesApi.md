# openapi.api.DatabasesApi

## Load the API package
```dart
import 'package:openapi/api.dart';
```

All URIs are relative to */api/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**workspacesWorkspaceIdDatabasesDatabaseIdDelete**](DatabasesApi.md#workspacesworkspaceiddatabasesdatabaseiddelete) | **DELETE** /workspaces/{workspace_id}/databases/{database_id} | Delete Database
[**workspacesWorkspaceIdDatabasesDatabaseIdGet**](DatabasesApi.md#workspacesworkspaceiddatabasesdatabaseidget) | **GET** /workspaces/{workspace_id}/databases/{database_id} | Get Database
[**workspacesWorkspaceIdDatabasesDatabaseIdPatch**](DatabasesApi.md#workspacesworkspaceiddatabasesdatabaseidpatch) | **PATCH** /workspaces/{workspace_id}/databases/{database_id} | Update Database
[**workspacesWorkspaceIdDatabasesMultiPositionsPut**](DatabasesApi.md#workspacesworkspaceiddatabasesmultipositionsput) | **PUT** /workspaces/{workspace_id}/databases/multi-positions | Update Database position
[**workspacesWorkspaceIdDatabasesMultiplePatch**](DatabasesApi.md#workspacesworkspaceiddatabasesmultiplepatch) | **PATCH** /workspaces/{workspace_id}/databases/multiple | Update databases
[**workspacesWorkspaceIdDatabasesPost**](DatabasesApi.md#workspacesworkspaceiddatabasespost) | **POST** /workspaces/{workspace_id}/databases | Post Database
[**workspacesWorkspaceIdDatabasesQueryPost**](DatabasesApi.md#workspacesworkspaceiddatabasesquerypost) | **POST** /workspaces/{workspace_id}/databases/query | Get Database


# **workspacesWorkspaceIdDatabasesDatabaseIdDelete**
> workspacesWorkspaceIdDatabasesDatabaseIdDelete(workspaceId, databaseId)

Delete Database

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final databaseId = databaseId_example; // String | DataBase ID

try {
    api_instance.workspacesWorkspaceIdDatabasesDatabaseIdDelete(workspaceId, databaseId);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesDatabaseIdDelete: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **databaseId** | **String**| DataBase ID | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdDatabasesDatabaseIdGet**
> DtoDatabaseResponse workspacesWorkspaceIdDatabasesDatabaseIdGet(workspaceId, databaseId)

Get Database

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final databaseId = databaseId_example; // String | Database ID

try {
    final result = api_instance.workspacesWorkspaceIdDatabasesDatabaseIdGet(workspaceId, databaseId);
    print(result);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesDatabaseIdGet: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **databaseId** | **String**| Database ID | 

### Return type

[**DtoDatabaseResponse**](DtoDatabaseResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdDatabasesDatabaseIdPatch**
> DtoDatabaseResponse workspacesWorkspaceIdDatabasesDatabaseIdPatch(workspaceId, databaseId, body)

Update Database

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final databaseId = databaseId_example; // String | Database ID
final body = DtoPatchDatabaseRequest(); // DtoPatchDatabaseRequest | Database Update object

try {
    final result = api_instance.workspacesWorkspaceIdDatabasesDatabaseIdPatch(workspaceId, databaseId, body);
    print(result);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesDatabaseIdPatch: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **databaseId** | **String**| Database ID | 
 **body** | [**DtoPatchDatabaseRequest**](DtoPatchDatabaseRequest.md)| Database Update object | 

### Return type

[**DtoDatabaseResponse**](DtoDatabaseResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdDatabasesMultiPositionsPut**
> workspacesWorkspaceIdDatabasesMultiPositionsPut(workspaceId, body)

Update Database position

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final body = DtoPutDatabasePositionRequest(); // DtoPutDatabasePositionRequest | DatabasePosition Update object

try {
    api_instance.workspacesWorkspaceIdDatabasesMultiPositionsPut(workspaceId, body);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesMultiPositionsPut: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **body** | [**DtoPutDatabasePositionRequest**](DtoPutDatabasePositionRequest.md)| DatabasePosition Update object | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdDatabasesMultiplePatch**
> DtoDatabasesResponse workspacesWorkspaceIdDatabasesMultiplePatch(workspaceId, body)

Update databases

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final body = [List<DtoPatchDatabaseRequest>()]; // List<DtoPatchDatabaseRequest> | Databases Update object

try {
    final result = api_instance.workspacesWorkspaceIdDatabasesMultiplePatch(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesMultiplePatch: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **body** | [**List<DtoPatchDatabaseRequest>**](DtoPatchDatabaseRequest.md)| Databases Update object | 

### Return type

[**DtoDatabasesResponse**](DtoDatabasesResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdDatabasesPost**
> DtoDatabaseResponse workspacesWorkspaceIdDatabasesPost(workspaceId, body)

Post Database

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final body = DtoPostDatabaseRequest(); // DtoPostDatabaseRequest | Post database Request

try {
    final result = api_instance.workspacesWorkspaceIdDatabasesPost(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **body** | [**DtoPostDatabaseRequest**](DtoPostDatabaseRequest.md)| Post database Request | 

### Return type

[**DtoDatabaseResponse**](DtoDatabaseResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **workspacesWorkspaceIdDatabasesQueryPost**
> DtoDatabasesResponse workspacesWorkspaceIdDatabasesQueryPost(workspaceId, body)

Get Database

### Example
```dart
import 'package:openapi/api.dart';

final api_instance = DatabasesApi();
final workspaceId = workspaceId_example; // String | Workspace ID
final body = DtoGetDatabasesQueryParams(); // DtoGetDatabasesQueryParams | Databases body view manager request

try {
    final result = api_instance.workspacesWorkspaceIdDatabasesQueryPost(workspaceId, body);
    print(result);
} catch (e) {
    print('Exception when calling DatabasesApi->workspacesWorkspaceIdDatabasesQueryPost: $e\n');
}
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **workspaceId** | **String**| Workspace ID | 
 **body** | [**DtoGetDatabasesQueryParams**](DtoGetDatabasesQueryParams.md)| Databases body view manager request | 

### Return type

[**DtoDatabasesResponse**](DtoDatabasesResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

