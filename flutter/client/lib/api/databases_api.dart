//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class DatabasesApi {
  DatabasesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Delete Database
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] databaseId (required):
  ///   DataBase ID
  Future<Response> workspacesWorkspaceIdDatabasesDatabaseIdDeleteWithHttpInfo(String workspaceId, String databaseId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases/{database_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{database_id}', databaseId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Database
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] databaseId (required):
  ///   DataBase ID
  Future<void> workspacesWorkspaceIdDatabasesDatabaseIdDelete(String workspaceId, String databaseId,) async {
    final response = await workspacesWorkspaceIdDatabasesDatabaseIdDeleteWithHttpInfo(workspaceId, databaseId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get Database
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] databaseId (required):
  ///   Database ID
  Future<Response> workspacesWorkspaceIdDatabasesDatabaseIdGetWithHttpInfo(String workspaceId, String databaseId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases/{database_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{database_id}', databaseId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Database
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] databaseId (required):
  ///   Database ID
  Future<DtoDatabaseResponse?> workspacesWorkspaceIdDatabasesDatabaseIdGet(String workspaceId, String databaseId,) async {
    final response = await workspacesWorkspaceIdDatabasesDatabaseIdGetWithHttpInfo(workspaceId, databaseId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoDatabaseResponse',) as DtoDatabaseResponse;
    
    }
    return null;
  }

  /// Update Database
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] databaseId (required):
  ///   Database ID
  ///
  /// * [DtoPatchDatabaseRequest] body (required):
  ///   Database Update object
  Future<Response> workspacesWorkspaceIdDatabasesDatabaseIdPatchWithHttpInfo(String workspaceId, String databaseId, DtoPatchDatabaseRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases/{database_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{database_id}', databaseId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Database
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] databaseId (required):
  ///   Database ID
  ///
  /// * [DtoPatchDatabaseRequest] body (required):
  ///   Database Update object
  Future<DtoDatabaseResponse?> workspacesWorkspaceIdDatabasesDatabaseIdPatch(String workspaceId, String databaseId, DtoPatchDatabaseRequest body,) async {
    final response = await workspacesWorkspaceIdDatabasesDatabaseIdPatchWithHttpInfo(workspaceId, databaseId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoDatabaseResponse',) as DtoDatabaseResponse;
    
    }
    return null;
  }

  /// Update Database position
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPutDatabasePositionRequest] body (required):
  ///   DatabasePosition Update object
  Future<Response> workspacesWorkspaceIdDatabasesMultiPositionsPutWithHttpInfo(String workspaceId, DtoPutDatabasePositionRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases/multi-positions'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Database position
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPutDatabasePositionRequest] body (required):
  ///   DatabasePosition Update object
  Future<void> workspacesWorkspaceIdDatabasesMultiPositionsPut(String workspaceId, DtoPutDatabasePositionRequest body,) async {
    final response = await workspacesWorkspaceIdDatabasesMultiPositionsPutWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Update databases
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [List<DtoPatchDatabaseRequest>] body (required):
  ///   Databases Update object
  Future<Response> workspacesWorkspaceIdDatabasesMultiplePatchWithHttpInfo(String workspaceId, List<DtoPatchDatabaseRequest> body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases/multiple'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update databases
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [List<DtoPatchDatabaseRequest>] body (required):
  ///   Databases Update object
  Future<DtoDatabasesResponse?> workspacesWorkspaceIdDatabasesMultiplePatch(String workspaceId, List<DtoPatchDatabaseRequest> body,) async {
    final response = await workspacesWorkspaceIdDatabasesMultiplePatchWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoDatabasesResponse',) as DtoDatabasesResponse;
    
    }
    return null;
  }

  /// Post Database
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostDatabaseRequest] body (required):
  ///   Post database Request
  Future<Response> workspacesWorkspaceIdDatabasesPostWithHttpInfo(String workspaceId, DtoPostDatabaseRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Post Database
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostDatabaseRequest] body (required):
  ///   Post database Request
  Future<DtoDatabaseResponse?> workspacesWorkspaceIdDatabasesPost(String workspaceId, DtoPostDatabaseRequest body,) async {
    final response = await workspacesWorkspaceIdDatabasesPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoDatabaseResponse',) as DtoDatabaseResponse;
    
    }
    return null;
  }

  /// Get Database
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetDatabasesQueryParams] body (required):
  ///   Databases body view manager request
  Future<Response> workspacesWorkspaceIdDatabasesQueryPostWithHttpInfo(String workspaceId, DtoGetDatabasesQueryParams body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/databases/query'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Database
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetDatabasesQueryParams] body (required):
  ///   Databases body view manager request
  Future<DtoDatabasesResponse?> workspacesWorkspaceIdDatabasesQueryPost(String workspaceId, DtoGetDatabasesQueryParams body,) async {
    final response = await workspacesWorkspaceIdDatabasesQueryPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoDatabasesResponse',) as DtoDatabasesResponse;
    
    }
    return null;
  }
}
