//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class UnsplashApi {
  UnsplashApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Unsplash
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] linkDownloadLocation:
  ///
  /// * [String] page:
  ///
  /// * [String] query:
  Future<Response> unsplashGetWithHttpInfo({ String? linkDownloadLocation, String? page, String? query, }) async {
    // ignore: prefer_const_declarations
    final path = r'/unsplash';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (linkDownloadLocation != null) {
      queryParams.addAll(_queryParams('', 'link_download_location', linkDownloadLocation));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Unsplash
  ///
  /// Parameters:
  ///
  /// * [String] linkDownloadLocation:
  ///
  /// * [String] page:
  ///
  /// * [String] query:
  Future<String?> unsplashGet({ String? linkDownloadLocation, String? page, String? query, }) async {
    final response = await unsplashGetWithHttpInfo( linkDownloadLocation: linkDownloadLocation, page: page, query: query, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Unsplash
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] linkDownloadLocation:
  ///
  /// * [String] page:
  ///
  /// * [String] query:
  Future<Response> unsplashSearchGetWithHttpInfo({ String? linkDownloadLocation, String? page, String? query, }) async {
    // ignore: prefer_const_declarations
    final path = r'/unsplash/search';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (linkDownloadLocation != null) {
      queryParams.addAll(_queryParams('', 'link_download_location', linkDownloadLocation));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Unsplash
  ///
  /// Parameters:
  ///
  /// * [String] linkDownloadLocation:
  ///
  /// * [String] page:
  ///
  /// * [String] query:
  Future<String?> unsplashSearchGet({ String? linkDownloadLocation, String? page, String? query, }) async {
    final response = await unsplashSearchGetWithHttpInfo( linkDownloadLocation: linkDownloadLocation, page: page, query: query, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Unsplash track download location
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] linkDownloadLocation:
  ///
  /// * [String] page:
  ///
  /// * [String] query:
  Future<Response> unsplashTrackDownloadGetWithHttpInfo({ String? linkDownloadLocation, String? page, String? query, }) async {
    // ignore: prefer_const_declarations
    final path = r'/unsplash/track-download';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (linkDownloadLocation != null) {
      queryParams.addAll(_queryParams('', 'link_download_location', linkDownloadLocation));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Unsplash track download location
  ///
  /// Parameters:
  ///
  /// * [String] linkDownloadLocation:
  ///
  /// * [String] page:
  ///
  /// * [String] query:
  Future<String?> unsplashTrackDownloadGet({ String? linkDownloadLocation, String? page, String? query, }) async {
    final response = await unsplashTrackDownloadGetWithHttpInfo( linkDownloadLocation: linkDownloadLocation, page: page, query: query, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }
}
