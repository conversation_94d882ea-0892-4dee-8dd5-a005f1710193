//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagMention {
  /// Returns a new [DtoTagMention] instance.
  DtoTagMention({
    required this.id,
    required this.value,
  });

  String id;

  String value;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagMention &&
     other.id == id &&
     other.value == value;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id.hashCode) +
    (value.hashCode);

  @override
  String toString() => 'DtoTagMention[id=$id, value=$value]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'id'] = this.id;
      json[r'value'] = this.value;
    return json;
  }

  /// Returns a new [DtoTagMention] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagMention? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagMention[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagMention[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagMention(
        id: mapValueOfType<String>(json, r'id')!,
        value: mapValueOfType<String>(json, r'value')!,
      );
    }
    return null;
  }

  static List<DtoTagMention>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagMention>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagMention.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagMention> mapFromJson(dynamic json) {
    final map = <String, DtoTagMention>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagMention.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagMention-objects as value to a dart map
  static Map<String, List<DtoTagMention>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagMention>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagMention.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
    'value',
  };
}

