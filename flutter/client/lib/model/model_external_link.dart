//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelExternalLink {
  /// Returns a new [ModelExternalLink] instance.
  ModelExternalLink({
    this.background,
    this.backgroundKey,
    required this.createdAt,
    this.creator,
    this.creatorId,
    this.deletedAt,
    this.description,
    this.formItems = const [],
    this.icon,
    this.iconKey,
    required this.id,
    this.illustration,
    required this.name,
    this.oAuth,
    required this.oAuthEnabled,
    this.oAuthId,
    this.opacity,
    this.primaryColor,
    this.reverseBackground,
    this.secondaryColor,
    this.status,
    this.tagNumber,
    this.textAlignment,
    this.textColor,
    required this.updatedAt,
    this.webhook,
    required this.webhookEnabled,
    this.webhookId,
    required this.workspaceId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelMember? creator;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? creatorId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  List<ModelFormItem> formItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? icon;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? iconKey;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  String name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelOAuth? oAuth;

  bool oAuthEnabled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? oAuthId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  ModelExternalLinkStatusEnum? status;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  ModelExternalLinkTextColorEnum? textColor;

  String updatedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelWebhook? webhook;

  bool webhookEnabled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? webhookId;

  String workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelExternalLink &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.createdAt == createdAt &&
     other.creator == creator &&
     other.creatorId == creatorId &&
     other.deletedAt == deletedAt &&
     other.description == description &&
     other.formItems == formItems &&
     other.icon == icon &&
     other.iconKey == iconKey &&
     other.id == id &&
     other.illustration == illustration &&
     other.name == name &&
     other.oAuth == oAuth &&
     other.oAuthEnabled == oAuthEnabled &&
     other.oAuthId == oAuthId &&
     other.opacity == opacity &&
     other.primaryColor == primaryColor &&
     other.reverseBackground == reverseBackground &&
     other.secondaryColor == secondaryColor &&
     other.status == status &&
     other.tagNumber == tagNumber &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.updatedAt == updatedAt &&
     other.webhook == webhook &&
     other.webhookEnabled == webhookEnabled &&
     other.webhookId == webhookId &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (createdAt.hashCode) +
    (creator == null ? 0 : creator!.hashCode) +
    (creatorId == null ? 0 : creatorId!.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (formItems.hashCode) +
    (icon == null ? 0 : icon!.hashCode) +
    (iconKey == null ? 0 : iconKey!.hashCode) +
    (id.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (name.hashCode) +
    (oAuth == null ? 0 : oAuth!.hashCode) +
    (oAuthEnabled.hashCode) +
    (oAuthId == null ? 0 : oAuthId!.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (updatedAt.hashCode) +
    (webhook == null ? 0 : webhook!.hashCode) +
    (webhookEnabled.hashCode) +
    (webhookId == null ? 0 : webhookId!.hashCode) +
    (workspaceId.hashCode);

  @override
  String toString() => 'ModelExternalLink[background=$background, backgroundKey=$backgroundKey, createdAt=$createdAt, creator=$creator, creatorId=$creatorId, deletedAt=$deletedAt, description=$description, formItems=$formItems, icon=$icon, iconKey=$iconKey, id=$id, illustration=$illustration, name=$name, oAuth=$oAuth, oAuthEnabled=$oAuthEnabled, oAuthId=$oAuthId, opacity=$opacity, primaryColor=$primaryColor, reverseBackground=$reverseBackground, secondaryColor=$secondaryColor, status=$status, tagNumber=$tagNumber, textAlignment=$textAlignment, textColor=$textColor, updatedAt=$updatedAt, webhook=$webhook, webhookEnabled=$webhookEnabled, webhookId=$webhookId, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
      json[r'created_at'] = this.createdAt;
    if (this.creator != null) {
      json[r'creator'] = this.creator;
    } else {
      json[r'creator'] = null;
    }
    if (this.creatorId != null) {
      json[r'creator_id'] = this.creatorId;
    } else {
      json[r'creator_id'] = null;
    }
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'form_items'] = this.formItems;
    if (this.icon != null) {
      json[r'icon'] = this.icon;
    } else {
      json[r'icon'] = null;
    }
    if (this.iconKey != null) {
      json[r'icon_key'] = this.iconKey;
    } else {
      json[r'icon_key'] = null;
    }
      json[r'id'] = this.id;
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
      json[r'name'] = this.name;
    if (this.oAuth != null) {
      json[r'o_auth'] = this.oAuth;
    } else {
      json[r'o_auth'] = null;
    }
      json[r'o_auth_enabled'] = this.oAuthEnabled;
    if (this.oAuthId != null) {
      json[r'o_auth_id'] = this.oAuthId;
    } else {
      json[r'o_auth_id'] = null;
    }
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
    if (this.webhook != null) {
      json[r'webhook'] = this.webhook;
    } else {
      json[r'webhook'] = null;
    }
      json[r'webhook_enabled'] = this.webhookEnabled;
    if (this.webhookId != null) {
      json[r'webhook_id'] = this.webhookId;
    } else {
      json[r'webhook_id'] = null;
    }
      json[r'workspace_id'] = this.workspaceId;
    return json;
  }

  /// Returns a new [ModelExternalLink] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelExternalLink? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelExternalLink[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelExternalLink[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelExternalLink(
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        creator: ModelMember.fromJson(json[r'creator']),
        creatorId: mapValueOfType<String>(json, r'creator_id'),
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        description: mapValueOfType<String>(json, r'description'),
        formItems: ModelFormItem.listFromJson(json[r'form_items']) ?? const [],
        icon: mapValueOfType<String>(json, r'icon'),
        iconKey: mapValueOfType<String>(json, r'icon_key'),
        id: mapValueOfType<String>(json, r'id')!,
        illustration: mapValueOfType<String>(json, r'illustration'),
        name: mapValueOfType<String>(json, r'name')!,
        oAuth: ModelOAuth.fromJson(json[r'o_auth']),
        oAuthEnabled: mapValueOfType<bool>(json, r'o_auth_enabled')!,
        oAuthId: mapValueOfType<String>(json, r'o_auth_id'),
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        status: ModelExternalLinkStatusEnum.fromJson(json[r'status']),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: ModelExternalLinkTextColorEnum.fromJson(json[r'text_color']),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        webhook: ModelWebhook.fromJson(json[r'webhook']),
        webhookEnabled: mapValueOfType<bool>(json, r'webhook_enabled')!,
        webhookId: mapValueOfType<String>(json, r'webhook_id'),
        workspaceId: mapValueOfType<String>(json, r'workspace_id')!,
      );
    }
    return null;
  }

  static List<ModelExternalLink>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelExternalLink>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelExternalLink.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelExternalLink> mapFromJson(dynamic json) {
    final map = <String, ModelExternalLink>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelExternalLink.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelExternalLink-objects as value to a dart map
  static Map<String, List<ModelExternalLink>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelExternalLink>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelExternalLink.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'id',
    'name',
    'o_auth_enabled',
    'updated_at',
    'webhook_enabled',
    'workspace_id',
  };
}


class ModelExternalLinkStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelExternalLinkStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const ACTIVE = ModelExternalLinkStatusEnum._(r'ACTIVE');
  static const SUSPENDED = ModelExternalLinkStatusEnum._(r'SUSPENDED');

  /// List of all possible values in this [enum][ModelExternalLinkStatusEnum].
  static const values = <ModelExternalLinkStatusEnum>[
    ACTIVE,
    SUSPENDED,
  ];

  static ModelExternalLinkStatusEnum? fromJson(dynamic value) => ModelExternalLinkStatusEnumTypeTransformer().decode(value);

  static List<ModelExternalLinkStatusEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelExternalLinkStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelExternalLinkStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelExternalLinkStatusEnum] to String,
/// and [decode] dynamic data back to [ModelExternalLinkStatusEnum].
class ModelExternalLinkStatusEnumTypeTransformer {
  factory ModelExternalLinkStatusEnumTypeTransformer() => _instance ??= const ModelExternalLinkStatusEnumTypeTransformer._();

  const ModelExternalLinkStatusEnumTypeTransformer._();

  String encode(ModelExternalLinkStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelExternalLinkStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelExternalLinkStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'ACTIVE': return ModelExternalLinkStatusEnum.ACTIVE;
        case r'SUSPENDED': return ModelExternalLinkStatusEnum.SUSPENDED;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelExternalLinkStatusEnumTypeTransformer] instance.
  static ModelExternalLinkStatusEnumTypeTransformer? _instance;
}



class ModelExternalLinkTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelExternalLinkTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = ModelExternalLinkTextColorEnum._(r'DARK');
  static const LIGHT = ModelExternalLinkTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][ModelExternalLinkTextColorEnum].
  static const values = <ModelExternalLinkTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static ModelExternalLinkTextColorEnum? fromJson(dynamic value) => ModelExternalLinkTextColorEnumTypeTransformer().decode(value);

  static List<ModelExternalLinkTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelExternalLinkTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelExternalLinkTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelExternalLinkTextColorEnum] to String,
/// and [decode] dynamic data back to [ModelExternalLinkTextColorEnum].
class ModelExternalLinkTextColorEnumTypeTransformer {
  factory ModelExternalLinkTextColorEnumTypeTransformer() => _instance ??= const ModelExternalLinkTextColorEnumTypeTransformer._();

  const ModelExternalLinkTextColorEnumTypeTransformer._();

  String encode(ModelExternalLinkTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelExternalLinkTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelExternalLinkTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return ModelExternalLinkTextColorEnum.DARK;
        case r'LIGHT': return ModelExternalLinkTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelExternalLinkTextColorEnumTypeTransformer] instance.
  static ModelExternalLinkTextColorEnumTypeTransformer? _instance;
}


