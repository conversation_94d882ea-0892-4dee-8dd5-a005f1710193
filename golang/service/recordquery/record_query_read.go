package recordquery

import (
	"context"

	"github.com/google/uuid"

	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
)

func (s *recordQueryService) FindByID(ctx context.Context, databaseID uuid.UUID) (dto.RecordQueryResponse, error) {
	db := s.c.GetRepository(ctx)
	recordQuery := model.RecordQuery{}
	recordQueryRes := dto.RecordQueryResponse{}
	err := db.Where("database_id = ?", databaseID).First(&recordQuery).Error
	if err != nil {
		return recordQueryRes, err
	}
	recordQueryRes.GetFrom(&recordQuery)
	return recordQueryRes, nil
}

// FindByIDs fetches multiple record queries in a single batch operation
func (s *recordQueryService) FindByIDs(ctx context.Context, databaseIDs []uuid.UUID) (map[uuid.UUID]dto.RecordQueryResponse, error) {
	if len(databaseIDs) == 0 {
		return make(map[uuid.UUID]dto.RecordQueryResponse), nil
	}

	db := s.c.GetRepository(ctx)
	recordQueries := []model.RecordQuery{}

	err := db.Where("database_id IN ?", databaseIDs).Find(&recordQueries).Error
	if err != nil {
		return nil, err
	}

	// Build result map
	result := make(map[uuid.UUID]dto.RecordQueryResponse)
	for _, recordQuery := range recordQueries {
		recordQueryRes := dto.RecordQueryResponse{}
		recordQueryRes.GetFrom(&recordQuery)
		result[recordQuery.DatabaseID] = recordQueryRes
	}

	return result, nil
}
