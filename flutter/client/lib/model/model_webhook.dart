//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelWebhook {
  /// Returns a new [ModelWebhook] instance.
  ModelWebhook({
    required this.createdAt,
    required this.creatorId,
    this.deletedAt,
    required this.id,
    this.isActive,
    this.secret,
    required this.updatedAt,
    required this.webhookUrl,
    required this.workspaceId,
  });

  String createdAt;

  String creatorId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isActive;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secret;

  String updatedAt;

  String webhookUrl;

  String workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelWebhook &&
     other.createdAt == createdAt &&
     other.creatorId == creatorId &&
     other.deletedAt == deletedAt &&
     other.id == id &&
     other.isActive == isActive &&
     other.secret == secret &&
     other.updatedAt == updatedAt &&
     other.webhookUrl == webhookUrl &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (createdAt.hashCode) +
    (creatorId.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (id.hashCode) +
    (isActive == null ? 0 : isActive!.hashCode) +
    (secret == null ? 0 : secret!.hashCode) +
    (updatedAt.hashCode) +
    (webhookUrl.hashCode) +
    (workspaceId.hashCode);

  @override
  String toString() => 'ModelWebhook[createdAt=$createdAt, creatorId=$creatorId, deletedAt=$deletedAt, id=$id, isActive=$isActive, secret=$secret, updatedAt=$updatedAt, webhookUrl=$webhookUrl, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'created_at'] = this.createdAt;
      json[r'creator_id'] = this.creatorId;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'id'] = this.id;
    if (this.isActive != null) {
      json[r'is_active'] = this.isActive;
    } else {
      json[r'is_active'] = null;
    }
    if (this.secret != null) {
      json[r'secret'] = this.secret;
    } else {
      json[r'secret'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
      json[r'webhook_url'] = this.webhookUrl;
      json[r'workspace_id'] = this.workspaceId;
    return json;
  }

  /// Returns a new [ModelWebhook] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelWebhook? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelWebhook[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelWebhook[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelWebhook(
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        creatorId: mapValueOfType<String>(json, r'creator_id')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        id: mapValueOfType<String>(json, r'id')!,
        isActive: mapValueOfType<bool>(json, r'is_active'),
        secret: mapValueOfType<String>(json, r'secret'),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        webhookUrl: mapValueOfType<String>(json, r'webhook_url')!,
        workspaceId: mapValueOfType<String>(json, r'workspace_id')!,
      );
    }
    return null;
  }

  static List<ModelWebhook>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelWebhook>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelWebhook.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelWebhook> mapFromJson(dynamic json) {
    final map = <String, ModelWebhook>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelWebhook.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelWebhook-objects as value to a dart map
  static Map<String, List<ModelWebhook>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelWebhook>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelWebhook.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'creator_id',
    'id',
    'updated_at',
    'webhook_url',
    'workspace_id',
  };
}

