//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class WebhookCard {
  /// Returns a new [WebhookCard] instance.
  WebhookCard({
    this.description,
    required this.id,
    required this.name,
    this.properties = const [],
    this.serialNo,
    required this.type,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  String id;

  String name;

  List<WebhookProperty> properties;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? serialNo;

  WebhookCardTypeEnum type;

  @override
  bool operator ==(Object other) => identical(this, other) || other is WebhookCard &&
     other.description == description &&
     other.id == id &&
     other.name == name &&
     other.properties == properties &&
     other.serialNo == serialNo &&
     other.type == type;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (description == null ? 0 : description!.hashCode) +
    (id.hashCode) +
    (name.hashCode) +
    (properties.hashCode) +
    (serialNo == null ? 0 : serialNo!.hashCode) +
    (type.hashCode);

  @override
  String toString() => 'WebhookCard[description=$description, id=$id, name=$name, properties=$properties, serialNo=$serialNo, type=$type]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'id'] = this.id;
      json[r'name'] = this.name;
      json[r'properties'] = this.properties;
    if (this.serialNo != null) {
      json[r'serial_no'] = this.serialNo;
    } else {
      json[r'serial_no'] = null;
    }
      json[r'type'] = this.type;
    return json;
  }

  /// Returns a new [WebhookCard] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static WebhookCard? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "WebhookCard[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "WebhookCard[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return WebhookCard(
        description: mapValueOfType<String>(json, r'description'),
        id: mapValueOfType<String>(json, r'id')!,
        name: mapValueOfType<String>(json, r'name')!,
        properties: WebhookProperty.listFromJson(json[r'properties']) ?? const [],
        serialNo: mapValueOfType<int>(json, r'serial_no'),
        type: WebhookCardTypeEnum.fromJson(json[r'type'])!,
      );
    }
    return null;
  }

  static List<WebhookCard>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <WebhookCard>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = WebhookCard.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, WebhookCard> mapFromJson(dynamic json) {
    final map = <String, WebhookCard>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = WebhookCard.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of WebhookCard-objects as value to a dart map
  static Map<String, List<WebhookCard>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<WebhookCard>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = WebhookCard.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
    'name',
    'type',
  };
}


class WebhookCardTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const WebhookCardTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const WORKSPACE = WebhookCardTypeEnum._(r'WORKSPACE');
  static const CARD = WebhookCardTypeEnum._(r'CARD');
  static const MANAGER = WebhookCardTypeEnum._(r'MANAGER');
  static const PARTNER = WebhookCardTypeEnum._(r'PARTNER');
  static const STAFF = WebhookCardTypeEnum._(r'STAFF');
  static const GUEST = WebhookCardTypeEnum._(r'GUEST');
  static const BOT = WebhookCardTypeEnum._(r'BOT');
  static const TEMPLATE = WebhookCardTypeEnum._(r'TEMPLATE');
  static const TAG = WebhookCardTypeEnum._(r'TAG');
  static const BROWSER = WebhookCardTypeEnum._(r'BROWSER');
  static const SETTING = WebhookCardTypeEnum._(r'SETTING');

  /// List of all possible values in this [enum][WebhookCardTypeEnum].
  static const values = <WebhookCardTypeEnum>[
    WORKSPACE,
    CARD,
    MANAGER,
    PARTNER,
    STAFF,
    GUEST,
    BOT,
    TEMPLATE,
    TAG,
    BROWSER,
    SETTING,
  ];

  static WebhookCardTypeEnum? fromJson(dynamic value) => WebhookCardTypeEnumTypeTransformer().decode(value);

  static List<WebhookCardTypeEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <WebhookCardTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = WebhookCardTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [WebhookCardTypeEnum] to String,
/// and [decode] dynamic data back to [WebhookCardTypeEnum].
class WebhookCardTypeEnumTypeTransformer {
  factory WebhookCardTypeEnumTypeTransformer() => _instance ??= const WebhookCardTypeEnumTypeTransformer._();

  const WebhookCardTypeEnumTypeTransformer._();

  String encode(WebhookCardTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a WebhookCardTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  WebhookCardTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'WORKSPACE': return WebhookCardTypeEnum.WORKSPACE;
        case r'CARD': return WebhookCardTypeEnum.CARD;
        case r'MANAGER': return WebhookCardTypeEnum.MANAGER;
        case r'PARTNER': return WebhookCardTypeEnum.PARTNER;
        case r'STAFF': return WebhookCardTypeEnum.STAFF;
        case r'GUEST': return WebhookCardTypeEnum.GUEST;
        case r'BOT': return WebhookCardTypeEnum.BOT;
        case r'TEMPLATE': return WebhookCardTypeEnum.TEMPLATE;
        case r'TAG': return WebhookCardTypeEnum.TAG;
        case r'BROWSER': return WebhookCardTypeEnum.BROWSER;
        case r'SETTING': return WebhookCardTypeEnum.SETTING;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [WebhookCardTypeEnumTypeTransformer] instance.
  static WebhookCardTypeEnumTypeTransformer? _instance;
}


