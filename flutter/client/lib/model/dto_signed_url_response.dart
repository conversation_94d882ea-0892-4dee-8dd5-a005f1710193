//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoSignedURLResponse {
  /// Returns a new [DtoSignedURLResponse] instance.
  DtoSignedURLResponse({
    required this.key,
    required this.signedUrl,
  });

  String key;

  String signedUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoSignedURLResponse &&
     other.key == key &&
     other.signedUrl == signedUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (key.hashCode) +
    (signedUrl.hashCode);

  @override
  String toString() => 'DtoSignedURLResponse[key=$key, signedUrl=$signedUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'key'] = this.key;
      json[r'signed_url'] = this.signedUrl;
    return json;
  }

  /// Returns a new [DtoSignedURLResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoSignedURLResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoSignedURLResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoSignedURLResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoSignedURLResponse(
        key: mapValueOfType<String>(json, r'key')!,
        signedUrl: mapValueOfType<String>(json, r'signed_url')!,
      );
    }
    return null;
  }

  static List<DtoSignedURLResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoSignedURLResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoSignedURLResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoSignedURLResponse> mapFromJson(dynamic json) {
    final map = <String, DtoSignedURLResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSignedURLResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoSignedURLResponse-objects as value to a dart map
  static Map<String, List<DtoSignedURLResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoSignedURLResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSignedURLResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'key',
    'signed_url',
  };
}

