//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class WorkspacesApi {
  WorkspacesApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Returns a list of workspaces
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> workspacesGetWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of workspaces
  Future<DtoGetWorkspacesResponse?> workspacesGet() async {
    final response = await workspacesGetWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetWorkspacesResponse',) as DtoGetWorkspacesResponse;
    
    }
    return null;
  }

  /// Create workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoPostWorkspaceRequest] body (required):
  ///   Workspace object
  Future<Response> workspacesPostWithHttpInfo(DtoPostWorkspaceRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create workspace
  ///
  /// Parameters:
  ///
  /// * [DtoPostWorkspaceRequest] body (required):
  ///   Workspace object
  Future<DtoPostWorkspaceResponse?> workspacesPost(DtoPostWorkspaceRequest body,) async {
    final response = await workspacesPostWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoPostWorkspaceResponse',) as DtoPostWorkspaceResponse;
    
    }
    return null;
  }

  /// Delete Background
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdBackgroundSignedUrlDeleteWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Background
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<void> workspacesWorkspaceIdBackgroundSignedUrlDelete(String workspaceId,) async {
    final response = await workspacesWorkspaceIdBackgroundSignedUrlDeleteWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create Background signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<Response> workspacesWorkspaceIdBackgroundSignedUrlPostWithHttpInfo(String workspaceId, String method,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Background signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdBackgroundSignedUrlPost(String workspaceId, String method,) async {
    final response = await workspacesWorkspaceIdBackgroundSignedUrlPostWithHttpInfo(workspaceId, method,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Delete workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdDeleteWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete workspace
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<void> workspacesWorkspaceIdDelete(String workspaceId,) async {
    final response = await workspacesWorkspaceIdDeleteWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get workspace
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoWorkspaceResponse?> workspacesWorkspaceIdGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoWorkspaceResponse',) as DtoWorkspaceResponse;
    
    }
    return null;
  }

  /// Delete Icon
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdIconSignedUrlDeleteWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/icon-signed-url'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Icon
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<void> workspacesWorkspaceIdIconSignedUrlDelete(String workspaceId,) async {
    final response = await workspacesWorkspaceIdIconSignedUrlDeleteWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create icon signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [String] size:
  ///   Size
  Future<Response> workspacesWorkspaceIdIconSignedUrlPostWithHttpInfo(String workspaceId, String method, { String? size, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/icon-signed-url'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));
    if (size != null) {
      queryParams.addAll(_queryParams('', 'size', size));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create icon signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [String] size:
  ///   Size
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdIconSignedUrlPost(String workspaceId, String method, { String? size, }) async {
    final response = await workspacesWorkspaceIdIconSignedUrlPostWithHttpInfo(workspaceId, method,  size: size, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Update workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPatchWorkspaceRequest] body (required):
  ///   Workspace object
  Future<Response> workspacesWorkspaceIdPatchWithHttpInfo(String workspaceId, DtoPatchWorkspaceRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update workspace
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPatchWorkspaceRequest] body (required):
  ///   Workspace object
  Future<DtoWorkspaceResponse?> workspacesWorkspaceIdPatch(String workspaceId, DtoPatchWorkspaceRequest body,) async {
    final response = await workspacesWorkspaceIdPatchWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoWorkspaceResponse',) as DtoWorkspaceResponse;
    
    }
    return null;
  }

  /// Change pricing plan
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPutWorkspacePricingPlanRequest] body (required):
  ///   Change pricing plan object
  Future<Response> workspacesWorkspaceIdPricingPlanPutWithHttpInfo(String workspaceId, DtoPutWorkspacePricingPlanRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/pricing-plan'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Change pricing plan
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPutWorkspacePricingPlanRequest] body (required):
  ///   Change pricing plan object
  Future<void> workspacesWorkspaceIdPricingPlanPut(String workspaceId, DtoPutWorkspacePricingPlanRequest body,) async {
    final response = await workspacesWorkspaceIdPricingPlanPutWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Update workspace
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPutWorkspaceRequest] body (required):
  ///   Workspace object
  Future<Response> workspacesWorkspaceIdPutWithHttpInfo(String workspaceId, DtoPutWorkspaceRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update workspace
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPutWorkspaceRequest] body (required):
  ///   Workspace object
  Future<DtoWorkspaceResponse?> workspacesWorkspaceIdPut(String workspaceId, DtoPutWorkspaceRequest body,) async {
    final response = await workspacesWorkspaceIdPutWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoWorkspaceResponse',) as DtoWorkspaceResponse;
    
    }
    return null;
  }
}
