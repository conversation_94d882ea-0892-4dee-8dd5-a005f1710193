package database

import (
	"context"
	"slices"
	"strings"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"

	"clipcrow/essentialworkware/common/logger"
	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
	"clipcrow/essentialworkware/model/enum/chattargettype"
	"clipcrow/essentialworkware/model/enum/memberrole"
	"clipcrow/essentialworkware/model/enum/targettype"
)

func (s *databaseService) FindByWorkspaceID(ctx context.Context, requesterID, workspaceID uuid.UUID, q dto.GetDatabasesQueryParams) (dto.DatabasesResponse, error) {
	db := s.c.GetRepository(ctx)
	databases := []*model.Database{}
	var requester model.Member
	err := db.Where("id = ?", requesterID).Preload("Tags", db.DefaultPreloadOrderTag()).First(&requester).Error
	if err != nil {
		s.c.GetLogger().Warn("error getting requester")
		return dto.DatabasesResponse{}, err
	}
	requesterTags := []uuid.UUID{}
	for _, rTag := range requester.Tags {
		requesterTags = append(requesterTags, rTag.ID)
	}
	tx := db.Model(&model.Database{}).
		Joins("LEFT JOIN database_tags ON database_tags.database_id = databases.id").
		Where("databases.workspace_id = ? ", workspaceID).
		Where(db.Not("(databases.caption = 'seats' AND databases.is_default = TRUE)")).
		Group("databases.id").
		Order("databases.position ASC").
		Preload("Tags").
		Select("databases.id")

	if (q.RoleTag == null.String{} && len(q.TagIDs) == 0 && q.Query == "") { // no query
		tx = tx.Where("database_tags.tag_id IN ? OR databases.is_default = TRUE OR ?", requesterTags, requester.Role == memberrole.MANAGER)
	} else { // has query
		if len(q.TagIDs) != 0 {
			requesterTags, err = s.tr.ToUUIDs(workspaceID, &q.TagIDs)
			if err != nil {
				s.c.GetLogger().Warn("FindByWorkspaceID error getting tag ids")
				return dto.DatabasesResponse{}, err
			}
			tx = tx.Where("database_tags.tag_id IN ?", requesterTags)
		}
		if q.Query != "" {
			q.Query = strings.ReplaceAll(q.Query, "%", "\\%")
			q.Query = "%" + q.Query + "%"
			tx = tx.Where("databases.title ILIKE ?", q.Query)
		}
	}

	if requester.Role == memberrole.GUEST || q.ForGuest.Bool {
		tx = tx.Where("databases.caption = ? and databases.type = ?", "guest_service", "GUEST_DATABASE")
	} else {
		tx = tx.Where("databases.parent_id is NULL")
	}

	err = tx.Find(&databases).Error
	if err != nil {
		return dto.DatabasesResponse{}, err
	}
	// Extract database IDs for batch processing
	databaseIDs := make([]uuid.UUID, len(databases))
	for i, db := range databases {
		databaseIDs[i] = db.ID
	}

	// Use batch method to get all database responses
	databaseResponses, err := s.GetDatabasesResponse(ctx, workspaceID, requesterID, databaseIDs)
	if err != nil {
		return dto.DatabasesResponse{}, err
	}

	// Build the final response
	response := dto.DatabasesResponse{}
	for i := range databaseResponses {
		response.Items = append(response.Items, &databaseResponses[i])
	}

	return response, nil
}

func (s *databaseService) FindByWorkspaceIDAndID(ctx context.Context, requesterID, workspaceID, databaseID uuid.UUID) (dto.DatabaseResponse, error) {
	db := s.c.GetRepository(ctx)
	var requester model.Member
	if err := db.Where("id = ?", requesterID).Preload("Tags", db.DefaultPreloadOrderTag()).First(&requester).Error; err != nil {
		s.c.GetLogger().Warn("error getting requester")
		return dto.DatabaseResponse{}, err
	}

	requesterTags := []uuid.UUID{}
	for _, rTag := range requester.Tags {
		requesterTags = append(requesterTags, rTag.ID)
	}

	orQuery := []*gorm.DB{
		db.Where("database_tags.tag_id IN ? OR databases.is_default = TRUE OR ?", requesterTags, requester.Role == memberrole.MANAGER),
	}

	if requester.Role == memberrole.GUEST {
		databaseIDs := []uuid.UUID{}
		if err := db.Model(&model.Database{}).Where("workspace_id = ? and parent_id is not null", workspaceID).Pluck("id", &databaseIDs).Error; err != nil {
			return dto.DatabaseResponse{}, err
		}
		orQuery = append(orQuery, db.Where("databases.id in (?) and ?", databaseIDs, requester.Role == memberrole.GUEST))
	}

	var database model.Database
	err := db.Model(&model.Database{}).
		Joins("LEFT JOIN database_tags ON database_tags.database_id = databases.id").
		Where(db.JoinOrQueries(orQuery)).
		Where("databases.id = ?", databaseID).
		Where("workspace_id = ?", workspaceID).Select("id").
		First(&database).Error
	if err != nil {
		return dto.DatabaseResponse{}, err
	}
	databaseR, err := s.GetDatabaseResponse(ctx, requesterID, databaseID)
	if err != nil {
		return dto.DatabaseResponse{}, err
	}
	databaseTile := []string{targettype.NAVIGATIONS, targettype.PROFILE_ITEMS}
	if databaseR.IsDefault.Bool {
		if slices.Contains(databaseTile, databaseR.Title.String) {
			target := databaseR.Title.String
			targetID := workspaceID
			if target == targettype.GUEST_SERVICE {
				target = targettype.DATABASES
				targetID = databaseID
			}
			chatInfo, err := s.chatInfoService.GetChatInfoByTargetID(ctx, workspaceID, targetID.String(), target, requesterID)
			if err != nil {
				return dto.DatabaseResponse{}, err
			}
			databaseR.ChatInfo = chatInfo
		} else if slices.Contains([]string{targettype.SEAT, targettype.GUEST_SERVICE}, databaseR.Title.String) {
			var target string
			switch databaseR.Title.String {
			case targettype.SEAT:
				target = targettype.SEAT
			case targettype.GUEST_SERVICE:
				target = targettype.GUEST_SERVICE
			default:
				target = targettype.DATABASES
			}
			result, err := s.chatInfoService.GetChatInfoByGroupIDs(ctx, workspaceID, []string{databaseID.String()}, target, requesterID)
			if err != nil {
				return dto.DatabaseResponse{}, err
			}
			chatInfo := result[databaseID.String()]
			databaseR.ChatInfo = &chatInfo
		}
	}
	return databaseR, nil
}

// GetDatabasesResponse fetches multiple database responses in a single batch operation
// This method optimizes database queries by reducing the number of round trips
func (s *databaseService) GetDatabasesResponse(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, databaseIDs []uuid.UUID) ([]dto.DatabaseResponse, error) {
	if len(databaseIDs) == 0 {
		return []dto.DatabaseResponse{}, nil
	}

	db := s.c.GetRepository(ctx)

	// Batch fetch all databases with their relationships
	databases := []*model.Database{}
	err := db.Where("id IN ?", databaseIDs).
		Preload("UpdateTableFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("Tags").
		Preload("BOTs").
		Find(&databases).Error
	if err != nil {
		return nil, err
	}

	// Create a map for quick lookup by database ID
	databaseMap := make(map[uuid.UUID]*model.Database)
	for i := range databases {
		databaseMap[databases[i].ID] = databases[i]
	}

	// Batch fetch bot external links for support databases
	supportDatabaseIDs := []uuid.UUID{}
	for _, db := range databases {
		if db.Caption.String == "support" && db.IsDefault.Bool {
			supportDatabaseIDs = append(supportDatabaseIDs, db.ID)
		}
	}

	botsByDatabaseID := make(map[uuid.UUID][]*model.ExternalLink)
	if len(supportDatabaseIDs) > 0 {
		var botDatabases []model.BotDatabase
		err = db.Where("database_id IN ?", supportDatabaseIDs).Find(&botDatabases).Error
		if err != nil {
			return nil, err
		}

		if len(botDatabases) > 0 {
			botIDs := make([]uuid.UUID, len(botDatabases))
			for i, bd := range botDatabases {
				botIDs[i] = bd.ExternalLinkID
			}

			var bots []*model.ExternalLink
			err = db.Where("id IN ?", botIDs).Find(&bots).Error
			if err != nil {
				return nil, err
			}

			// Fill icon signed URLs for all bots
			for _, bot := range bots {
				err := s.externalLinkService.FillIconSignedURL(ctx, bot)
				if err != nil {
					return nil, err
				}
			}

			// Group bots by database ID
			botIDToDatabaseID := make(map[uuid.UUID]uuid.UUID)
			for _, bd := range botDatabases {
				botIDToDatabaseID[bd.ExternalLinkID] = bd.DatabaseID
			}

			for _, bot := range bots {
				databaseID := botIDToDatabaseID[bot.ID]
				botsByDatabaseID[databaseID] = append(botsByDatabaseID[databaseID], bot)
			}
		}
	}

	// Batch fetch workspace form items for navigation databases
	mNavigationDatabaseIDs := make(map[uuid.UUID]struct{})
	for _, db := range databases {
		if db.IsDefault.Bool && db.Caption.String == targettype.NAVIGATIONS {
			mNavigationDatabaseIDs[db.ID] = struct{}{}
		}
	}

	profileFormItems := model.FormItems{}
	if len(mNavigationDatabaseIDs) > 0 {
		err = db.
			Joins("LEFT JOIN workspace_form_items ON workspace_form_items.form_item_id = form_items.id").
			Where("workspace_form_items.workspace_id = ?", workspaceID).
			Order("form_items.position ASC").
			Find(&profileFormItems).Error
		if err != nil {
			return nil, errors.Wrap(err, "error getting workspace form items")
		}
	}

	// Build responses maintaining the order of input IDs
	responses := make([]dto.DatabaseResponse, 0, len(databaseIDs))
	for _, databaseID := range databaseIDs {
		database, exists := databaseMap[databaseID]
		if !exists {
			// Skip databases that weren't found
			continue
		}

		response := dto.DatabaseResponse{}

		// Set BOTs if this is a support database
		if bots, hasBots := botsByDatabaseID[databaseID]; hasBots {
			database.BOTs = bots
		}

		response.GetFrom(database)

		// Set profile form items if this is a navigation database
		if _, isNavigationDatabase := mNavigationDatabaseIDs[database.WorkspaceID]; isNavigationDatabase {
			response.ProfileFormItems = profileFormItems
		}

		responses = append(responses, response)
	}

	// Group databases by type for batch processing
	recordQueryIDs := []uuid.UUID{}
	webViewIDs := []uuid.UUID{}
	guestDatabaseIDs := []uuid.UUID{}

	// Create index maps to track which response corresponds to which database
	recordQueryIndexMap := make(map[uuid.UUID]int)
	webViewIndexMap := make(map[uuid.UUID]int)
	guestDatabaseIndexMap := make(map[uuid.UUID]int)

	for i, databaseID := range databaseIDs {
		database := databaseMap[databaseID]
		if database == nil {
			continue
		}

		switch database.Type.String {
		case "RECORD_QUERY":
			recordQueryIDs = append(recordQueryIDs, database.ID)
			recordQueryIndexMap[database.ID] = i
		case "WEB_VIEW":
			webViewIDs = append(webViewIDs, database.ID)
			webViewIndexMap[database.ID] = i
		case "GUEST_DATABASE":
			guestDatabaseIDs = append(guestDatabaseIDs, database.ID)
			guestDatabaseIndexMap[database.ID] = i
		}
	}

	// Batch fetch record queries
	if len(recordQueryIDs) > 0 {
		recordQueryResponses, err := s.recordQueryService.FindByIDs(ctx, recordQueryIDs)
		if err != nil {
			return nil, errors.Wrap(err, "error getting record queries in batch")
		}

		for databaseID, recordQueryResponse := range recordQueryResponses {
			if idx, exists := recordQueryIndexMap[databaseID]; exists {
				responses[idx].RecordQueryResponse = recordQueryResponse
			}
		}
	}

	// Batch fetch web views
	if len(webViewIDs) > 0 {
		webViewResponses, err := s.webViewService.FindByIDs(ctx, webViewIDs)
		if err != nil {
			return nil, errors.Wrap(err, "error getting web views in batch")
		}

		var chatRooms []*model.Record
		err = db.Where("origin_id IN ? AND origin_type = ?", webViewIDs, chattargettype.DATABASE).
			Order("created_at ASC").Find(&chatRooms).Error
		if err != nil {
			return nil, errors.Wrap(err, "error getting chat rooms")
		}

		mChatRoomsResponse := make(map[uuid.UUID][]*dto.ChatRoomResponse)
		for _, chatRoom := range chatRooms {
			shareScopeResponse, err := s.shareScopeService.FindByTargetID(ctx, chatRoom.ID)
			if err != nil {
				s.c.GetLogger().WithContext(ctx).Error("error getting share scope", logger.FieldMap{logger.ErrorKey: err})
				continue
			}
			mChatRoomsResponse[chatRoom.OriginID.UUID] = append(mChatRoomsResponse[chatRoom.OriginID.UUID], &dto.ChatRoomResponse{
				Name:       chatRoom.Name.String,
				ID:         chatRoom.ID,
				ShareScope: shareScopeResponse,
			})
		}

		for databaseID, webViewResponse := range webViewResponses {
			if idx, exists := webViewIndexMap[databaseID]; exists {
				webViewResponse.ChatRoomsResponse = mChatRoomsResponse[databaseID]
				responses[idx].WebViewResponse = webViewResponse
			}
		}
	}

	// Batch fetch guest databases
	if len(guestDatabaseIDs) > 0 {
		var guestDatabases []model.GuestDatabase
		err := db.Where("database_id IN ?", guestDatabaseIDs).Preload("Databases").Find(&guestDatabases).Error
		if err != nil {
			return nil, errors.Wrap(err, "error getting guest databases in batch")
		}

		// Process each guest database
		for _, guestDatabase := range guestDatabases {
			if idx, exists := guestDatabaseIndexMap[guestDatabase.DatabaseID]; exists {
				// Get nested database IDs for recursive call
				nestedDatabaseIDs := make([]uuid.UUID, len(guestDatabase.Databases))
				for j, gdb := range guestDatabase.Databases {
					nestedDatabaseIDs[j] = gdb.ID
				}

				// Get workspace ID from the database map
				var workspaceID uuid.UUID
				if database := databaseMap[guestDatabase.DatabaseID]; database != nil {
					workspaceID = database.WorkspaceID
				}

				// Recursively get database responses for guest databases
				dtoDatabases, err := s.GetDatabasesResponse(ctx, workspaceID, requesterID, nestedDatabaseIDs)
				if err != nil {
					return nil, errors.Wrap(err, "error getting guest database responses")
				}

				responses[idx].GuestDatabaseResponse.MapFrom(&guestDatabase)
				responses[idx].GuestDatabaseResponse.Databases = dtoDatabases
			}
		}
	}

	requester := model.Member{ID: requesterID}
	if err := db.Where("id = ?", requesterID).First(&requester).Error; err != nil {
		return nil, errors.Wrap(err, "error getting requester")
	}

	for i := range responses {
		database := databaseMap[databaseIDs[i]]
		if database == nil {
			continue
		}

		// Get facilitator response
		facilitatorResponse, err := s.facilitatorService.FindByTargetID(ctx, database.ID)
		if err != nil {
			responses[i].Facilitator = nil
		} else {
			responses[i].Facilitator = facilitatorResponse
		}

		// Get template response if template ID is valid
		if responses[i].TemplateID.Valid {
			template, err := s.templateService.GetTemplateResponse(ctx, requesterID, database.TemplateID.UUID, false, targettype.TEMPLATE)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					s.c.GetLogger().WithContext(ctx).Error("error getting template", logger.FieldMap{logger.ErrorKey: err})
				}
			} else {
				responses[i].Template = template
			}
		}

		// Handle guest role tags
		if requester.Role == memberrole.GUEST {
			responses[i].Tags = []*model.Tag{
				{
					Value: "TAG_VALUE_MATCH_ALL_USER",
				},
			}
		}
	}

	return responses, nil
}

func (s *databaseService) GetDatabaseResponse(ctx context.Context, requesterID, databaseID uuid.UUID) (dto.DatabaseResponse, error) {
	db := s.c.GetRepository(ctx)
	database := model.Database{
		ID: databaseID,
	}
	err := db.
		Preload("UpdateTableFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{})).Preload("Tags").
		Preload("BOTs").
		First(&database).Error
	if err != nil {
		return dto.DatabaseResponse{}, err
	}
	response := dto.DatabaseResponse{}
	if database.Caption.String == "support" && database.IsDefault.Bool {
		var botIDs []uuid.UUID
		err = db.Model(&model.BotDatabase{}).Where("database_id = ?", database.ID).Select("external_link_id").Scan(&botIDs).Error
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
		var bots []*model.ExternalLink
		err = db.Where("id IN ?", botIDs).Find(&bots).Error
		if err != nil {
			return dto.DatabaseResponse{}, err
		}

		for _, bot := range bots {
			err := s.externalLinkService.FillIconSignedURL(ctx, bot)
			if err != nil {
				return dto.DatabaseResponse{}, err
			}
		}
		database.BOTs = bots
	}
	response.GetFrom(&database)
	if database.IsDefault.Bool && database.Caption.String == targettype.NAVIGATIONS {
		var workspace model.Workspace
		err = db.
			Where("id = ?", database.WorkspaceID).
			Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{})).
			First(&workspace).Error
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
		response.ProfileFormItems = workspace.FormItems
	}
	if database.Type.String == "RECORD_QUERY" {
		response.RecordQueryResponse, err = s.recordQueryService.FindByID(ctx, databaseID)
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
	} else if database.Type.String == "WEB_VIEW" {
		response.WebViewResponse, err = s.webViewService.FindByID(ctx, databaseID)
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
	}

	switch database.Type.String {
	case "RECORD_QUERY":
		response.RecordQueryResponse, err = s.recordQueryService.FindByID(ctx, databaseID)
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
	case "CARD_HOLDER":
	case "WEB_VIEW":
		response.WebViewResponse, err = s.webViewService.FindByID(ctx, databaseID)
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
	case "GUEST_DATABASE":
		guestDatabase := &model.GuestDatabase{
			DatabaseID: databaseID,
		}

		err = db.Where(guestDatabase).Preload("Databases").First(guestDatabase).Error
		if err != nil {
			return dto.DatabaseResponse{}, err
		}

		dtoDatabases := []dto.DatabaseResponse{}
		for _, db := range guestDatabase.Databases {
			dtoDatabase, err := s.GetDatabaseResponse(ctx, requesterID, db.ID)
			if err != nil {
				return dto.DatabaseResponse{}, err
			}
			dtoDatabases = append(dtoDatabases, dtoDatabase)
		}
		response.GuestDatabaseResponse.MapFrom(guestDatabase)
		response.GuestDatabaseResponse.Databases = dtoDatabases
	}
	facilitatorResponse, err := s.facilitatorService.FindByTargetID(ctx, databaseID)
	if err != nil {
		response.Facilitator = nil
	} else {
		response.Facilitator = facilitatorResponse
	}

	if response.TemplateID.Valid {
		template, err := s.templateService.GetTemplateResponse(ctx, requesterID, database.TemplateID.UUID, false, targettype.TEMPLATE)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return dto.DatabaseResponse{}, err
			}
		}
		response.Template = template
	}

	requester := model.Member{
		ID: requesterID,
	}
	if err := db.Where("id = ?", requesterID).First(&requester).Error; err != nil {
		return dto.DatabaseResponse{}, err
	}

	if requester.Role == memberrole.GUEST {
		response.Tags = []*model.Tag{
			{
				Value: "TAG_VALUE_MATCH_ALL_USER",
			},
		}
	}

	// get chat room if database is webview
	if response.Type.String == "WEB_VIEW" {
		var chatRooms []*model.Record
		err = db.Where("origin_id = ? AND origin_type = ?", response.ID, chattargettype.DATABASE).Order("created_at ASC").Find(&chatRooms).Error
		if err != nil {
			return dto.DatabaseResponse{}, err
		}
		for _, chatRoom := range chatRooms {

			shareScopeResponse, err := s.shareScopeService.FindByTargetID(ctx, chatRoom.ID)
			if err != nil {
				return dto.DatabaseResponse{}, err
			}
			response.WebViewResponse.ChatRoomsResponse = append(response.WebViewResponse.ChatRoomsResponse, &dto.ChatRoomResponse{
				Name:       chatRoom.Name.String,
				ID:         chatRoom.ID,
				ShareScope: shareScopeResponse,
			})
		}
	}

	return response, nil
}

func (s *databaseService) GetGuestDatabaseService(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID) (dto.DatabaseResponse, error) {
	db := s.c.GetRepository(ctx)

	guestDatabase := &model.Database{
		WorkspaceID: workspaceID,
		Caption:     null.StringFrom("guest"),
		Title:       null.StringFrom("guest"),
		IsDefault:   null.BoolFrom(true),
	}

	err := db.Where(guestDatabase).First(guestDatabase).Error
	if err != nil {
		return dto.DatabaseResponse{}, err
	}

	return s.GetDatabaseResponse(ctx, requesterID, guestDatabase.ID)
}
