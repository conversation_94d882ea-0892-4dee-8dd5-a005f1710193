//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoStatusTaskIndicator {
  /// Returns a new [DtoStatusTaskIndicator] instance.
  DtoStatusTaskIndicator({
    this.memberBeforeStart = const [],
    this.memberCompleted = const [],
    this.memberInProgress = const [],
    this.memberIncomplete = const [],
    this.memberPaused = const [],
  });

  List<DtoMemberIndicator> memberBeforeStart;

  List<DtoMemberIndicator> memberCompleted;

  List<DtoMemberIndicator> memberInProgress;

  List<DtoMemberIndicator> memberIncomplete;

  List<DtoMemberIndicator> memberPaused;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoStatusTaskIndicator &&
     other.memberBeforeStart == memberBeforeStart &&
     other.memberCompleted == memberCompleted &&
     other.memberInProgress == memberInProgress &&
     other.memberIncomplete == memberIncomplete &&
     other.memberPaused == memberPaused;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (memberBeforeStart.hashCode) +
    (memberCompleted.hashCode) +
    (memberInProgress.hashCode) +
    (memberIncomplete.hashCode) +
    (memberPaused.hashCode);

  @override
  String toString() => 'DtoStatusTaskIndicator[memberBeforeStart=$memberBeforeStart, memberCompleted=$memberCompleted, memberInProgress=$memberInProgress, memberIncomplete=$memberIncomplete, memberPaused=$memberPaused]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'member_before_start'] = this.memberBeforeStart;
      json[r'member_completed'] = this.memberCompleted;
      json[r'member_in_progress'] = this.memberInProgress;
      json[r'member_incomplete'] = this.memberIncomplete;
      json[r'member_paused'] = this.memberPaused;
    return json;
  }

  /// Returns a new [DtoStatusTaskIndicator] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoStatusTaskIndicator? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoStatusTaskIndicator[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoStatusTaskIndicator[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoStatusTaskIndicator(
        memberBeforeStart: DtoMemberIndicator.listFromJson(json[r'member_before_start']) ?? const [],
        memberCompleted: DtoMemberIndicator.listFromJson(json[r'member_completed']) ?? const [],
        memberInProgress: DtoMemberIndicator.listFromJson(json[r'member_in_progress']) ?? const [],
        memberIncomplete: DtoMemberIndicator.listFromJson(json[r'member_incomplete']) ?? const [],
        memberPaused: DtoMemberIndicator.listFromJson(json[r'member_paused']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoStatusTaskIndicator>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoStatusTaskIndicator>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoStatusTaskIndicator.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoStatusTaskIndicator> mapFromJson(dynamic json) {
    final map = <String, DtoStatusTaskIndicator>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoStatusTaskIndicator.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoStatusTaskIndicator-objects as value to a dart map
  static Map<String, List<DtoStatusTaskIndicator>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoStatusTaskIndicator>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoStatusTaskIndicator.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

