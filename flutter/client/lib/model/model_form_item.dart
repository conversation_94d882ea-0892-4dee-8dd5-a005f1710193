//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelFormItem {
  /// Returns a new [ModelFormItem] instance.
  ModelFormItem({
    required this.createdAt,
    this.dataObj,
    this.deletedAt,
    this.description,
    required this.displayOnCard,
    required this.id,
    this.isLockSettings,
    required this.itemRequired,
    required this.name,
    this.position,
    this.props,
    required this.type,
    required this.updatedAt,
  });

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? dataObj;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  bool displayOnCard;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isLockSettings;

  bool itemRequired;

  String name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? position;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? props;

  ModelFormItemTypeEnum type;

  String updatedAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelFormItem &&
     other.createdAt == createdAt &&
     other.dataObj == dataObj &&
     other.deletedAt == deletedAt &&
     other.description == description &&
     other.displayOnCard == displayOnCard &&
     other.id == id &&
     other.isLockSettings == isLockSettings &&
     other.itemRequired == itemRequired &&
     other.name == name &&
     other.position == position &&
     other.props == props &&
     other.type == type &&
     other.updatedAt == updatedAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (createdAt.hashCode) +
    (dataObj == null ? 0 : dataObj!.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (displayOnCard.hashCode) +
    (id.hashCode) +
    (isLockSettings == null ? 0 : isLockSettings!.hashCode) +
    (itemRequired.hashCode) +
    (name.hashCode) +
    (position == null ? 0 : position!.hashCode) +
    (props == null ? 0 : props!.hashCode) +
    (type.hashCode) +
    (updatedAt.hashCode);

  @override
  String toString() => 'ModelFormItem[createdAt=$createdAt, dataObj=$dataObj, deletedAt=$deletedAt, description=$description, displayOnCard=$displayOnCard, id=$id, isLockSettings=$isLockSettings, itemRequired=$itemRequired, name=$name, position=$position, props=$props, type=$type, updatedAt=$updatedAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'created_at'] = this.createdAt;
    if (this.dataObj != null) {
      json[r'data_obj'] = this.dataObj;
    } else {
      json[r'data_obj'] = null;
    }
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
      json[r'display_on_card'] = this.displayOnCard;
      json[r'id'] = this.id;
    if (this.isLockSettings != null) {
      json[r'is_lock_settings'] = this.isLockSettings;
    } else {
      json[r'is_lock_settings'] = null;
    }
      json[r'item_required'] = this.itemRequired;
      json[r'name'] = this.name;
    if (this.position != null) {
      json[r'position'] = this.position;
    } else {
      json[r'position'] = null;
    }
    if (this.props != null) {
      json[r'props'] = this.props;
    } else {
      json[r'props'] = null;
    }
      json[r'type'] = this.type;
      json[r'updated_at'] = this.updatedAt;
    return json;
  }

  /// Returns a new [ModelFormItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelFormItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelFormItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelFormItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelFormItem(
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        dataObj: mapValueOfType<Object>(json, r'data_obj'),
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        description: mapValueOfType<String>(json, r'description'),
        displayOnCard: mapValueOfType<bool>(json, r'display_on_card')!,
        id: mapValueOfType<String>(json, r'id')!,
        isLockSettings: mapValueOfType<bool>(json, r'is_lock_settings'),
        itemRequired: mapValueOfType<bool>(json, r'item_required')!,
        name: mapValueOfType<String>(json, r'name')!,
        position: json[r'position'] == null
            ? null
            : num.parse(json[r'position'].toString()),
        props: mapValueOfType<Object>(json, r'props'),
        type: ModelFormItemTypeEnum.fromJson(json[r'type'])!,
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
      );
    }
    return null;
  }

  static List<ModelFormItem>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelFormItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelFormItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelFormItem> mapFromJson(dynamic json) {
    final map = <String, ModelFormItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelFormItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelFormItem-objects as value to a dart map
  static Map<String, List<ModelFormItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelFormItem>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelFormItem.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'display_on_card',
    'id',
    'item_required',
    'name',
    'type',
    'updated_at',
  };
}


class ModelFormItemTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelFormItemTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const TEXT = ModelFormItemTypeEnum._(r'TEXT');
  static const DROPDOWN = ModelFormItemTypeEnum._(r'DROPDOWN');
  static const CHECKBOX = ModelFormItemTypeEnum._(r'CHECKBOX');
  static const RADIO_BUTTON = ModelFormItemTypeEnum._(r'RADIO_BUTTON');
  static const DATETIME = ModelFormItemTypeEnum._(r'DATETIME');
  static const LINK = ModelFormItemTypeEnum._(r'LINK');
  static const NUMBER = ModelFormItemTypeEnum._(r'NUMBER');
  static const IMAGE = ModelFormItemTypeEnum._(r'IMAGE');
  static const LOCATION = ModelFormItemTypeEnum._(r'LOCATION');
  static const CRM = ModelFormItemTypeEnum._(r'CRM');
  static const MEMBER = ModelFormItemTypeEnum._(r'MEMBER');
  static const TEXTAREA = ModelFormItemTypeEnum._(r'TEXTAREA');
  static const DATABASE = ModelFormItemTypeEnum._(r'DATABASE');
  static const FILE = ModelFormItemTypeEnum._(r'FILE');

  /// List of all possible values in this [enum][ModelFormItemTypeEnum].
  static const values = <ModelFormItemTypeEnum>[
    TEXT,
    DROPDOWN,
    CHECKBOX,
    RADIO_BUTTON,
    DATETIME,
    LINK,
    NUMBER,
    IMAGE,
    LOCATION,
    CRM,
    MEMBER,
    TEXTAREA,
    DATABASE,
    FILE,
  ];

  static ModelFormItemTypeEnum? fromJson(dynamic value) => ModelFormItemTypeEnumTypeTransformer().decode(value);

  static List<ModelFormItemTypeEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelFormItemTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelFormItemTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelFormItemTypeEnum] to String,
/// and [decode] dynamic data back to [ModelFormItemTypeEnum].
class ModelFormItemTypeEnumTypeTransformer {
  factory ModelFormItemTypeEnumTypeTransformer() => _instance ??= const ModelFormItemTypeEnumTypeTransformer._();

  const ModelFormItemTypeEnumTypeTransformer._();

  String encode(ModelFormItemTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelFormItemTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelFormItemTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'TEXT': return ModelFormItemTypeEnum.TEXT;
        case r'DROPDOWN': return ModelFormItemTypeEnum.DROPDOWN;
        case r'CHECKBOX': return ModelFormItemTypeEnum.CHECKBOX;
        case r'RADIO_BUTTON': return ModelFormItemTypeEnum.RADIO_BUTTON;
        case r'DATETIME': return ModelFormItemTypeEnum.DATETIME;
        case r'LINK': return ModelFormItemTypeEnum.LINK;
        case r'NUMBER': return ModelFormItemTypeEnum.NUMBER;
        case r'IMAGE': return ModelFormItemTypeEnum.IMAGE;
        case r'LOCATION': return ModelFormItemTypeEnum.LOCATION;
        case r'CRM': return ModelFormItemTypeEnum.CRM;
        case r'MEMBER': return ModelFormItemTypeEnum.MEMBER;
        case r'TEXTAREA': return ModelFormItemTypeEnum.TEXTAREA;
        case r'DATABASE': return ModelFormItemTypeEnum.DATABASE;
        case r'FILE': return ModelFormItemTypeEnum.FILE;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelFormItemTypeEnumTypeTransformer] instance.
  static ModelFormItemTypeEnumTypeTransformer? _instance;
}


