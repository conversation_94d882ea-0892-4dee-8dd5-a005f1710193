# openapi.model.DtoDatabaseResponse

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**bots** | [**List<DtoExternalLinkResponse>**](DtoExternalLinkResponse.md) |  | [optional] [default to const []]
**caption** | **String** |  | [optional] 
**chatInfo** | [**DtoChatInfo**](DtoChatInfo.md) |  | [optional] 
**displayChatOnCard** | **bool** |  | [optional] 
**facilitator** | [**DtoFacilitatorResponse**](DtoFacilitatorResponse.md) |  | [optional] 
**formItems** | [**List<ModelFormItem>**](ModelFormItem.md) |  | [optional] [default to const []]
**guestDatabase** | [**DtoGuestDatabaseResponse**](DtoGuestDatabaseResponse.md) |  | [optional] 
**iconString** | **String** |  | [optional] 
**id** | **String** |  | 
**isDefault** | **bool** |  | [optional] 
**position** | **num** |  | [optional] 
**profileFormItems** | [**List<ModelFormItem>**](ModelFormItem.md) |  | [optional] [default to const []]
**recordQuery** | [**DtoRecordQueryResponse**](DtoRecordQueryResponse.md) |  | [optional] 
**tags** | [**List<ModelTag>**](ModelTag.md) |  | [optional] [default to const []]
**template** | [**DtoTemplateResponse**](DtoTemplateResponse.md) |  | [optional] 
**templateId** | **String** |  | [optional] 
**title** | **String** |  | 
**type** | **String** |  | [optional] 
**updateableFormItems** | [**List<ModelFormItem>**](ModelFormItem.md) |  | [optional] [default to const []]
**webView** | [**DtoWebViewResponse**](DtoWebViewResponse.md) |  | [optional] 
**workspaceId** | **String** |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


