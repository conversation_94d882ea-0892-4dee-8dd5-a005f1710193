# openapi.model.ClipcrowEssentialworkwarePkgMapsGeocodingResult

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**addressComponents** | [**List<MapsAddressComponent>**](MapsAddressComponent.md) |  | [optional] [default to const []]
**formattedAddress** | **String** |  | [optional] 
**geometry** | [**MapsAddressGeometry**](MapsAddressGeometry.md) |  | [optional] 
**partialMatch** | **bool** | PartialMatch indicates that the geocoder did not return an exact match for the original request, though it was able to match part of the requested address. You may wish to examine the original request for misspellings and/or an incomplete address. Partial matches most often occur for street addresses that do not exist within the locality you pass in the request. Partial matches may also be returned when a request matches two or more locations in the same locality. For example, \"21 Henr St, Bristol, UK\" will return a partial match for both Henry Street and Henrietta Street. Note that if a request includes a misspelled address component, the geocoding service may suggest an alternative address. Suggestions triggered in this way will also be marked as a partial match. | [optional] 
**placeId** | **String** |  | [optional] 
**plusCode** | [**ClipcrowEssentialworkwarePkgMapsGeocodingResultPlusCode**](ClipcrowEssentialworkwarePkgMapsGeocodingResultPlusCode.md) |  | [optional] 
**types** | **List<String>** |  | [optional] [default to const []]

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


