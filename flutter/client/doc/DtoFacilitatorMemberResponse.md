# openapi.model.DtoFacilitatorMemberResponse

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **String** |  | [optional] 
**icon** | **String** |  | [optional] 
**iconKey** | **String** |  | [optional] 
**memberFormItems** | [**List<ModelMemberFormItem>**](ModelMemberFormItem.md) |  | [optional] [default to const []]
**memberId** | **String** |  | [optional] 
**name** | **String** |  | [optional] 
**optionalFormItems** | [**List<ModelFormItem>**](ModelFormItem.md) |  | [optional] [default to const []]
**role** | **String** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


