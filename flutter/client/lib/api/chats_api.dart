//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class ChatsApi {
  ChatsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Check if bot is replying
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] targetType (required):
  ///   Target type
  ///
  /// * [String] targetId (required):
  ///   Target ID
  Future<Response> workspacesWorkspaceIdChatsBotReplyStatusGetWithHttpInfo(String workspaceId, String targetType, String targetId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/bot_reply_status'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'target_type', targetType));
      queryParams.addAll(_queryParams('', 'target_id', targetId));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Check if bot is replying
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] targetType (required):
  ///   Target type
  ///
  /// * [String] targetId (required):
  ///   Target ID
  Future<DtoBotReplyStatusResponse?> workspacesWorkspaceIdChatsBotReplyStatusGet(String workspaceId, String targetType, String targetId,) async {
    final response = await workspacesWorkspaceIdChatsBotReplyStatusGetWithHttpInfo(workspaceId, targetType, targetId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoBotReplyStatusResponse',) as DtoBotReplyStatusResponse;
    
    }
    return null;
  }

  /// Delete message
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] chatId (required):
  ///   ChatID
  Future<Response> workspacesWorkspaceIdChatsChatIdDeleteWithHttpInfo(String workspaceId, String chatId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete message
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [String] chatId (required):
  ///   ChatID
  Future<void> workspacesWorkspaceIdChatsChatIdDelete(String workspaceId, String chatId,) async {
    final response = await workspacesWorkspaceIdChatsChatIdDeleteWithHttpInfo(workspaceId, chatId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create signed url download Chat Image
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   Chat ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [DtoImageSignedURLObjectRequest] body (required):
  ///   Get signed url request
  Future<Response> workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPostWithHttpInfo(String workspaceId, String chatId, String method, DtoImageSignedURLObjectRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}/files/signed_urls'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create signed url download Chat Image
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   Chat ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  ///
  /// * [DtoImageSignedURLObjectRequest] body (required):
  ///   Get signed url request
  Future<DtoGetChatSignedURLResponse?> workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPost(String workspaceId, String chatId, String method, DtoImageSignedURLObjectRequest body,) async {
    final response = await workspacesWorkspaceIdChatsChatIdFilesSignedUrlsPostWithHttpInfo(workspaceId, chatId, method, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetChatSignedURLResponse',) as DtoGetChatSignedURLResponse;
    
    }
    return null;
  }

  /// Returns number of page and a message list by message ID
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [int] pageSize (required):
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  ///
  /// * [bool] doGetPrivate:
  Future<Response> workspacesWorkspaceIdChatsChatIdFindPageGetWithHttpInfo(String workspaceId, String chatId, int pageSize, String targetId, String targetType, { bool? doGetPrivate, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}/find-page'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (doGetPrivate != null) {
      queryParams.addAll(_queryParams('', 'do_get_private', doGetPrivate));
    }
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
      queryParams.addAll(_queryParams('', 'target_id', targetId));
      queryParams.addAll(_queryParams('', 'target_type', targetType));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns number of page and a message list by message ID
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [int] pageSize (required):
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  ///
  /// * [bool] doGetPrivate:
  Future<DtoFindPageAndMessagesResponse?> workspacesWorkspaceIdChatsChatIdFindPageGet(String workspaceId, String chatId, int pageSize, String targetId, String targetType, { bool? doGetPrivate, }) async {
    final response = await workspacesWorkspaceIdChatsChatIdFindPageGetWithHttpInfo(workspaceId, chatId, pageSize, targetId, targetType,  doGetPrivate: doGetPrivate, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoFindPageAndMessagesResponse',) as DtoFindPageAndMessagesResponse;
    
    }
    return null;
  }

  /// Returns Message
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [bool] hasViewed:
  Future<Response> workspacesWorkspaceIdChatsChatIdGetWithHttpInfo(String workspaceId, String chatId, { bool? hasViewed, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (hasViewed != null) {
      queryParams.addAll(_queryParams('', 'has_viewed', hasViewed));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns Message
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [bool] hasViewed:
  Future<DtoMessage?> workspacesWorkspaceIdChatsChatIdGet(String workspaceId, String chatId, { bool? hasViewed, }) async {
    final response = await workspacesWorkspaceIdChatsChatIdGetWithHttpInfo(workspaceId, chatId,  hasViewed: hasViewed, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMessage',) as DtoMessage;
    
    }
    return null;
  }

  /// Update Chat
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [DtoPatchMessageRequest] body (required):
  ///   PatchMessageRequest object
  Future<Response> workspacesWorkspaceIdChatsChatIdPatchWithHttpInfo(String workspaceId, String chatId, DtoPatchMessageRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Chat
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [DtoPatchMessageRequest] body (required):
  ///   PatchMessageRequest object
  Future<DtoMessage?> workspacesWorkspaceIdChatsChatIdPatch(String workspaceId, String chatId, DtoPatchMessageRequest body,) async {
    final response = await workspacesWorkspaceIdChatsChatIdPatchWithHttpInfo(workspaceId, chatId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMessage',) as DtoMessage;
    
    }
    return null;
  }

  /// React Chat
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [DtoReactionUpdateRequest] body (required):
  ///   ReactionUpdateRequest object
  Future<Response> workspacesWorkspaceIdChatsChatIdReactionsPutWithHttpInfo(String workspaceId, String chatId, DtoReactionUpdateRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}/reactions'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// React Chat
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   chat id
  ///
  /// * [DtoReactionUpdateRequest] body (required):
  ///   ReactionUpdateRequest object
  Future<void> workspacesWorkspaceIdChatsChatIdReactionsPut(String workspaceId, String chatId, DtoReactionUpdateRequest body,) async {
    final response = await workspacesWorkspaceIdChatsChatIdReactionsPutWithHttpInfo(workspaceId, chatId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Returns members who reacted to the reaction
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   Chat ID
  ///
  /// * [String] reactionId (required):
  ///   Reaction ID
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  Future<Response> workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGetWithHttpInfo(String workspaceId, String chatId, String reactionId, { int? page, int? pageSize, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/{chat_id}/reactions/{reaction_id}/members'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{chat_id}', chatId)
      .replaceAll('{reaction_id}', reactionId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns members who reacted to the reaction
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] chatId (required):
  ///   Chat ID
  ///
  /// * [String] reactionId (required):
  ///   Reaction ID
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  Future<DtoGetMembersResponse?> workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGet(String workspaceId, String chatId, String reactionId, { int? page, int? pageSize, }) async {
    final response = await workspacesWorkspaceIdChatsChatIdReactionsReactionIdMembersGetWithHttpInfo(workspaceId, chatId, reactionId,  page: page, pageSize: pageSize, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMembersResponse',) as DtoGetMembersResponse;
    
    }
    return null;
  }

  /// Returns a list of Message
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  ///
  /// * [String] bookmark:
  ///
  /// * [bool] doGetPrivate:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] hasViewed:
  ///
  /// * [bool] isArchive:
  ///
  /// * [bool] manageProgressAsTask:
  ///
  /// * [List<String>] messageType:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  Future<Response> workspacesWorkspaceIdChatsGetWithHttpInfo(String workspaceId, String targetId, String targetType, { String? bookmark, bool? doGetPrivate, String? facilitator, bool? hasViewed, bool? isArchive, bool? manageProgressAsTask, List<String>? messageType, bool? onlyUnread, int? page, int? pageSize, String? query, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (bookmark != null) {
      queryParams.addAll(_queryParams('', 'bookmark', bookmark));
    }
    if (doGetPrivate != null) {
      queryParams.addAll(_queryParams('', 'do_get_private', doGetPrivate));
    }
    if (facilitator != null) {
      queryParams.addAll(_queryParams('', 'facilitator', facilitator));
    }
    if (hasViewed != null) {
      queryParams.addAll(_queryParams('', 'has_viewed', hasViewed));
    }
    if (isArchive != null) {
      queryParams.addAll(_queryParams('', 'is_archive', isArchive));
    }
    if (manageProgressAsTask != null) {
      queryParams.addAll(_queryParams('', 'manage_progress_as_task', manageProgressAsTask));
    }
    if (messageType != null) {
      queryParams.addAll(_queryParams('csv', 'message_type', messageType));
    }
    if (onlyUnread != null) {
      queryParams.addAll(_queryParams('', 'only_unread', onlyUnread));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
      queryParams.addAll(_queryParams('', 'target_id', targetId));
      queryParams.addAll(_queryParams('', 'target_type', targetType));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of Message
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  ///
  /// * [String] bookmark:
  ///
  /// * [bool] doGetPrivate:
  ///
  /// * [String] facilitator:
  ///
  /// * [bool] hasViewed:
  ///
  /// * [bool] isArchive:
  ///
  /// * [bool] manageProgressAsTask:
  ///
  /// * [List<String>] messageType:
  ///
  /// * [bool] onlyUnread:
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] query:
  Future<DtoGetMessagesResponse?> workspacesWorkspaceIdChatsGet(String workspaceId, String targetId, String targetType, { String? bookmark, bool? doGetPrivate, String? facilitator, bool? hasViewed, bool? isArchive, bool? manageProgressAsTask, List<String>? messageType, bool? onlyUnread, int? page, int? pageSize, String? query, }) async {
    final response = await workspacesWorkspaceIdChatsGetWithHttpInfo(workspaceId, targetId, targetType,  bookmark: bookmark, doGetPrivate: doGetPrivate, facilitator: facilitator, hasViewed: hasViewed, isArchive: isArchive, manageProgressAsTask: manageProgressAsTask, messageType: messageType, onlyUnread: onlyUnread, page: page, pageSize: pageSize, query: query, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMessagesResponse',) as DtoGetMessagesResponse;
    
    }
    return null;
  }

  /// Return all features has unread messages
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdChatsGetUnreadFeaturesGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/get-unread-features'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Return all features has unread messages
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<List<DtoFeatureUnread>?> workspacesWorkspaceIdChatsGetUnreadFeaturesGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdChatsGetUnreadFeaturesGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      final responseBody = await _decodeBodyBytes(response);
      return (await apiClient.deserializeAsync(responseBody, 'List<DtoFeatureUnread>') as List)
        .cast<DtoFeatureUnread>()
        .toList();

    }
    return null;
  }

  /// Post log
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostLogRequest] body (required):
  ///   PostLogRequest object
  Future<Response> workspacesWorkspaceIdChatsLogsPostWithHttpInfo(String workspaceId, DtoPostLogRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/logs'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Post log
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostLogRequest] body (required):
  ///   PostLogRequest object
  Future<DtoMessage?> workspacesWorkspaceIdChatsLogsPost(String workspaceId, DtoPostLogRequest body,) async {
    final response = await workspacesWorkspaceIdChatsLogsPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMessage',) as DtoMessage;
    
    }
    return null;
  }

  /// Returns mention names
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  Future<Response> workspacesWorkspaceIdChatsMentionNameGetWithHttpInfo(String workspaceId, String targetId, String targetType,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/mention-name'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'target_id', targetId));
      queryParams.addAll(_queryParams('', 'target_type', targetType));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns mention names
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  Future<DtoMentionNameResponse?> workspacesWorkspaceIdChatsMentionNameGet(String workspaceId, String targetId, String targetType,) async {
    final response = await workspacesWorkspaceIdChatsMentionNameGetWithHttpInfo(workspaceId, targetId, targetType,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMentionNameResponse',) as DtoMentionNameResponse;
    
    }
    return null;
  }

  /// Returns a list of members or external links
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetMentionRequest] body (required):
  ///   Query Parameters
  Future<Response> workspacesWorkspaceIdChatsMentionPostWithHttpInfo(String workspaceId, DtoGetMentionRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/mention'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Returns a list of members or external links
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetMentionRequest] body (required):
  ///   Query Parameters
  Future<DtoGetMentionResponse?> workspacesWorkspaceIdChatsMentionPost(String workspaceId, DtoGetMentionRequest body,) async {
    final response = await workspacesWorkspaceIdChatsMentionPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetMentionResponse',) as DtoGetMentionResponse;
    
    }
    return null;
  }

  /// Send message
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostChatRequest] body (required):
  ///   PostChatRequest object
  Future<Response> workspacesWorkspaceIdChatsPostWithHttpInfo(String workspaceId, DtoPostChatRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Send message
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostChatRequest] body (required):
  ///   PostChatRequest object
  Future<DtoMessage?> workspacesWorkspaceIdChatsPost(String workspaceId, DtoPostChatRequest body,) async {
    final response = await workspacesWorkspaceIdChatsPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoMessage',) as DtoMessage;
    
    }
    return null;
  }

  /// Typing message
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostTypingRequest] body (required):
  ///   PostTypingRequest object
  Future<Response> workspacesWorkspaceIdChatsTypingPostWithHttpInfo(String workspaceId, DtoPostTypingRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/typing'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Typing message
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   WorkspaceID
  ///
  /// * [DtoPostTypingRequest] body (required):
  ///   PostTypingRequest object
  Future<String?> workspacesWorkspaceIdChatsTypingPost(String workspaceId, DtoPostTypingRequest body,) async {
    final response = await workspacesWorkspaceIdChatsTypingPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'String',) as String;
    
    }
    return null;
  }

  /// Set has viewed
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  Future<Response> workspacesWorkspaceIdChatsUpdateHasViewedPatchWithHttpInfo(String workspaceId, String targetId, String targetType,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/chats/update-has-viewed'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'target_id', targetId));
      queryParams.addAll(_queryParams('', 'target_type', targetType));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Set has viewed
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] targetId (required):
  ///
  /// * [String] targetType (required):
  Future<void> workspacesWorkspaceIdChatsUpdateHasViewedPatch(String workspaceId, String targetId, String targetType,) async {
    final response = await workspacesWorkspaceIdChatsUpdateHasViewedPatchWithHttpInfo(workspaceId, targetId, targetType,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}
