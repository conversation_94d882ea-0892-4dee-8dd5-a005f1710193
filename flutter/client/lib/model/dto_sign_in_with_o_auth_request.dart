//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoSignInWithOAuthRequest {
  /// Returns a new [DtoSignInWithOAuthRequest] instance.
  DtoSignInWithOAuthRequest({
    required this.idToken,
  });

  String idToken;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoSignInWithOAuthRequest &&
     other.idToken == idToken;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (idToken.hashCode);

  @override
  String toString() => 'DtoSignInWithOAuthRequest[idToken=$idToken]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'id_token'] = this.idToken;
    return json;
  }

  /// Returns a new [DtoSignInWithOAuthRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoSignInWithOAuthRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoSignInWithOAuthRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoSignInWithOAuthRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoSignInWithOAuthRequest(
        idToken: mapValueOfType<String>(json, r'id_token')!,
      );
    }
    return null;
  }

  static List<DtoSignInWithOAuthRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoSignInWithOAuthRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoSignInWithOAuthRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoSignInWithOAuthRequest> mapFromJson(dynamic json) {
    final map = <String, DtoSignInWithOAuthRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSignInWithOAuthRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoSignInWithOAuthRequest-objects as value to a dart map
  static Map<String, List<DtoSignInWithOAuthRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoSignInWithOAuthRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoSignInWithOAuthRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id_token',
  };
}

