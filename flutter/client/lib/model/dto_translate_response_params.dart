//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTranslateResponseParams {
  /// Returns a new [DtoTranslateResponseParams] instance.
  DtoTranslateResponseParams({
    required this.detectedSourceLanguage,
    required this.text,
  });

  String detectedSourceLanguage;

  String text;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTranslateResponseParams &&
     other.detectedSourceLanguage == detectedSourceLanguage &&
     other.text == text;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (detectedSourceLanguage.hashCode) +
    (text.hashCode);

  @override
  String toString() => 'DtoTranslateResponseParams[detectedSourceLanguage=$detectedSourceLanguage, text=$text]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'detected_source_language'] = this.detectedSourceLanguage;
      json[r'text'] = this.text;
    return json;
  }

  /// Returns a new [DtoTranslateResponseParams] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTranslateResponseParams? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTranslateResponseParams[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTranslateResponseParams[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTranslateResponseParams(
        detectedSourceLanguage: mapValueOfType<String>(json, r'detected_source_language')!,
        text: mapValueOfType<String>(json, r'text')!,
      );
    }
    return null;
  }

  static List<DtoTranslateResponseParams>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTranslateResponseParams>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTranslateResponseParams.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTranslateResponseParams> mapFromJson(dynamic json) {
    final map = <String, DtoTranslateResponseParams>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTranslateResponseParams.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTranslateResponseParams-objects as value to a dart map
  static Map<String, List<DtoTranslateResponseParams>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTranslateResponseParams>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTranslateResponseParams.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'detected_source_language',
    'text',
  };
}

