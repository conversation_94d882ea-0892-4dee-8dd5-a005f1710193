//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoUpdateTagRequest {
  /// Returns a new [DtoUpdateTagRequest] instance.
  DtoUpdateTagRequest({
    this.id,
    this.position,
    required this.value,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? position;

  String value;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoUpdateTagRequest &&
     other.id == id &&
     other.position == position &&
     other.value == value;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id == null ? 0 : id!.hashCode) +
    (position == null ? 0 : position!.hashCode) +
    (value.hashCode);

  @override
  String toString() => 'DtoUpdateTagRequest[id=$id, position=$position, value=$value]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.id != null) {
      json[r'id'] = this.id;
    } else {
      json[r'id'] = null;
    }
    if (this.position != null) {
      json[r'position'] = this.position;
    } else {
      json[r'position'] = null;
    }
      json[r'value'] = this.value;
    return json;
  }

  /// Returns a new [DtoUpdateTagRequest] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoUpdateTagRequest? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoUpdateTagRequest[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoUpdateTagRequest[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoUpdateTagRequest(
        id: mapValueOfType<String>(json, r'id'),
        position: json[r'position'] == null
            ? null
            : num.parse(json[r'position'].toString()),
        value: mapValueOfType<String>(json, r'value')!,
      );
    }
    return null;
  }

  static List<DtoUpdateTagRequest>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoUpdateTagRequest>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoUpdateTagRequest.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoUpdateTagRequest> mapFromJson(dynamic json) {
    final map = <String, DtoUpdateTagRequest>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoUpdateTagRequest.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoUpdateTagRequest-objects as value to a dart map
  static Map<String, List<DtoUpdateTagRequest>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoUpdateTagRequest>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoUpdateTagRequest.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'value',
  };
}

