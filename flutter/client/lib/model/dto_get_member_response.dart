//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetMemberResponse {
  /// Returns a new [DtoGetMemberResponse] instance.
  DtoGetMemberResponse({
    this.mainMember,
    this.mainPicTaskCount,
    this.member,
    this.subMember,
    this.subPicTaskCount,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoMember? mainMember;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? mainPicTaskCount;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoMember? member;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoMember? subMember;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? subPicTaskCount;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetMemberResponse &&
     other.mainMember == mainMember &&
     other.mainPicTaskCount == mainPicTaskCount &&
     other.member == member &&
     other.subMember == subMember &&
     other.subPicTaskCount == subPicTaskCount;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (mainMember == null ? 0 : mainMember!.hashCode) +
    (mainPicTaskCount == null ? 0 : mainPicTaskCount!.hashCode) +
    (member == null ? 0 : member!.hashCode) +
    (subMember == null ? 0 : subMember!.hashCode) +
    (subPicTaskCount == null ? 0 : subPicTaskCount!.hashCode);

  @override
  String toString() => 'DtoGetMemberResponse[mainMember=$mainMember, mainPicTaskCount=$mainPicTaskCount, member=$member, subMember=$subMember, subPicTaskCount=$subPicTaskCount]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.mainMember != null) {
      json[r'main_member'] = this.mainMember;
    } else {
      json[r'main_member'] = null;
    }
    if (this.mainPicTaskCount != null) {
      json[r'main_pic_task_count'] = this.mainPicTaskCount;
    } else {
      json[r'main_pic_task_count'] = null;
    }
    if (this.member != null) {
      json[r'member'] = this.member;
    } else {
      json[r'member'] = null;
    }
    if (this.subMember != null) {
      json[r'sub_member'] = this.subMember;
    } else {
      json[r'sub_member'] = null;
    }
    if (this.subPicTaskCount != null) {
      json[r'sub_pic_task_count'] = this.subPicTaskCount;
    } else {
      json[r'sub_pic_task_count'] = null;
    }
    return json;
  }

  /// Returns a new [DtoGetMemberResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetMemberResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetMemberResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetMemberResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetMemberResponse(
        mainMember: DtoMember.fromJson(json[r'main_member']),
        mainPicTaskCount: mapValueOfType<int>(json, r'main_pic_task_count'),
        member: DtoMember.fromJson(json[r'member']),
        subMember: DtoMember.fromJson(json[r'sub_member']),
        subPicTaskCount: mapValueOfType<int>(json, r'sub_pic_task_count'),
      );
    }
    return null;
  }

  static List<DtoGetMemberResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetMemberResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetMemberResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetMemberResponse> mapFromJson(dynamic json) {
    final map = <String, DtoGetMemberResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetMemberResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetMemberResponse-objects as value to a dart map
  static Map<String, List<DtoGetMemberResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetMemberResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetMemberResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

