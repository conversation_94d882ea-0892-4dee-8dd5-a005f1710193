//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class InfographicApi {
  InfographicApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get Infographic Chat
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] queryType:
  ///
  /// * [String] sort:
  Future<Response> workspacesWorkspaceIdInfographicChatGetWithHttpInfo(String workspaceId, { int? page, int? pageSize, String? queryType, String? sort, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/infographic/chat'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }
    if (pageSize != null) {
      queryParams.addAll(_queryParams('', 'page_size', pageSize));
    }
    if (queryType != null) {
      queryParams.addAll(_queryParams('', 'query_type', queryType));
    }
    if (sort != null) {
      queryParams.addAll(_queryParams('', 'sort', sort));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Infographic Chat
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [int] page:
  ///
  /// * [int] pageSize:
  ///
  /// * [String] queryType:
  ///
  /// * [String] sort:
  Future<DtoGetInfographicChatResponse?> workspacesWorkspaceIdInfographicChatGet(String workspaceId, { int? page, int? pageSize, String? queryType, String? sort, }) async {
    final response = await workspacesWorkspaceIdInfographicChatGetWithHttpInfo(workspaceId,  page: page, pageSize: pageSize, queryType: queryType, sort: sort, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetInfographicChatResponse',) as DtoGetInfographicChatResponse;
    
    }
    return null;
  }

  /// Get Infographic
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> workspacesWorkspaceIdInfographicGetWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/infographic'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Infographic
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoGetInfographicDataResponse?> workspacesWorkspaceIdInfographicGet(String workspaceId,) async {
    final response = await workspacesWorkspaceIdInfographicGetWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetInfographicDataResponse',) as DtoGetInfographicDataResponse;
    
    }
    return null;
  }
}
