//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class AuthApi {
  AuthApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Sign in with Apple Callback
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoSignInWithOAuthRequest] body (required):
  ///   Sign in with Apple request object
  Future<Response> authCallbacksSignInWithApplePostWithHttpInfo(DtoSignInWithOAuthRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/callbacks/sign-in-with-apple';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sign in with Apple Callback
  ///
  /// Parameters:
  ///
  /// * [DtoSignInWithOAuthRequest] body (required):
  ///   Sign in with Apple request object
  Future<void> authCallbacksSignInWithApplePost(DtoSignInWithOAuthRequest body,) async {
    final response = await authCallbacksSignInWithApplePostWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create custom token
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<Response> authCustomTokenPostWithHttpInfo(String workspaceId,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/custom-token';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'workspace_id', workspaceId));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create custom token
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  Future<DtoPostAuthCustomTokenResponse?> authCustomTokenPost(String workspaceId,) async {
    final response = await authCustomTokenPostWithHttpInfo(workspaceId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoPostAuthCustomTokenResponse',) as DtoPostAuthCustomTokenResponse;
    
    }
    return null;
  }

  /// forgot password
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoForgotPasswordRequest] body (required):
  ///   ChangePassword object
  Future<Response> authForgotPasswordPutWithHttpInfo(DtoForgotPasswordRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/forgot-password';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// forgot password
  ///
  /// Parameters:
  ///
  /// * [DtoForgotPasswordRequest] body (required):
  ///   ChangePassword object
  Future<void> authForgotPasswordPut(DtoForgotPasswordRequest body,) async {
    final response = await authForgotPasswordPutWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Get Inviting Member Info
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoInvitingMemberInfoRequest] body (required):
  ///   Inviting Member Info Request
  Future<Response> authInvitationsPostWithHttpInfo(DtoInvitingMemberInfoRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/invitations';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Inviting Member Info
  ///
  /// Parameters:
  ///
  /// * [DtoInvitingMemberInfoRequest] body (required):
  ///   Inviting Member Info Request
  Future<DtoWorkspaceInvitingMember?> authInvitationsPost(DtoInvitingMemberInfoRequest body,) async {
    final response = await authInvitationsPostWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoWorkspaceInvitingMember',) as DtoWorkspaceInvitingMember;
    
    }
    return null;
  }

  /// Revoke Tokens
  ///
  /// Note: This method returns the HTTP [Response].
  Future<Response> authRevokeTokensPostWithHttpInfo() async {
    // ignore: prefer_const_declarations
    final path = r'/auth/revoke-tokens';

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Revoke Tokens
  Future<void> authRevokeTokensPost() async {
    final response = await authRevokeTokensPostWithHttpInfo();
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Sign in with OAuth
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoSignInWithOAuthRequest] body (required):
  ///   Sign in with OAuth request object
  Future<Response> authSignInWithOauthPostWithHttpInfo(DtoSignInWithOAuthRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/auth/sign-in-with-oauth';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Sign in with OAuth
  ///
  /// Parameters:
  ///
  /// * [DtoSignInWithOAuthRequest] body (required):
  ///   Sign in with OAuth request object
  Future<DtoPostAuthCustomTokenResponse?> authSignInWithOauthPost(DtoSignInWithOAuthRequest body,) async {
    final response = await authSignInWithOauthPostWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoPostAuthCustomTokenResponse',) as DtoPostAuthCustomTokenResponse;
    
    }
    return null;
  }
}
