//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelLatestChatUser {
  /// Returns a new [ModelLatestChatUser] instance.
  ModelLatestChatUser({
    this.actor,
    required this.actorId,
    this.actorType,
    required this.chatInfoId,
    required this.groupId,
    this.latestMsgTime,
    this.position,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelActor? actor;

  String actorId;

  ModelLatestChatUserActorTypeEnum? actorType;

  String chatInfoId;

  String groupId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? latestMsgTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? position;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelLatestChatUser &&
     other.actor == actor &&
     other.actorId == actorId &&
     other.actorType == actorType &&
     other.chatInfoId == chatInfoId &&
     other.groupId == groupId &&
     other.latestMsgTime == latestMsgTime &&
     other.position == position;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (actor == null ? 0 : actor!.hashCode) +
    (actorId.hashCode) +
    (actorType == null ? 0 : actorType!.hashCode) +
    (chatInfoId.hashCode) +
    (groupId.hashCode) +
    (latestMsgTime == null ? 0 : latestMsgTime!.hashCode) +
    (position == null ? 0 : position!.hashCode);

  @override
  String toString() => 'ModelLatestChatUser[actor=$actor, actorId=$actorId, actorType=$actorType, chatInfoId=$chatInfoId, groupId=$groupId, latestMsgTime=$latestMsgTime, position=$position]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.actor != null) {
      json[r'actor'] = this.actor;
    } else {
      json[r'actor'] = null;
    }
      json[r'actor_id'] = this.actorId;
    if (this.actorType != null) {
      json[r'actor_type'] = this.actorType;
    } else {
      json[r'actor_type'] = null;
    }
      json[r'chat_info_id'] = this.chatInfoId;
      json[r'group_id'] = this.groupId;
    if (this.latestMsgTime != null) {
      json[r'latest_msg_time'] = this.latestMsgTime;
    } else {
      json[r'latest_msg_time'] = null;
    }
    if (this.position != null) {
      json[r'position'] = this.position;
    } else {
      json[r'position'] = null;
    }
    return json;
  }

  /// Returns a new [ModelLatestChatUser] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelLatestChatUser? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelLatestChatUser[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelLatestChatUser[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelLatestChatUser(
        actor: ModelActor.fromJson(json[r'actor']),
        actorId: mapValueOfType<String>(json, r'actor_id')!,
        actorType: ModelLatestChatUserActorTypeEnum.fromJson(json[r'actor_type']),
        chatInfoId: mapValueOfType<String>(json, r'chat_info_id')!,
        groupId: mapValueOfType<String>(json, r'group_id')!,
        latestMsgTime: mapValueOfType<String>(json, r'latest_msg_time'),
        position: mapValueOfType<int>(json, r'position'),
      );
    }
    return null;
  }

  static List<ModelLatestChatUser>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelLatestChatUser>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelLatestChatUser.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelLatestChatUser> mapFromJson(dynamic json) {
    final map = <String, ModelLatestChatUser>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelLatestChatUser.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelLatestChatUser-objects as value to a dart map
  static Map<String, List<ModelLatestChatUser>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelLatestChatUser>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelLatestChatUser.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'actor_id',
    'chat_info_id',
    'group_id',
  };
}


class ModelLatestChatUserActorTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelLatestChatUserActorTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const members = ModelLatestChatUserActorTypeEnum._(r'members');
  static const externalLinks = ModelLatestChatUserActorTypeEnum._(r'external_links');
  static const seatBot = ModelLatestChatUserActorTypeEnum._(r'seat_bot');

  /// List of all possible values in this [enum][ModelLatestChatUserActorTypeEnum].
  static const values = <ModelLatestChatUserActorTypeEnum>[
    members,
    externalLinks,
    seatBot,
  ];

  static ModelLatestChatUserActorTypeEnum? fromJson(dynamic value) => ModelLatestChatUserActorTypeEnumTypeTransformer().decode(value);

  static List<ModelLatestChatUserActorTypeEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelLatestChatUserActorTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelLatestChatUserActorTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelLatestChatUserActorTypeEnum] to String,
/// and [decode] dynamic data back to [ModelLatestChatUserActorTypeEnum].
class ModelLatestChatUserActorTypeEnumTypeTransformer {
  factory ModelLatestChatUserActorTypeEnumTypeTransformer() => _instance ??= const ModelLatestChatUserActorTypeEnumTypeTransformer._();

  const ModelLatestChatUserActorTypeEnumTypeTransformer._();

  String encode(ModelLatestChatUserActorTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelLatestChatUserActorTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelLatestChatUserActorTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'members': return ModelLatestChatUserActorTypeEnum.members;
        case r'external_links': return ModelLatestChatUserActorTypeEnum.externalLinks;
        case r'seat_bot': return ModelLatestChatUserActorTypeEnum.seatBot;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelLatestChatUserActorTypeEnumTypeTransformer] instance.
  static ModelLatestChatUserActorTypeEnumTypeTransformer? _instance;
}


