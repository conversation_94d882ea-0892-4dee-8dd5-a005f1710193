//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTemplateResponse {
  /// Returns a new [DtoTemplateResponse] instance.
  DtoTemplateResponse({
    this.additionalFormItems = const [],
    this.background,
    this.backgroundKey,
    this.cardDescription,
    this.cardTitle,
    this.cardTitleMaxLength,
    this.cardTitleMinLength,
    this.cardTitleRegex,
    this.chatInfo,
    this.content,
    required this.createdAt,
    this.deletable,
    this.estimatedTime,
    this.facilitator,
    this.hasNewMessage,
    required this.id,
    this.illustration,
    this.isDefault,
    this.manageStatusIndividually,
    this.memberBookmarkedItemId,
    this.numberOfCard,
    this.opacity,
    this.primaryColor,
    this.reportFormItems = const [],
    this.reverseBackground,
    this.secondaryColor,
    this.shareScope,
    this.tagNumber,
    this.targetType,
    this.taskEnabled,
    this.textAlignment,
    this.textColor,
    this.timingEnabled,
    required this.title,
    required this.updatedAt,
    this.workspaceId,
  });

  List<ModelFormItem> additionalFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cardDescription;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cardTitle;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? cardTitleMaxLength;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? cardTitleMinLength;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? cardTitleRegex;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoChatInfo? chatInfo;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? content;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? deletable;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? estimatedTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoFacilitatorResponse? facilitator;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasNewMessage;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isDefault;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? manageStatusIndividually;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? memberBookmarkedItemId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? numberOfCard;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  List<ModelFormItem> reportFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  DtoShareScopeResponse? shareScope;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? targetType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? taskEnabled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  DtoTemplateResponseTextColorEnum? textColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? timingEnabled;

  String title;

  String updatedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTemplateResponse &&
     other.additionalFormItems == additionalFormItems &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.cardDescription == cardDescription &&
     other.cardTitle == cardTitle &&
     other.cardTitleMaxLength == cardTitleMaxLength &&
     other.cardTitleMinLength == cardTitleMinLength &&
     other.cardTitleRegex == cardTitleRegex &&
     other.chatInfo == chatInfo &&
     other.content == content &&
     other.createdAt == createdAt &&
     other.deletable == deletable &&
     other.estimatedTime == estimatedTime &&
     other.facilitator == facilitator &&
     other.hasNewMessage == hasNewMessage &&
     other.id == id &&
     other.illustration == illustration &&
     other.isDefault == isDefault &&
     other.manageStatusIndividually == manageStatusIndividually &&
     other.memberBookmarkedItemId == memberBookmarkedItemId &&
     other.numberOfCard == numberOfCard &&
     other.opacity == opacity &&
     other.primaryColor == primaryColor &&
     other.reportFormItems == reportFormItems &&
     other.reverseBackground == reverseBackground &&
     other.secondaryColor == secondaryColor &&
     other.shareScope == shareScope &&
     other.tagNumber == tagNumber &&
     other.targetType == targetType &&
     other.taskEnabled == taskEnabled &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.timingEnabled == timingEnabled &&
     other.title == title &&
     other.updatedAt == updatedAt &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (additionalFormItems.hashCode) +
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (cardDescription == null ? 0 : cardDescription!.hashCode) +
    (cardTitle == null ? 0 : cardTitle!.hashCode) +
    (cardTitleMaxLength == null ? 0 : cardTitleMaxLength!.hashCode) +
    (cardTitleMinLength == null ? 0 : cardTitleMinLength!.hashCode) +
    (cardTitleRegex == null ? 0 : cardTitleRegex!.hashCode) +
    (chatInfo == null ? 0 : chatInfo!.hashCode) +
    (content == null ? 0 : content!.hashCode) +
    (createdAt.hashCode) +
    (deletable == null ? 0 : deletable!.hashCode) +
    (estimatedTime == null ? 0 : estimatedTime!.hashCode) +
    (facilitator == null ? 0 : facilitator!.hashCode) +
    (hasNewMessage == null ? 0 : hasNewMessage!.hashCode) +
    (id.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (isDefault == null ? 0 : isDefault!.hashCode) +
    (manageStatusIndividually == null ? 0 : manageStatusIndividually!.hashCode) +
    (memberBookmarkedItemId == null ? 0 : memberBookmarkedItemId!.hashCode) +
    (numberOfCard == null ? 0 : numberOfCard!.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (reportFormItems.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (shareScope == null ? 0 : shareScope!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (targetType == null ? 0 : targetType!.hashCode) +
    (taskEnabled == null ? 0 : taskEnabled!.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (timingEnabled == null ? 0 : timingEnabled!.hashCode) +
    (title.hashCode) +
    (updatedAt.hashCode) +
    (workspaceId == null ? 0 : workspaceId!.hashCode);

  @override
  String toString() => 'DtoTemplateResponse[additionalFormItems=$additionalFormItems, background=$background, backgroundKey=$backgroundKey, cardDescription=$cardDescription, cardTitle=$cardTitle, cardTitleMaxLength=$cardTitleMaxLength, cardTitleMinLength=$cardTitleMinLength, cardTitleRegex=$cardTitleRegex, chatInfo=$chatInfo, content=$content, createdAt=$createdAt, deletable=$deletable, estimatedTime=$estimatedTime, facilitator=$facilitator, hasNewMessage=$hasNewMessage, id=$id, illustration=$illustration, isDefault=$isDefault, manageStatusIndividually=$manageStatusIndividually, memberBookmarkedItemId=$memberBookmarkedItemId, numberOfCard=$numberOfCard, opacity=$opacity, primaryColor=$primaryColor, reportFormItems=$reportFormItems, reverseBackground=$reverseBackground, secondaryColor=$secondaryColor, shareScope=$shareScope, tagNumber=$tagNumber, targetType=$targetType, taskEnabled=$taskEnabled, textAlignment=$textAlignment, textColor=$textColor, timingEnabled=$timingEnabled, title=$title, updatedAt=$updatedAt, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'additional_form_items'] = this.additionalFormItems;
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
    if (this.cardDescription != null) {
      json[r'card_description'] = this.cardDescription;
    } else {
      json[r'card_description'] = null;
    }
    if (this.cardTitle != null) {
      json[r'card_title'] = this.cardTitle;
    } else {
      json[r'card_title'] = null;
    }
    if (this.cardTitleMaxLength != null) {
      json[r'card_title_max_length'] = this.cardTitleMaxLength;
    } else {
      json[r'card_title_max_length'] = null;
    }
    if (this.cardTitleMinLength != null) {
      json[r'card_title_min_length'] = this.cardTitleMinLength;
    } else {
      json[r'card_title_min_length'] = null;
    }
    if (this.cardTitleRegex != null) {
      json[r'card_title_regex'] = this.cardTitleRegex;
    } else {
      json[r'card_title_regex'] = null;
    }
    if (this.chatInfo != null) {
      json[r'chat_info'] = this.chatInfo;
    } else {
      json[r'chat_info'] = null;
    }
    if (this.content != null) {
      json[r'content'] = this.content;
    } else {
      json[r'content'] = null;
    }
      json[r'created_at'] = this.createdAt;
    if (this.deletable != null) {
      json[r'deletable'] = this.deletable;
    } else {
      json[r'deletable'] = null;
    }
    if (this.estimatedTime != null) {
      json[r'estimated_time'] = this.estimatedTime;
    } else {
      json[r'estimated_time'] = null;
    }
    if (this.facilitator != null) {
      json[r'facilitator'] = this.facilitator;
    } else {
      json[r'facilitator'] = null;
    }
    if (this.hasNewMessage != null) {
      json[r'has_new_message'] = this.hasNewMessage;
    } else {
      json[r'has_new_message'] = null;
    }
      json[r'id'] = this.id;
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
    if (this.isDefault != null) {
      json[r'is_default'] = this.isDefault;
    } else {
      json[r'is_default'] = null;
    }
    if (this.manageStatusIndividually != null) {
      json[r'manage_status_individually'] = this.manageStatusIndividually;
    } else {
      json[r'manage_status_individually'] = null;
    }
    if (this.memberBookmarkedItemId != null) {
      json[r'member_bookmarked_item_id'] = this.memberBookmarkedItemId;
    } else {
      json[r'member_bookmarked_item_id'] = null;
    }
    if (this.numberOfCard != null) {
      json[r'number_of_card'] = this.numberOfCard;
    } else {
      json[r'number_of_card'] = null;
    }
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
      json[r'report_form_items'] = this.reportFormItems;
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
    if (this.shareScope != null) {
      json[r'share_scope'] = this.shareScope;
    } else {
      json[r'share_scope'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
    if (this.targetType != null) {
      json[r'target_type'] = this.targetType;
    } else {
      json[r'target_type'] = null;
    }
    if (this.taskEnabled != null) {
      json[r'task_enabled'] = this.taskEnabled;
    } else {
      json[r'task_enabled'] = null;
    }
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
    if (this.timingEnabled != null) {
      json[r'timing_enabled'] = this.timingEnabled;
    } else {
      json[r'timing_enabled'] = null;
    }
      json[r'title'] = this.title;
      json[r'updated_at'] = this.updatedAt;
    if (this.workspaceId != null) {
      json[r'workspace_id'] = this.workspaceId;
    } else {
      json[r'workspace_id'] = null;
    }
    return json;
  }

  /// Returns a new [DtoTemplateResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTemplateResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTemplateResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTemplateResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTemplateResponse(
        additionalFormItems: ModelFormItem.listFromJson(json[r'additional_form_items']) ?? const [],
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        cardDescription: mapValueOfType<String>(json, r'card_description'),
        cardTitle: mapValueOfType<String>(json, r'card_title'),
        cardTitleMaxLength: mapValueOfType<int>(json, r'card_title_max_length'),
        cardTitleMinLength: mapValueOfType<int>(json, r'card_title_min_length'),
        cardTitleRegex: mapValueOfType<String>(json, r'card_title_regex'),
        chatInfo: DtoChatInfo.fromJson(json[r'chat_info']),
        content: mapValueOfType<String>(json, r'content'),
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletable: mapValueOfType<bool>(json, r'deletable'),
        estimatedTime: mapValueOfType<int>(json, r'estimated_time'),
        facilitator: DtoFacilitatorResponse.fromJson(json[r'facilitator']),
        hasNewMessage: mapValueOfType<bool>(json, r'has_new_message'),
        id: mapValueOfType<String>(json, r'id')!,
        illustration: mapValueOfType<String>(json, r'illustration'),
        isDefault: mapValueOfType<bool>(json, r'is_default'),
        manageStatusIndividually: mapValueOfType<bool>(json, r'manage_status_individually'),
        memberBookmarkedItemId: mapValueOfType<String>(json, r'member_bookmarked_item_id'),
        numberOfCard: mapValueOfType<int>(json, r'number_of_card'),
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        reportFormItems: ModelFormItem.listFromJson(json[r'report_form_items']) ?? const [],
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        shareScope: DtoShareScopeResponse.fromJson(json[r'share_scope']),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        targetType: mapValueOfType<String>(json, r'target_type'),
        taskEnabled: mapValueOfType<bool>(json, r'task_enabled'),
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: DtoTemplateResponseTextColorEnum.fromJson(json[r'text_color']),
        timingEnabled: mapValueOfType<bool>(json, r'timing_enabled'),
        title: mapValueOfType<String>(json, r'title')!,
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        workspaceId: mapValueOfType<String>(json, r'workspace_id'),
      );
    }
    return null;
  }

  static List<DtoTemplateResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTemplateResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTemplateResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTemplateResponse> mapFromJson(dynamic json) {
    final map = <String, DtoTemplateResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTemplateResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTemplateResponse-objects as value to a dart map
  static Map<String, List<DtoTemplateResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTemplateResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTemplateResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'id',
    'title',
    'updated_at',
  };
}


class DtoTemplateResponseTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const DtoTemplateResponseTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = DtoTemplateResponseTextColorEnum._(r'DARK');
  static const LIGHT = DtoTemplateResponseTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][DtoTemplateResponseTextColorEnum].
  static const values = <DtoTemplateResponseTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static DtoTemplateResponseTextColorEnum? fromJson(dynamic value) => DtoTemplateResponseTextColorEnumTypeTransformer().decode(value);

  static List<DtoTemplateResponseTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTemplateResponseTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTemplateResponseTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DtoTemplateResponseTextColorEnum] to String,
/// and [decode] dynamic data back to [DtoTemplateResponseTextColorEnum].
class DtoTemplateResponseTextColorEnumTypeTransformer {
  factory DtoTemplateResponseTextColorEnumTypeTransformer() => _instance ??= const DtoTemplateResponseTextColorEnumTypeTransformer._();

  const DtoTemplateResponseTextColorEnumTypeTransformer._();

  String encode(DtoTemplateResponseTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DtoTemplateResponseTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DtoTemplateResponseTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return DtoTemplateResponseTextColorEnum.DARK;
        case r'LIGHT': return DtoTemplateResponseTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DtoTemplateResponseTextColorEnumTypeTransformer] instance.
  static DtoTemplateResponseTextColorEnumTypeTransformer? _instance;
}


