package webview

import (
	"context"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v4"

	"clipcrow/essentialworkware/common/logger"
	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
)

func (s *webViewService) FindByID(ctx context.Context, databaseID uuid.UUID) (dto.WebViewResponse, error) {
	db := s.c.GetRepository(ctx)
	webView := model.WebView{}
	webViewRes := dto.WebViewResponse{}
	err := db.Where("database_id = ?", databaseID).First(&webView).Error
	if err != nil {
		return webViewRes, err
	}
	webViewRes.GetFrom(&webView)
	if len(webView.Secret) > 0 {
		encrypter, err := s.c.GetEncryptionFactory().NewClient(ctx)
		if err != nil {
			return webViewRes, errors.Wrap(err, "error creating encrypter instance")
		}
		decrypted, err := encrypter.Decrypt(webView.Secret)
		if err != nil {
			return webViewRes, errors.Wrap(err, "error decrypting Webhook.Secret")
		}
		webViewRes.Secret = null.StringFrom(string(decrypted))
		if err := encrypter.Close(); err != nil {
			s.c.GetLogger().WithContext(ctx).Warn("error closing encrypter instance", logger.FieldMap{logger.ErrorKey: err})
		}
	}
	return webViewRes, nil
}

// FindByIDs fetches multiple web views in a single batch operation
func (s *webViewService) FindByIDs(ctx context.Context, databaseIDs []uuid.UUID) (map[uuid.UUID]dto.WebViewResponse, error) {
	if len(databaseIDs) == 0 {
		return make(map[uuid.UUID]dto.WebViewResponse), nil
	}

	db := s.c.GetRepository(ctx)
	webViews := []model.WebView{}

	err := db.Where("database_id IN ?", databaseIDs).Find(&webViews).Error
	if err != nil {
		return nil, err
	}

	// Build result map
	result := make(map[uuid.UUID]dto.WebViewResponse)
	for _, webView := range webViews {
		webViewRes := dto.WebViewResponse{}
		webViewRes.GetFrom(&webView)

		// Handle secret decryption if needed
		if len(webView.Secret) > 0 {
			encrypter, err := s.c.GetEncryptionFactory().NewClient(ctx)
			if err != nil {
				s.c.GetLogger().WithContext(ctx).Error("error creating encrypter instance", logger.FieldMap{logger.ErrorKey: err})
				continue
			}
			decrypted, err := encrypter.Decrypt(webView.Secret)
			if err != nil {
				s.c.GetLogger().WithContext(ctx).Error("error decrypting Webhook.Secret", logger.FieldMap{logger.ErrorKey: err})
				if err := encrypter.Close(); err != nil {
					s.c.GetLogger().WithContext(ctx).Warn("error closing encrypter instance", logger.FieldMap{logger.ErrorKey: err})
				}
				continue
			}
			webViewRes.Secret = null.StringFrom(string(decrypted))
			if err := encrypter.Close(); err != nil {
				s.c.GetLogger().WithContext(ctx).Warn("error closing encrypter instance", logger.FieldMap{logger.ErrorKey: err})
			}
		}

		result[webView.DatabaseID] = webViewRes
	}

	return result, nil
}
