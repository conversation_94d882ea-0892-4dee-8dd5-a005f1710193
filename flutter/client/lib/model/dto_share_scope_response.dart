//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoShareScopeResponse {
  /// Returns a new [DtoShareScopeResponse] instance.
  DtoShareScopeResponse({
    this.shareScopeMembersResponse = const [],
    this.shareScopeTagsResponse = const [],
  });

  List<DtoShareScopeMemberResponse> shareScopeMembersResponse;

  List<DtoShareScopeTagResponse> shareScopeTagsResponse;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoShareScopeResponse &&
     other.shareScopeMembersResponse == shareScopeMembersResponse &&
     other.shareScopeTagsResponse == shareScopeTagsResponse;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (shareScopeMembersResponse.hashCode) +
    (shareScopeTagsResponse.hashCode);

  @override
  String toString() => 'DtoShareScopeResponse[shareScopeMembersResponse=$shareScopeMembersResponse, shareScopeTagsResponse=$shareScopeTagsResponse]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'share_scope_members_response'] = this.shareScopeMembersResponse;
      json[r'share_scope_tags_response'] = this.shareScopeTagsResponse;
    return json;
  }

  /// Returns a new [DtoShareScopeResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoShareScopeResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoShareScopeResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoShareScopeResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoShareScopeResponse(
        shareScopeMembersResponse: DtoShareScopeMemberResponse.listFromJson(json[r'share_scope_members_response']) ?? const [],
        shareScopeTagsResponse: DtoShareScopeTagResponse.listFromJson(json[r'share_scope_tags_response']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoShareScopeResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoShareScopeResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoShareScopeResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoShareScopeResponse> mapFromJson(dynamic json) {
    final map = <String, DtoShareScopeResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoShareScopeResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoShareScopeResponse-objects as value to a dart map
  static Map<String, List<DtoShareScopeResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoShareScopeResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoShareScopeResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

