//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagGroupColor {
  /// Returns a new [DtoTagGroupColor] instance.
  DtoTagGroupColor({
    this.color,
    this.isDefault,
    this.tagGroupId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? color;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isDefault;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? tagGroupId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagGroupColor &&
     other.color == color &&
     other.isDefault == isDefault &&
     other.tagGroupId == tagGroupId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (color == null ? 0 : color!.hashCode) +
    (isDefault == null ? 0 : isDefault!.hashCode) +
    (tagGroupId == null ? 0 : tagGroupId!.hashCode);

  @override
  String toString() => 'DtoTagGroupColor[color=$color, isDefault=$isDefault, tagGroupId=$tagGroupId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.color != null) {
      json[r'color'] = this.color;
    } else {
      json[r'color'] = null;
    }
    if (this.isDefault != null) {
      json[r'is_default'] = this.isDefault;
    } else {
      json[r'is_default'] = null;
    }
    if (this.tagGroupId != null) {
      json[r'tag_group_id'] = this.tagGroupId;
    } else {
      json[r'tag_group_id'] = null;
    }
    return json;
  }

  /// Returns a new [DtoTagGroupColor] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagGroupColor? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagGroupColor[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagGroupColor[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagGroupColor(
        color: mapValueOfType<String>(json, r'color'),
        isDefault: mapValueOfType<bool>(json, r'is_default'),
        tagGroupId: mapValueOfType<String>(json, r'tag_group_id'),
      );
    }
    return null;
  }

  static List<DtoTagGroupColor>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagGroupColor>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagGroupColor.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagGroupColor> mapFromJson(dynamic json) {
    final map = <String, DtoTagGroupColor>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroupColor.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagGroupColor-objects as value to a dart map
  static Map<String, List<DtoTagGroupColor>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagGroupColor>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagGroupColor.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

