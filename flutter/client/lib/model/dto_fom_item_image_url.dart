//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoFomItemImageURL {
  /// Returns a new [DtoFomItemImageURL] instance.
  DtoFomItemImageURL({
    this.imagePath,
    this.sizeOriginal,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? imagePath;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? sizeOriginal;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoFomItemImageURL &&
     other.imagePath == imagePath &&
     other.sizeOriginal == sizeOriginal;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (imagePath == null ? 0 : imagePath!.hashCode) +
    (sizeOriginal == null ? 0 : sizeOriginal!.hashCode);

  @override
  String toString() => 'DtoFomItemImageURL[imagePath=$imagePath, sizeOriginal=$sizeOriginal]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.imagePath != null) {
      json[r'image_path'] = this.imagePath;
    } else {
      json[r'image_path'] = null;
    }
    if (this.sizeOriginal != null) {
      json[r'size_original'] = this.sizeOriginal;
    } else {
      json[r'size_original'] = null;
    }
    return json;
  }

  /// Returns a new [DtoFomItemImageURL] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoFomItemImageURL? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoFomItemImageURL[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoFomItemImageURL[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoFomItemImageURL(
        imagePath: mapValueOfType<String>(json, r'image_path'),
        sizeOriginal: mapValueOfType<String>(json, r'size_original'),
      );
    }
    return null;
  }

  static List<DtoFomItemImageURL>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoFomItemImageURL>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoFomItemImageURL.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoFomItemImageURL> mapFromJson(dynamic json) {
    final map = <String, DtoFomItemImageURL>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFomItemImageURL.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoFomItemImageURL-objects as value to a dart map
  static Map<String, List<DtoFomItemImageURL>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoFomItemImageURL>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFomItemImageURL.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

