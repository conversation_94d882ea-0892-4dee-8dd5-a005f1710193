//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelMentionedTag {
  /// Returns a new [ModelMentionedTag] instance.
  ModelMentionedTag({
    this.deletedAt,
    required this.id,
    this.name,
    this.tagGroupId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? tagGroupId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelMentionedTag &&
     other.deletedAt == deletedAt &&
     other.id == id &&
     other.name == name &&
     other.tagGroupId == tagGroupId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (id.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (tagGroupId == null ? 0 : tagGroupId!.hashCode);

  @override
  String toString() => 'ModelMentionedTag[deletedAt=$deletedAt, id=$id, name=$name, tagGroupId=$tagGroupId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'id'] = this.id;
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    if (this.tagGroupId != null) {
      json[r'tag_group_id'] = this.tagGroupId;
    } else {
      json[r'tag_group_id'] = null;
    }
    return json;
  }

  /// Returns a new [ModelMentionedTag] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelMentionedTag? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelMentionedTag[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelMentionedTag[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelMentionedTag(
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        id: mapValueOfType<String>(json, r'id')!,
        name: mapValueOfType<String>(json, r'name'),
        tagGroupId: mapValueOfType<String>(json, r'tag_group_id'),
      );
    }
    return null;
  }

  static List<ModelMentionedTag>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMentionedTag>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMentionedTag.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelMentionedTag> mapFromJson(dynamic json) {
    final map = <String, ModelMentionedTag>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMentionedTag.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelMentionedTag-objects as value to a dart map
  static Map<String, List<ModelMentionedTag>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelMentionedTag>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMentionedTag.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
  };
}

