//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoTagResponse {
  /// Returns a new [DtoTagResponse] instance.
  DtoTagResponse({
    required this.id,
    required this.tagGroupId,
    required this.value,
  });

  String id;

  String tagGroupId;

  String value;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoTagResponse &&
     other.id == id &&
     other.tagGroupId == tagGroupId &&
     other.value == value;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id.hashCode) +
    (tagGroupId.hashCode) +
    (value.hashCode);

  @override
  String toString() => 'DtoTagResponse[id=$id, tagGroupId=$tagGroupId, value=$value]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'id'] = this.id;
      json[r'tag_group_id'] = this.tagGroupId;
      json[r'value'] = this.value;
    return json;
  }

  /// Returns a new [DtoTagResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoTagResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoTagResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoTagResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoTagResponse(
        id: mapValueOfType<String>(json, r'id')!,
        tagGroupId: mapValueOfType<String>(json, r'tag_group_id')!,
        value: mapValueOfType<String>(json, r'value')!,
      );
    }
    return null;
  }

  static List<DtoTagResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoTagResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoTagResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoTagResponse> mapFromJson(dynamic json) {
    final map = <String, DtoTagResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoTagResponse-objects as value to a dart map
  static Map<String, List<DtoTagResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoTagResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoTagResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
    'tag_group_id',
    'value',
  };
}

