//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelRecordAssigneeReportFormItem {
  /// Returns a new [ModelRecordAssigneeReportFormItem] instance.
  ModelRecordAssigneeReportFormItem({
    required this.createdAt,
    this.dataObj,
    this.deletedAt,
    required this.formItemId,
    required this.recordAssigneeId,
    required this.updatedAt,
  });

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? dataObj;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String formItemId;

  String recordAssigneeId;

  String updatedAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelRecordAssigneeReportFormItem &&
     other.createdAt == createdAt &&
     other.dataObj == dataObj &&
     other.deletedAt == deletedAt &&
     other.formItemId == formItemId &&
     other.recordAssigneeId == recordAssigneeId &&
     other.updatedAt == updatedAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (createdAt.hashCode) +
    (dataObj == null ? 0 : dataObj!.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (formItemId.hashCode) +
    (recordAssigneeId.hashCode) +
    (updatedAt.hashCode);

  @override
  String toString() => 'ModelRecordAssigneeReportFormItem[createdAt=$createdAt, dataObj=$dataObj, deletedAt=$deletedAt, formItemId=$formItemId, recordAssigneeId=$recordAssigneeId, updatedAt=$updatedAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'created_at'] = this.createdAt;
    if (this.dataObj != null) {
      json[r'data_obj'] = this.dataObj;
    } else {
      json[r'data_obj'] = null;
    }
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'form_item_id'] = this.formItemId;
      json[r'record_assignee_id'] = this.recordAssigneeId;
      json[r'updated_at'] = this.updatedAt;
    return json;
  }

  /// Returns a new [ModelRecordAssigneeReportFormItem] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelRecordAssigneeReportFormItem? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelRecordAssigneeReportFormItem[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelRecordAssigneeReportFormItem[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelRecordAssigneeReportFormItem(
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        dataObj: mapValueOfType<Object>(json, r'data_obj'),
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        formItemId: mapValueOfType<String>(json, r'form_item_id')!,
        recordAssigneeId: mapValueOfType<String>(json, r'record_assignee_id')!,
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
      );
    }
    return null;
  }

  static List<ModelRecordAssigneeReportFormItem>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelRecordAssigneeReportFormItem>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelRecordAssigneeReportFormItem.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelRecordAssigneeReportFormItem> mapFromJson(dynamic json) {
    final map = <String, ModelRecordAssigneeReportFormItem>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelRecordAssigneeReportFormItem.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelRecordAssigneeReportFormItem-objects as value to a dart map
  static Map<String, List<ModelRecordAssigneeReportFormItem>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelRecordAssigneeReportFormItem>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelRecordAssigneeReportFormItem.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'created_at',
    'form_item_id',
    'record_assignee_id',
    'updated_at',
  };
}

