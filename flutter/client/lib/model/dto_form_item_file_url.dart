//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoFormItemFileURL {
  /// Returns a new [DtoFormItemFileURL] instance.
  DtoFormItemFileURL({
    this.filePath,
    this.signedUrl,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? filePath;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? signedUrl;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoFormItemFileURL &&
     other.filePath == filePath &&
     other.signedUrl == signedUrl;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (filePath == null ? 0 : filePath!.hashCode) +
    (signedUrl == null ? 0 : signedUrl!.hashCode);

  @override
  String toString() => 'DtoFormItemFileURL[filePath=$filePath, signedUrl=$signedUrl]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.filePath != null) {
      json[r'file_path'] = this.filePath;
    } else {
      json[r'file_path'] = null;
    }
    if (this.signedUrl != null) {
      json[r'signed_url'] = this.signedUrl;
    } else {
      json[r'signed_url'] = null;
    }
    return json;
  }

  /// Returns a new [DtoFormItemFileURL] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoFormItemFileURL? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoFormItemFileURL[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoFormItemFileURL[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoFormItemFileURL(
        filePath: mapValueOfType<String>(json, r'file_path'),
        signedUrl: mapValueOfType<String>(json, r'signed_url'),
      );
    }
    return null;
  }

  static List<DtoFormItemFileURL>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoFormItemFileURL>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoFormItemFileURL.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoFormItemFileURL> mapFromJson(dynamic json) {
    final map = <String, DtoFormItemFileURL>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFormItemFileURL.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoFormItemFileURL-objects as value to a dart map
  static Map<String, List<DtoFormItemFileURL>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoFormItemFileURL>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoFormItemFileURL.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

