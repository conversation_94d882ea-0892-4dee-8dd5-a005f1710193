//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelRecord {
  /// Returns a new [ModelRecord] instance.
  ModelRecord({
    this.additionalFormItems = const [],
    this.background,
    this.backgroundKey,
    this.completedAt,
    this.createdAt,
    this.createdByOAuthId,
    this.creator,
    required this.creatorId,
    this.databaseId,
    this.deletedAt,
    this.description,
    this.doArchive,
    required this.dueDate,
    this.estimatedTime,
    this.hasNewMessage,
    this.id,
    this.illustration,
    this.lastMessageTime,
    this.manageProgressAsTask,
    this.manageStatusIndividually,
    required this.name,
    this.opacity,
    this.originId,
    this.originType,
    this.pausedAt,
    this.primaryColor,
    this.recordAssignees = const [],
    this.resumedAt,
    this.reverseBackground,
    this.rootId,
    this.rootType,
    this.secondaryColor,
    this.sortRank,
    this.startedAt,
    this.status,
    this.tagNumber,
    this.templateId,
    this.textAlignment,
    this.textColor,
    this.timingEnabled,
    this.totalWorkTime,
    this.updateableFormItems = const [],
    this.updatedAt,
    this.workspaceId,
  });

  List<ModelFormItem> additionalFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? completedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? createdByOAuthId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelMember? creator;

  String creatorId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? databaseId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? doArchive;

  String dueDate;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? estimatedTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasNewMessage;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? lastMessageTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? manageProgressAsTask;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? manageStatusIndividually;

  String name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? originId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? originType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? pausedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  List<ModelRecordAssignee> recordAssignees;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? resumedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? rootId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? rootType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? sortRank;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? startedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? status;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? templateId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  ModelRecordTextColorEnum? textColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? timingEnabled;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? totalWorkTime;

  List<ModelFormItem> updateableFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? updatedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelRecord &&
     other.additionalFormItems == additionalFormItems &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.completedAt == completedAt &&
     other.createdAt == createdAt &&
     other.createdByOAuthId == createdByOAuthId &&
     other.creator == creator &&
     other.creatorId == creatorId &&
     other.databaseId == databaseId &&
     other.deletedAt == deletedAt &&
     other.description == description &&
     other.doArchive == doArchive &&
     other.dueDate == dueDate &&
     other.estimatedTime == estimatedTime &&
     other.hasNewMessage == hasNewMessage &&
     other.id == id &&
     other.illustration == illustration &&
     other.lastMessageTime == lastMessageTime &&
     other.manageProgressAsTask == manageProgressAsTask &&
     other.manageStatusIndividually == manageStatusIndividually &&
     other.name == name &&
     other.opacity == opacity &&
     other.originId == originId &&
     other.originType == originType &&
     other.pausedAt == pausedAt &&
     other.primaryColor == primaryColor &&
     other.recordAssignees == recordAssignees &&
     other.resumedAt == resumedAt &&
     other.reverseBackground == reverseBackground &&
     other.rootId == rootId &&
     other.rootType == rootType &&
     other.secondaryColor == secondaryColor &&
     other.sortRank == sortRank &&
     other.startedAt == startedAt &&
     other.status == status &&
     other.tagNumber == tagNumber &&
     other.templateId == templateId &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.timingEnabled == timingEnabled &&
     other.totalWorkTime == totalWorkTime &&
     other.updateableFormItems == updateableFormItems &&
     other.updatedAt == updatedAt &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (additionalFormItems.hashCode) +
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (completedAt == null ? 0 : completedAt!.hashCode) +
    (createdAt == null ? 0 : createdAt!.hashCode) +
    (createdByOAuthId == null ? 0 : createdByOAuthId!.hashCode) +
    (creator == null ? 0 : creator!.hashCode) +
    (creatorId.hashCode) +
    (databaseId == null ? 0 : databaseId!.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (doArchive == null ? 0 : doArchive!.hashCode) +
    (dueDate.hashCode) +
    (estimatedTime == null ? 0 : estimatedTime!.hashCode) +
    (hasNewMessage == null ? 0 : hasNewMessage!.hashCode) +
    (id == null ? 0 : id!.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (lastMessageTime == null ? 0 : lastMessageTime!.hashCode) +
    (manageProgressAsTask == null ? 0 : manageProgressAsTask!.hashCode) +
    (manageStatusIndividually == null ? 0 : manageStatusIndividually!.hashCode) +
    (name.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (originId == null ? 0 : originId!.hashCode) +
    (originType == null ? 0 : originType!.hashCode) +
    (pausedAt == null ? 0 : pausedAt!.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (recordAssignees.hashCode) +
    (resumedAt == null ? 0 : resumedAt!.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (rootId == null ? 0 : rootId!.hashCode) +
    (rootType == null ? 0 : rootType!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (sortRank == null ? 0 : sortRank!.hashCode) +
    (startedAt == null ? 0 : startedAt!.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (templateId == null ? 0 : templateId!.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (timingEnabled == null ? 0 : timingEnabled!.hashCode) +
    (totalWorkTime == null ? 0 : totalWorkTime!.hashCode) +
    (updateableFormItems.hashCode) +
    (updatedAt == null ? 0 : updatedAt!.hashCode) +
    (workspaceId == null ? 0 : workspaceId!.hashCode);

  @override
  String toString() => 'ModelRecord[additionalFormItems=$additionalFormItems, background=$background, backgroundKey=$backgroundKey, completedAt=$completedAt, createdAt=$createdAt, createdByOAuthId=$createdByOAuthId, creator=$creator, creatorId=$creatorId, databaseId=$databaseId, deletedAt=$deletedAt, description=$description, doArchive=$doArchive, dueDate=$dueDate, estimatedTime=$estimatedTime, hasNewMessage=$hasNewMessage, id=$id, illustration=$illustration, lastMessageTime=$lastMessageTime, manageProgressAsTask=$manageProgressAsTask, manageStatusIndividually=$manageStatusIndividually, name=$name, opacity=$opacity, originId=$originId, originType=$originType, pausedAt=$pausedAt, primaryColor=$primaryColor, recordAssignees=$recordAssignees, resumedAt=$resumedAt, reverseBackground=$reverseBackground, rootId=$rootId, rootType=$rootType, secondaryColor=$secondaryColor, sortRank=$sortRank, startedAt=$startedAt, status=$status, tagNumber=$tagNumber, templateId=$templateId, textAlignment=$textAlignment, textColor=$textColor, timingEnabled=$timingEnabled, totalWorkTime=$totalWorkTime, updateableFormItems=$updateableFormItems, updatedAt=$updatedAt, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'additional_form_items'] = this.additionalFormItems;
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
    if (this.completedAt != null) {
      json[r'completed_at'] = this.completedAt;
    } else {
      json[r'completed_at'] = null;
    }
    if (this.createdAt != null) {
      json[r'created_at'] = this.createdAt;
    } else {
      json[r'created_at'] = null;
    }
    if (this.createdByOAuthId != null) {
      json[r'created_by_o_auth_id'] = this.createdByOAuthId;
    } else {
      json[r'created_by_o_auth_id'] = null;
    }
    if (this.creator != null) {
      json[r'creator'] = this.creator;
    } else {
      json[r'creator'] = null;
    }
      json[r'creator_id'] = this.creatorId;
    if (this.databaseId != null) {
      json[r'database_id'] = this.databaseId;
    } else {
      json[r'database_id'] = null;
    }
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
    if (this.doArchive != null) {
      json[r'do_archive'] = this.doArchive;
    } else {
      json[r'do_archive'] = null;
    }
      json[r'due_date'] = this.dueDate;
    if (this.estimatedTime != null) {
      json[r'estimated_time'] = this.estimatedTime;
    } else {
      json[r'estimated_time'] = null;
    }
    if (this.hasNewMessage != null) {
      json[r'has_new_message'] = this.hasNewMessage;
    } else {
      json[r'has_new_message'] = null;
    }
    if (this.id != null) {
      json[r'id'] = this.id;
    } else {
      json[r'id'] = null;
    }
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
    if (this.lastMessageTime != null) {
      json[r'last_message_time'] = this.lastMessageTime;
    } else {
      json[r'last_message_time'] = null;
    }
    if (this.manageProgressAsTask != null) {
      json[r'manage_progress_as_task'] = this.manageProgressAsTask;
    } else {
      json[r'manage_progress_as_task'] = null;
    }
    if (this.manageStatusIndividually != null) {
      json[r'manage_status_individually'] = this.manageStatusIndividually;
    } else {
      json[r'manage_status_individually'] = null;
    }
      json[r'name'] = this.name;
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
    if (this.originId != null) {
      json[r'origin_id'] = this.originId;
    } else {
      json[r'origin_id'] = null;
    }
    if (this.originType != null) {
      json[r'origin_type'] = this.originType;
    } else {
      json[r'origin_type'] = null;
    }
    if (this.pausedAt != null) {
      json[r'paused_at'] = this.pausedAt;
    } else {
      json[r'paused_at'] = null;
    }
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
      json[r'record_assignees'] = this.recordAssignees;
    if (this.resumedAt != null) {
      json[r'resumed_at'] = this.resumedAt;
    } else {
      json[r'resumed_at'] = null;
    }
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.rootId != null) {
      json[r'root_id'] = this.rootId;
    } else {
      json[r'root_id'] = null;
    }
    if (this.rootType != null) {
      json[r'root_type'] = this.rootType;
    } else {
      json[r'root_type'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
    if (this.sortRank != null) {
      json[r'sort_rank'] = this.sortRank;
    } else {
      json[r'sort_rank'] = null;
    }
    if (this.startedAt != null) {
      json[r'started_at'] = this.startedAt;
    } else {
      json[r'started_at'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
    if (this.templateId != null) {
      json[r'template_id'] = this.templateId;
    } else {
      json[r'template_id'] = null;
    }
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
    if (this.timingEnabled != null) {
      json[r'timing_enabled'] = this.timingEnabled;
    } else {
      json[r'timing_enabled'] = null;
    }
    if (this.totalWorkTime != null) {
      json[r'total_work_time'] = this.totalWorkTime;
    } else {
      json[r'total_work_time'] = null;
    }
      json[r'updateable_form_items'] = this.updateableFormItems;
    if (this.updatedAt != null) {
      json[r'updated_at'] = this.updatedAt;
    } else {
      json[r'updated_at'] = null;
    }
    if (this.workspaceId != null) {
      json[r'workspace_id'] = this.workspaceId;
    } else {
      json[r'workspace_id'] = null;
    }
    return json;
  }

  /// Returns a new [ModelRecord] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelRecord? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelRecord[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelRecord[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelRecord(
        additionalFormItems: ModelFormItem.listFromJson(json[r'additional_form_items']) ?? const [],
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        completedAt: mapValueOfType<String>(json, r'completed_at'),
        createdAt: mapValueOfType<String>(json, r'created_at'),
        createdByOAuthId: mapValueOfType<String>(json, r'created_by_o_auth_id'),
        creator: ModelMember.fromJson(json[r'creator']),
        creatorId: mapValueOfType<String>(json, r'creator_id')!,
        databaseId: mapValueOfType<String>(json, r'database_id'),
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        description: mapValueOfType<String>(json, r'description'),
        doArchive: mapValueOfType<bool>(json, r'do_archive'),
        dueDate: mapValueOfType<String>(json, r'due_date')!,
        estimatedTime: mapValueOfType<int>(json, r'estimated_time'),
        hasNewMessage: mapValueOfType<bool>(json, r'has_new_message'),
        id: mapValueOfType<String>(json, r'id'),
        illustration: mapValueOfType<String>(json, r'illustration'),
        lastMessageTime: mapValueOfType<String>(json, r'last_message_time'),
        manageProgressAsTask: mapValueOfType<bool>(json, r'manage_progress_as_task'),
        manageStatusIndividually: mapValueOfType<bool>(json, r'manage_status_individually'),
        name: mapValueOfType<String>(json, r'name')!,
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        originId: mapValueOfType<String>(json, r'origin_id'),
        originType: mapValueOfType<String>(json, r'origin_type'),
        pausedAt: mapValueOfType<String>(json, r'paused_at'),
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        recordAssignees: ModelRecordAssignee.listFromJson(json[r'record_assignees']) ?? const [],
        resumedAt: mapValueOfType<String>(json, r'resumed_at'),
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        rootId: mapValueOfType<String>(json, r'root_id'),
        rootType: mapValueOfType<String>(json, r'root_type'),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        sortRank: mapValueOfType<String>(json, r'sort_rank'),
        startedAt: mapValueOfType<String>(json, r'started_at'),
        status: mapValueOfType<String>(json, r'status'),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        templateId: mapValueOfType<String>(json, r'template_id'),
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: ModelRecordTextColorEnum.fromJson(json[r'text_color']),
        timingEnabled: mapValueOfType<bool>(json, r'timing_enabled'),
        totalWorkTime: mapValueOfType<int>(json, r'total_work_time'),
        updateableFormItems: ModelFormItem.listFromJson(json[r'updateable_form_items']) ?? const [],
        updatedAt: mapValueOfType<String>(json, r'updated_at'),
        workspaceId: mapValueOfType<String>(json, r'workspace_id'),
      );
    }
    return null;
  }

  static List<ModelRecord>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelRecord>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelRecord.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelRecord> mapFromJson(dynamic json) {
    final map = <String, ModelRecord>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelRecord.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelRecord-objects as value to a dart map
  static Map<String, List<ModelRecord>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelRecord>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelRecord.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'creator_id',
    'due_date',
    'name',
  };
}


class ModelRecordTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelRecordTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = ModelRecordTextColorEnum._(r'DARK');
  static const LIGHT = ModelRecordTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][ModelRecordTextColorEnum].
  static const values = <ModelRecordTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static ModelRecordTextColorEnum? fromJson(dynamic value) => ModelRecordTextColorEnumTypeTransformer().decode(value);

  static List<ModelRecordTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelRecordTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelRecordTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelRecordTextColorEnum] to String,
/// and [decode] dynamic data back to [ModelRecordTextColorEnum].
class ModelRecordTextColorEnumTypeTransformer {
  factory ModelRecordTextColorEnumTypeTransformer() => _instance ??= const ModelRecordTextColorEnumTypeTransformer._();

  const ModelRecordTextColorEnumTypeTransformer._();

  String encode(ModelRecordTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelRecordTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelRecordTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return ModelRecordTextColorEnum.DARK;
        case r'LIGHT': return ModelRecordTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelRecordTextColorEnumTypeTransformer] instance.
  static ModelRecordTextColorEnumTypeTransformer? _instance;
}


