# openapi.model.DtoExternalLinkIcon

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**formItems** | [**List<ModelFormItem>**](ModelFormItem.md) |  | [optional] [default to const []]
**icon** | **String** |  | [optional] 
**iconKey** | **String** |  | [optional] 
**id** | **String** |  | 
**memberBookmarkedItemId** | **String** |  | [optional] 
**name** | **String** |  | 
**tagNumber** | **int** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


