//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class RecordsApi {
  RecordsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Get Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<Response> workspacesWorkspaceIdRecordsByTagTagNumberGetWithHttpInfo(String workspaceId, String tagNumber,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/by-tag/{tag_number}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{tag_number}', tagNumber);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] tagNumber (required):
  ///   Tag Number
  Future<DtoRecord?> workspacesWorkspaceIdRecordsByTagTagNumberGet(String workspaceId, String tagNumber,) async {
    final response = await workspacesWorkspaceIdRecordsByTagTagNumberGetWithHttpInfo(workspaceId, tagNumber,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Post Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostRecordRequest] body (required):
  ///   Post Record Request
  Future<Response> workspacesWorkspaceIdRecordsPostWithHttpInfo(String workspaceId, DtoPostRecordRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Post Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoPostRecordRequest] body (required):
  ///   Post Record Request
  Future<DtoRecord?> workspacesWorkspaceIdRecordsPost(String workspaceId, DtoPostRecordRequest body,) async {
    final response = await workspacesWorkspaceIdRecordsPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Get Records
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetRecordsReq] body (required):
  ///   Record Request
  Future<Response> workspacesWorkspaceIdRecordsQueryPostWithHttpInfo(String workspaceId, DtoGetRecordsReq body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/query'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Records
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [DtoGetRecordsReq] body (required):
  ///   Record Request
  Future<DtoRecordsResponse?> workspacesWorkspaceIdRecordsQueryPost(String workspaceId, DtoGetRecordsReq body,) async {
    final response = await workspacesWorkspaceIdRecordsQueryPostWithHttpInfo(workspaceId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecordsResponse',) as DtoRecordsResponse;
    
    }
    return null;
  }

  /// Update Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [DtoPatchRecordRequestByVisibleMember] body (required):
  ///   Record Update object
  Future<Response> workspacesWorkspaceIdRecordsRecordIdArchivePatchWithHttpInfo(String workspaceId, String recordId, DtoPatchRecordRequestByVisibleMember body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/archive'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [DtoPatchRecordRequestByVisibleMember] body (required):
  ///   Record Update object
  Future<DtoRecord?> workspacesWorkspaceIdRecordsRecordIdArchivePatch(String workspaceId, String recordId, DtoPatchRecordRequestByVisibleMember body,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdArchivePatchWithHttpInfo(workspaceId, recordId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Delete Background
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<Response> workspacesWorkspaceIdRecordsRecordIdBackgroundSignedUrlDeleteWithHttpInfo(String workspaceId, String recordId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Background
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<void> workspacesWorkspaceIdRecordsRecordIdBackgroundSignedUrlDelete(String workspaceId, String recordId,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdBackgroundSignedUrlDeleteWithHttpInfo(workspaceId, recordId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Create Background signed url
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<Response> workspacesWorkspaceIdRecordsRecordIdBackgroundSignedUrlPostWithHttpInfo(String workspaceId, String recordId, String method,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/background-signed-url'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'method', method));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Create Background signed url
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [String] method (required):
  ///   Method is GET or PUT
  Future<DtoSignedURLResponse?> workspacesWorkspaceIdRecordsRecordIdBackgroundSignedUrlPost(String workspaceId, String recordId, String method,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdBackgroundSignedUrlPostWithHttpInfo(workspaceId, recordId, method,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoSignedURLResponse',) as DtoSignedURLResponse;
    
    }
    return null;
  }

  /// Delete Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<Response> workspacesWorkspaceIdRecordsRecordIdDeleteWithHttpInfo(String workspaceId, String recordId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Delete Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<void> workspacesWorkspaceIdRecordsRecordIdDelete(String workspaceId, String recordId,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdDeleteWithHttpInfo(workspaceId, recordId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Update Record Force Complete
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<Response> workspacesWorkspaceIdRecordsRecordIdForceCompletePostWithHttpInfo(String workspaceId, String recordId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/force-complete'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Record Force Complete
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<DtoManageRecordDetail?> workspacesWorkspaceIdRecordsRecordIdForceCompletePost(String workspaceId, String recordId,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdForceCompletePostWithHttpInfo(workspaceId, recordId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoManageRecordDetail',) as DtoManageRecordDetail;
    
    }
    return null;
  }

  /// Get Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<Response> workspacesWorkspaceIdRecordsRecordIdGetWithHttpInfo(String workspaceId, String recordId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<DtoRecord?> workspacesWorkspaceIdRecordsRecordIdGet(String workspaceId, String recordId,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdGetWithHttpInfo(workspaceId, recordId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Get Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<Response> workspacesWorkspaceIdRecordsRecordIdManageMemberDetailGetWithHttpInfo(String workspaceId, String recordId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/manage-member-detail'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  Future<DtoManageRecordDetail?> workspacesWorkspaceIdRecordsRecordIdManageMemberDetailGet(String workspaceId, String recordId,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdManageMemberDetailGetWithHttpInfo(workspaceId, recordId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoManageRecordDetail',) as DtoManageRecordDetail;
    
    }
    return null;
  }

  /// Update Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [DtoPatchRecordRequest] body (required):
  ///   Record Update object
  ///
  /// * [bool] isOnlyUpdateUpdateableFormItems:
  Future<Response> workspacesWorkspaceIdRecordsRecordIdPatchWithHttpInfo(String workspaceId, String recordId, DtoPatchRecordRequest body, { bool? isOnlyUpdateUpdateableFormItems, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (isOnlyUpdateUpdateableFormItems != null) {
      queryParams.addAll(_queryParams('', 'is_only_update_updateable_form_items', isOnlyUpdateUpdateableFormItems));
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [DtoPatchRecordRequest] body (required):
  ///   Record Update object
  ///
  /// * [bool] isOnlyUpdateUpdateableFormItems:
  Future<DtoRecord?> workspacesWorkspaceIdRecordsRecordIdPatch(String workspaceId, String recordId, DtoPatchRecordRequest body, { bool? isOnlyUpdateUpdateableFormItems, }) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdPatchWithHttpInfo(workspaceId, recordId, body,  isOnlyUpdateUpdateableFormItems: isOnlyUpdateUpdateableFormItems, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Get record's pic detail
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   record ID
  ///
  /// * [String] memberId (required):
  ///   member ID
  Future<Response> workspacesWorkspaceIdRecordsRecordIdPicMemberIdGetWithHttpInfo(String workspaceId, String recordId, String memberId,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/pic/{member_id}'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId)
      .replaceAll('{member_id}', memberId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get record's pic detail
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   record ID
  ///
  /// * [String] memberId (required):
  ///   member ID
  Future<DtoRecordPICDetail?> workspacesWorkspaceIdRecordsRecordIdPicMemberIdGet(String workspaceId, String recordId, String memberId,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdPicMemberIdGetWithHttpInfo(workspaceId, recordId, memberId,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecordPICDetail',) as DtoRecordPICDetail;
    
    }
    return null;
  }

  /// Update Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [DtoUpdatePICRequest] body (required):
  ///   UpdatePICRequest request object
  Future<Response> workspacesWorkspaceIdRecordsRecordIdPicPutWithHttpInfo(String workspaceId, String recordId, DtoUpdatePICRequest body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/pic'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   Record ID
  ///
  /// * [DtoUpdatePICRequest] body (required):
  ///   UpdatePICRequest request object
  Future<DtoRecord?> workspacesWorkspaceIdRecordsRecordIdPicPut(String workspaceId, String recordId, DtoUpdatePICRequest body,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdPicPutWithHttpInfo(workspaceId, recordId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Update Status Record of PIC
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   record ID
  ///
  /// * [DtoPatchRecordRequestBySelf] body (required):
  ///   PatchRecordRequestBySelf request object
  Future<Response> workspacesWorkspaceIdRecordsRecordIdSelfPatchWithHttpInfo(String workspaceId, String recordId, DtoPatchRecordRequestBySelf body,) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/{record_id}/self'
      .replaceAll('{workspace_id}', workspaceId)
      .replaceAll('{record_id}', recordId);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PATCH',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Update Status Record of PIC
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] recordId (required):
  ///   record ID
  ///
  /// * [DtoPatchRecordRequestBySelf] body (required):
  ///   PatchRecordRequestBySelf request object
  Future<DtoRecord?> workspacesWorkspaceIdRecordsRecordIdSelfPatch(String workspaceId, String recordId, DtoPatchRecordRequestBySelf body,) async {
    final response = await workspacesWorkspaceIdRecordsRecordIdSelfPatchWithHttpInfo(workspaceId, recordId, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoRecord',) as DtoRecord;
    
    }
    return null;
  }

  /// Get summary Record
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] fromDate (required):
  ///
  /// * [String] toDate (required):
  ///
  /// * [String] facilitator:
  ///
  /// * [List<String>] measureTimes:
  ///
  /// * [List<String>] pic:
  ///
  /// * [String] query:
  ///
  /// * [String] status:
  ///
  /// * [List<String>] tagIds:
  ///
  /// * [String] templateId:
  Future<Response> workspacesWorkspaceIdRecordsSummaryGetWithHttpInfo(String workspaceId, String fromDate, String toDate, { String? facilitator, List<String>? measureTimes, List<String>? pic, String? query, String? status, List<String>? tagIds, String? templateId, }) async {
    // ignore: prefer_const_declarations
    final path = r'/workspaces/{workspace_id}/records/summary'
      .replaceAll('{workspace_id}', workspaceId);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (facilitator != null) {
      queryParams.addAll(_queryParams('', 'facilitator', facilitator));
    }
      queryParams.addAll(_queryParams('', 'from_date', fromDate));
    if (measureTimes != null) {
      queryParams.addAll(_queryParams('csv', 'measure_times', measureTimes));
    }
    if (pic != null) {
      queryParams.addAll(_queryParams('csv', 'pic', pic));
    }
    if (query != null) {
      queryParams.addAll(_queryParams('', 'query', query));
    }
    if (status != null) {
      queryParams.addAll(_queryParams('', 'status', status));
    }
    if (tagIds != null) {
      queryParams.addAll(_queryParams('csv', 'tag_ids', tagIds));
    }
    if (templateId != null) {
      queryParams.addAll(_queryParams('', 'template_id', templateId));
    }
      queryParams.addAll(_queryParams('', 'to_date', toDate));

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Get summary Record
  ///
  /// Parameters:
  ///
  /// * [String] workspaceId (required):
  ///   Workspace ID
  ///
  /// * [String] fromDate (required):
  ///
  /// * [String] toDate (required):
  ///
  /// * [String] facilitator:
  ///
  /// * [List<String>] measureTimes:
  ///
  /// * [List<String>] pic:
  ///
  /// * [String] query:
  ///
  /// * [String] status:
  ///
  /// * [List<String>] tagIds:
  ///
  /// * [String] templateId:
  Future<DtoGetSummaryRecordResponse?> workspacesWorkspaceIdRecordsSummaryGet(String workspaceId, String fromDate, String toDate, { String? facilitator, List<String>? measureTimes, List<String>? pic, String? query, String? status, List<String>? tagIds, String? templateId, }) async {
    final response = await workspacesWorkspaceIdRecordsSummaryGetWithHttpInfo(workspaceId, fromDate, toDate,  facilitator: facilitator, measureTimes: measureTimes, pic: pic, query: query, status: status, tagIds: tagIds, templateId: templateId, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'DtoGetSummaryRecordResponse',) as DtoGetSummaryRecordResponse;
    
    }
    return null;
  }
}
