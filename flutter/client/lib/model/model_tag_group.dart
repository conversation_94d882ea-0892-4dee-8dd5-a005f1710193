//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelTagGroup {
  /// Returns a new [ModelTagGroup] instance.
  ModelTagGroup({
    this.background,
    this.backgroundKey,
    required this.color,
    required this.createdAt,
    this.deletedAt,
    this.description,
    this.displayOnCard,
    this.hasNewMessage,
    required this.id,
    this.illustration,
    this.informationFormItems = const [],
    this.isDefault,
    required this.name,
    this.onlySingleValueIsSelected,
    this.opacity,
    this.primaryColor,
    this.reverseBackground,
    this.secondaryColor,
    this.tagNumber,
    this.tags = const [],
    this.textAlignment,
    this.textColor,
    required this.updatedAt,
    required this.workspaceId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? background;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? backgroundKey;

  String color;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? description;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? displayOnCard;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasNewMessage;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? illustration;

  List<ModelFormItem> informationFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isDefault;

  String name;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? onlySingleValueIsSelected;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  num? opacity;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? primaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? reverseBackground;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? secondaryColor;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  List<ModelTag> tags;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textAlignment;

  ModelTagGroupTextColorEnum? textColor;

  String updatedAt;

  String workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelTagGroup &&
     other.background == background &&
     other.backgroundKey == backgroundKey &&
     other.color == color &&
     other.createdAt == createdAt &&
     other.deletedAt == deletedAt &&
     other.description == description &&
     other.displayOnCard == displayOnCard &&
     other.hasNewMessage == hasNewMessage &&
     other.id == id &&
     other.illustration == illustration &&
     other.informationFormItems == informationFormItems &&
     other.isDefault == isDefault &&
     other.name == name &&
     other.onlySingleValueIsSelected == onlySingleValueIsSelected &&
     other.opacity == opacity &&
     other.primaryColor == primaryColor &&
     other.reverseBackground == reverseBackground &&
     other.secondaryColor == secondaryColor &&
     other.tagNumber == tagNumber &&
     other.tags == tags &&
     other.textAlignment == textAlignment &&
     other.textColor == textColor &&
     other.updatedAt == updatedAt &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (background == null ? 0 : background!.hashCode) +
    (backgroundKey == null ? 0 : backgroundKey!.hashCode) +
    (color.hashCode) +
    (createdAt.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (description == null ? 0 : description!.hashCode) +
    (displayOnCard == null ? 0 : displayOnCard!.hashCode) +
    (hasNewMessage == null ? 0 : hasNewMessage!.hashCode) +
    (id.hashCode) +
    (illustration == null ? 0 : illustration!.hashCode) +
    (informationFormItems.hashCode) +
    (isDefault == null ? 0 : isDefault!.hashCode) +
    (name.hashCode) +
    (onlySingleValueIsSelected == null ? 0 : onlySingleValueIsSelected!.hashCode) +
    (opacity == null ? 0 : opacity!.hashCode) +
    (primaryColor == null ? 0 : primaryColor!.hashCode) +
    (reverseBackground == null ? 0 : reverseBackground!.hashCode) +
    (secondaryColor == null ? 0 : secondaryColor!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (tags.hashCode) +
    (textAlignment == null ? 0 : textAlignment!.hashCode) +
    (textColor == null ? 0 : textColor!.hashCode) +
    (updatedAt.hashCode) +
    (workspaceId.hashCode);

  @override
  String toString() => 'ModelTagGroup[background=$background, backgroundKey=$backgroundKey, color=$color, createdAt=$createdAt, deletedAt=$deletedAt, description=$description, displayOnCard=$displayOnCard, hasNewMessage=$hasNewMessage, id=$id, illustration=$illustration, informationFormItems=$informationFormItems, isDefault=$isDefault, name=$name, onlySingleValueIsSelected=$onlySingleValueIsSelected, opacity=$opacity, primaryColor=$primaryColor, reverseBackground=$reverseBackground, secondaryColor=$secondaryColor, tagNumber=$tagNumber, tags=$tags, textAlignment=$textAlignment, textColor=$textColor, updatedAt=$updatedAt, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.background != null) {
      json[r'background'] = this.background;
    } else {
      json[r'background'] = null;
    }
    if (this.backgroundKey != null) {
      json[r'background_key'] = this.backgroundKey;
    } else {
      json[r'background_key'] = null;
    }
      json[r'color'] = this.color;
      json[r'created_at'] = this.createdAt;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.description != null) {
      json[r'description'] = this.description;
    } else {
      json[r'description'] = null;
    }
    if (this.displayOnCard != null) {
      json[r'display_on_card'] = this.displayOnCard;
    } else {
      json[r'display_on_card'] = null;
    }
    if (this.hasNewMessage != null) {
      json[r'has_new_message'] = this.hasNewMessage;
    } else {
      json[r'has_new_message'] = null;
    }
      json[r'id'] = this.id;
    if (this.illustration != null) {
      json[r'illustration'] = this.illustration;
    } else {
      json[r'illustration'] = null;
    }
      json[r'information_form_items'] = this.informationFormItems;
    if (this.isDefault != null) {
      json[r'is_default'] = this.isDefault;
    } else {
      json[r'is_default'] = null;
    }
      json[r'name'] = this.name;
    if (this.onlySingleValueIsSelected != null) {
      json[r'only_single_value_is_selected'] = this.onlySingleValueIsSelected;
    } else {
      json[r'only_single_value_is_selected'] = null;
    }
    if (this.opacity != null) {
      json[r'opacity'] = this.opacity;
    } else {
      json[r'opacity'] = null;
    }
    if (this.primaryColor != null) {
      json[r'primary_color'] = this.primaryColor;
    } else {
      json[r'primary_color'] = null;
    }
    if (this.reverseBackground != null) {
      json[r'reverse_background'] = this.reverseBackground;
    } else {
      json[r'reverse_background'] = null;
    }
    if (this.secondaryColor != null) {
      json[r'secondary_color'] = this.secondaryColor;
    } else {
      json[r'secondary_color'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
      json[r'tags'] = this.tags;
    if (this.textAlignment != null) {
      json[r'text_alignment'] = this.textAlignment;
    } else {
      json[r'text_alignment'] = null;
    }
    if (this.textColor != null) {
      json[r'text_color'] = this.textColor;
    } else {
      json[r'text_color'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
      json[r'workspace_id'] = this.workspaceId;
    return json;
  }

  /// Returns a new [ModelTagGroup] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelTagGroup? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelTagGroup[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelTagGroup[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelTagGroup(
        background: mapValueOfType<String>(json, r'background'),
        backgroundKey: mapValueOfType<String>(json, r'background_key'),
        color: mapValueOfType<String>(json, r'color')!,
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        description: mapValueOfType<String>(json, r'description'),
        displayOnCard: mapValueOfType<bool>(json, r'display_on_card'),
        hasNewMessage: mapValueOfType<bool>(json, r'has_new_message'),
        id: mapValueOfType<String>(json, r'id')!,
        illustration: mapValueOfType<String>(json, r'illustration'),
        informationFormItems: ModelFormItem.listFromJson(json[r'information_form_items']) ?? const [],
        isDefault: mapValueOfType<bool>(json, r'is_default'),
        name: mapValueOfType<String>(json, r'name')!,
        onlySingleValueIsSelected: mapValueOfType<bool>(json, r'only_single_value_is_selected'),
        opacity: json[r'opacity'] == null
            ? null
            : num.parse(json[r'opacity'].toString()),
        primaryColor: mapValueOfType<String>(json, r'primary_color'),
        reverseBackground: mapValueOfType<bool>(json, r'reverse_background'),
        secondaryColor: mapValueOfType<String>(json, r'secondary_color'),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        tags: ModelTag.listFromJson(json[r'tags']) ?? const [],
        textAlignment: mapValueOfType<String>(json, r'text_alignment'),
        textColor: ModelTagGroupTextColorEnum.fromJson(json[r'text_color']),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        workspaceId: mapValueOfType<String>(json, r'workspace_id')!,
      );
    }
    return null;
  }

  static List<ModelTagGroup>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelTagGroup>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelTagGroup.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelTagGroup> mapFromJson(dynamic json) {
    final map = <String, ModelTagGroup>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelTagGroup.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelTagGroup-objects as value to a dart map
  static Map<String, List<ModelTagGroup>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelTagGroup>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelTagGroup.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'color',
    'created_at',
    'id',
    'name',
    'updated_at',
    'workspace_id',
  };
}


class ModelTagGroupTextColorEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelTagGroupTextColorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const DARK = ModelTagGroupTextColorEnum._(r'DARK');
  static const LIGHT = ModelTagGroupTextColorEnum._(r'LIGHT');

  /// List of all possible values in this [enum][ModelTagGroupTextColorEnum].
  static const values = <ModelTagGroupTextColorEnum>[
    DARK,
    LIGHT,
  ];

  static ModelTagGroupTextColorEnum? fromJson(dynamic value) => ModelTagGroupTextColorEnumTypeTransformer().decode(value);

  static List<ModelTagGroupTextColorEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelTagGroupTextColorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelTagGroupTextColorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelTagGroupTextColorEnum] to String,
/// and [decode] dynamic data back to [ModelTagGroupTextColorEnum].
class ModelTagGroupTextColorEnumTypeTransformer {
  factory ModelTagGroupTextColorEnumTypeTransformer() => _instance ??= const ModelTagGroupTextColorEnumTypeTransformer._();

  const ModelTagGroupTextColorEnumTypeTransformer._();

  String encode(ModelTagGroupTextColorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelTagGroupTextColorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelTagGroupTextColorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'DARK': return ModelTagGroupTextColorEnum.DARK;
        case r'LIGHT': return ModelTagGroupTextColorEnum.LIGHT;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelTagGroupTextColorEnumTypeTransformer] instance.
  static ModelTagGroupTextColorEnumTypeTransformer? _instance;
}


