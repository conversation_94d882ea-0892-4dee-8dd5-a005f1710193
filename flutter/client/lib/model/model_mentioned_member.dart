//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelMentionedMember {
  /// Returns a new [ModelMentionedMember] instance.
  ModelMentionedMember({
    this.email,
    this.icon,
    this.iconKey,
    required this.id,
    this.name,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? email;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? icon;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? iconKey;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelMentionedMember &&
     other.email == email &&
     other.icon == icon &&
     other.iconKey == iconKey &&
     other.id == id &&
     other.name == name;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (email == null ? 0 : email!.hashCode) +
    (icon == null ? 0 : icon!.hashCode) +
    (iconKey == null ? 0 : iconKey!.hashCode) +
    (id.hashCode) +
    (name == null ? 0 : name!.hashCode);

  @override
  String toString() => 'ModelMentionedMember[email=$email, icon=$icon, iconKey=$iconKey, id=$id, name=$name]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.email != null) {
      json[r'email'] = this.email;
    } else {
      json[r'email'] = null;
    }
    if (this.icon != null) {
      json[r'icon'] = this.icon;
    } else {
      json[r'icon'] = null;
    }
    if (this.iconKey != null) {
      json[r'icon_key'] = this.iconKey;
    } else {
      json[r'icon_key'] = null;
    }
      json[r'id'] = this.id;
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    return json;
  }

  /// Returns a new [ModelMentionedMember] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelMentionedMember? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelMentionedMember[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelMentionedMember[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelMentionedMember(
        email: mapValueOfType<String>(json, r'email'),
        icon: mapValueOfType<String>(json, r'icon'),
        iconKey: mapValueOfType<String>(json, r'icon_key'),
        id: mapValueOfType<String>(json, r'id')!,
        name: mapValueOfType<String>(json, r'name'),
      );
    }
    return null;
  }

  static List<ModelMentionedMember>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMentionedMember>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMentionedMember.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelMentionedMember> mapFromJson(dynamic json) {
    final map = <String, ModelMentionedMember>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMentionedMember.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelMentionedMember-objects as value to a dart map
  static Map<String, List<ModelMentionedMember>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelMentionedMember>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMentionedMember.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
  };
}

