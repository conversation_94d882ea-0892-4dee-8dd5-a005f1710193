//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetMemberBookmarkedItemsResponse {
  /// Returns a new [DtoGetMemberBookmarkedItemsResponse] instance.
  DtoGetMemberBookmarkedItemsResponse({
    this.hasMore,
    this.items = const [],
    this.total,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasMore;

  List<DtoMemberBookmarkedItem> items;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? total;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetMemberBookmarkedItemsResponse &&
     other.hasMore == hasMore &&
     other.items == items &&
     other.total == total;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (hasMore == null ? 0 : hasMore!.hashCode) +
    (items.hashCode) +
    (total == null ? 0 : total!.hashCode);

  @override
  String toString() => 'DtoGetMemberBookmarkedItemsResponse[hasMore=$hasMore, items=$items, total=$total]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.hasMore != null) {
      json[r'has_more'] = this.hasMore;
    } else {
      json[r'has_more'] = null;
    }
      json[r'items'] = this.items;
    if (this.total != null) {
      json[r'total'] = this.total;
    } else {
      json[r'total'] = null;
    }
    return json;
  }

  /// Returns a new [DtoGetMemberBookmarkedItemsResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetMemberBookmarkedItemsResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetMemberBookmarkedItemsResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetMemberBookmarkedItemsResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetMemberBookmarkedItemsResponse(
        hasMore: mapValueOfType<bool>(json, r'has_more'),
        items: DtoMemberBookmarkedItem.listFromJson(json[r'items']) ?? const [],
        total: mapValueOfType<int>(json, r'total'),
      );
    }
    return null;
  }

  static List<DtoGetMemberBookmarkedItemsResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetMemberBookmarkedItemsResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetMemberBookmarkedItemsResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetMemberBookmarkedItemsResponse> mapFromJson(dynamic json) {
    final map = <String, DtoGetMemberBookmarkedItemsResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetMemberBookmarkedItemsResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetMemberBookmarkedItemsResponse-objects as value to a dart map
  static Map<String, List<DtoGetMemberBookmarkedItemsResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetMemberBookmarkedItemsResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetMemberBookmarkedItemsResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

