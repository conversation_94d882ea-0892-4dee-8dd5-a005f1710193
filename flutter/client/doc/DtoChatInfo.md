# openapi.model.DtoChatInfo

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**createdAt** | **String** |  | [optional] 
**deletedAt** | **int** |  | [optional] 
**groupId** | **String** |  | [optional] 
**id** | **String** |  | [optional] 
**isPrivateChat** | **bool** |  | [optional] 
**lastMessTime** | **String** | LatestUsers     pq.StringArray        `json:\"latest_users,omitempty\" gorm:\"type:varchar(64)[]\"` | [optional] 
**latestMessage** | [**ModelChat**](ModelChat.md) |  | [optional] 
**latestUsers** | [**List<ModelActor>**](ModelActor.md) | LatestUsers     []*ChatInfoMember `json:\"latest_users,omitempty\"` | [optional] [default to const []]
**participantIds** | **List<String>** |  | [optional] [default to const []]
**targetId** | **String** |  | [optional] 
**targetType** | **String** |  | [optional] 
**totalLogs** | **int** |  | [optional] 
**totalMess** | **int** |  | [optional] 
**totalMessages** | **int** |  | [optional] 
**updatedAt** | **String** |  | [optional] 
**workspaceId** | **String** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


