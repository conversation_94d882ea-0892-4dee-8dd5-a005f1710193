//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MapsLatLngBounds {
  /// Returns a new [MapsLatLngBounds] instance.
  MapsLatLngBounds({
    this.northeast,
    this.southwest,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  GooglemapsGithubIoMapsLatLng? northeast;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  GooglemapsGithubIoMapsLatLng? southwest;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MapsLatLngBounds &&
     other.northeast == northeast &&
     other.southwest == southwest;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (northeast == null ? 0 : northeast!.hashCode) +
    (southwest == null ? 0 : southwest!.hashCode);

  @override
  String toString() => 'MapsLatLngBounds[northeast=$northeast, southwest=$southwest]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.northeast != null) {
      json[r'northeast'] = this.northeast;
    } else {
      json[r'northeast'] = null;
    }
    if (this.southwest != null) {
      json[r'southwest'] = this.southwest;
    } else {
      json[r'southwest'] = null;
    }
    return json;
  }

  /// Returns a new [MapsLatLngBounds] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MapsLatLngBounds? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MapsLatLngBounds[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MapsLatLngBounds[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MapsLatLngBounds(
        northeast: GooglemapsGithubIoMapsLatLng.fromJson(json[r'northeast']),
        southwest: GooglemapsGithubIoMapsLatLng.fromJson(json[r'southwest']),
      );
    }
    return null;
  }

  static List<MapsLatLngBounds>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MapsLatLngBounds>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MapsLatLngBounds.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MapsLatLngBounds> mapFromJson(dynamic json) {
    final map = <String, MapsLatLngBounds>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsLatLngBounds.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MapsLatLngBounds-objects as value to a dart map
  static Map<String, List<MapsLatLngBounds>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MapsLatLngBounds>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsLatLngBounds.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

