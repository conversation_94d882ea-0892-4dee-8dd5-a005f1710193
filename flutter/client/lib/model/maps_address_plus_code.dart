//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class MapsAddressPlusCode {
  /// Returns a new [MapsAddressPlusCode] instance.
  MapsAddressPlusCode({
    this.compoundCode,
    this.globalCode,
  });

  /// CompoundCode is a 6 character or longer local code with an explicit location (CWC8+R9, Mountain View, CA, USA).
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? compoundCode;

  /// GlobalCode is a 4 character area code and 6 character or longer local code (849VCWC8+R9).
  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? globalCode;

  @override
  bool operator ==(Object other) => identical(this, other) || other is MapsAddressPlusCode &&
     other.compoundCode == compoundCode &&
     other.globalCode == globalCode;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (compoundCode == null ? 0 : compoundCode!.hashCode) +
    (globalCode == null ? 0 : globalCode!.hashCode);

  @override
  String toString() => 'MapsAddressPlusCode[compoundCode=$compoundCode, globalCode=$globalCode]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.compoundCode != null) {
      json[r'compound_code'] = this.compoundCode;
    } else {
      json[r'compound_code'] = null;
    }
    if (this.globalCode != null) {
      json[r'global_code'] = this.globalCode;
    } else {
      json[r'global_code'] = null;
    }
    return json;
  }

  /// Returns a new [MapsAddressPlusCode] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static MapsAddressPlusCode? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "MapsAddressPlusCode[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "MapsAddressPlusCode[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return MapsAddressPlusCode(
        compoundCode: mapValueOfType<String>(json, r'compound_code'),
        globalCode: mapValueOfType<String>(json, r'global_code'),
      );
    }
    return null;
  }

  static List<MapsAddressPlusCode>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <MapsAddressPlusCode>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = MapsAddressPlusCode.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, MapsAddressPlusCode> mapFromJson(dynamic json) {
    final map = <String, MapsAddressPlusCode>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsAddressPlusCode.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of MapsAddressPlusCode-objects as value to a dart map
  static Map<String, List<MapsAddressPlusCode>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<MapsAddressPlusCode>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = MapsAddressPlusCode.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

