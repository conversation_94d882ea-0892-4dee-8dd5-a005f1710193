//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoRecordQueryResponse {
  /// Returns a new [DtoRecordQueryResponse] instance.
  DtoRecordQueryResponse({
    this.assignee,
    this.bookmark,
    this.facilitationStatus,
    this.isArchive,
    this.measureTime,
    this.status,
    this.taskManagement,
    this.templateId,
    this.textSearch,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? assignee;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? bookmark;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? facilitationStatus;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? isArchive;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? measureTime;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? status;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? taskManagement;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? templateId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? textSearch;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoRecordQueryResponse &&
     other.assignee == assignee &&
     other.bookmark == bookmark &&
     other.facilitationStatus == facilitationStatus &&
     other.isArchive == isArchive &&
     other.measureTime == measureTime &&
     other.status == status &&
     other.taskManagement == taskManagement &&
     other.templateId == templateId &&
     other.textSearch == textSearch;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (assignee == null ? 0 : assignee!.hashCode) +
    (bookmark == null ? 0 : bookmark!.hashCode) +
    (facilitationStatus == null ? 0 : facilitationStatus!.hashCode) +
    (isArchive == null ? 0 : isArchive!.hashCode) +
    (measureTime == null ? 0 : measureTime!.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (taskManagement == null ? 0 : taskManagement!.hashCode) +
    (templateId == null ? 0 : templateId!.hashCode) +
    (textSearch == null ? 0 : textSearch!.hashCode);

  @override
  String toString() => 'DtoRecordQueryResponse[assignee=$assignee, bookmark=$bookmark, facilitationStatus=$facilitationStatus, isArchive=$isArchive, measureTime=$measureTime, status=$status, taskManagement=$taskManagement, templateId=$templateId, textSearch=$textSearch]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.assignee != null) {
      json[r'assignee'] = this.assignee;
    } else {
      json[r'assignee'] = null;
    }
    if (this.bookmark != null) {
      json[r'bookmark'] = this.bookmark;
    } else {
      json[r'bookmark'] = null;
    }
    if (this.facilitationStatus != null) {
      json[r'facilitation_status'] = this.facilitationStatus;
    } else {
      json[r'facilitation_status'] = null;
    }
    if (this.isArchive != null) {
      json[r'is_archive'] = this.isArchive;
    } else {
      json[r'is_archive'] = null;
    }
    if (this.measureTime != null) {
      json[r'measure_time'] = this.measureTime;
    } else {
      json[r'measure_time'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    if (this.taskManagement != null) {
      json[r'task_management'] = this.taskManagement;
    } else {
      json[r'task_management'] = null;
    }
    if (this.templateId != null) {
      json[r'template_id'] = this.templateId;
    } else {
      json[r'template_id'] = null;
    }
    if (this.textSearch != null) {
      json[r'text_search'] = this.textSearch;
    } else {
      json[r'text_search'] = null;
    }
    return json;
  }

  /// Returns a new [DtoRecordQueryResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoRecordQueryResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoRecordQueryResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoRecordQueryResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoRecordQueryResponse(
        assignee: mapValueOfType<String>(json, r'assignee'),
        bookmark: mapValueOfType<String>(json, r'bookmark'),
        facilitationStatus: mapValueOfType<String>(json, r'facilitation_status'),
        isArchive: mapValueOfType<bool>(json, r'is_archive'),
        measureTime: mapValueOfType<String>(json, r'measure_time'),
        status: mapValueOfType<String>(json, r'status'),
        taskManagement: mapValueOfType<String>(json, r'task_management'),
        templateId: mapValueOfType<String>(json, r'template_id'),
        textSearch: mapValueOfType<String>(json, r'text_search'),
      );
    }
    return null;
  }

  static List<DtoRecordQueryResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoRecordQueryResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoRecordQueryResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoRecordQueryResponse> mapFromJson(dynamic json) {
    final map = <String, DtoRecordQueryResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoRecordQueryResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoRecordQueryResponse-objects as value to a dart map
  static Map<String, List<DtoRecordQueryResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoRecordQueryResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoRecordQueryResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

