//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelChat {
  /// Returns a new [ModelChat] instance.
  ModelChat({
    this.actor,
    required this.actorId,
    this.actorType,
    this.additionalData,
    required this.createdAt,
    this.deletedAt,
    this.doArchive,
    this.groupId,
    this.hasThread,
    required this.id,
    this.mentionedExternalLinks = const [],
    this.mentionedMembers = const [],
    this.mentionedTags = const [],
    this.message,
    this.messageType,
    this.metadata,
    this.rootTargetId,
    this.rootTargetType,
    this.tagNumber,
    required this.targetId,
    this.targetType,
    required this.updatedAt,
    required this.workspaceId,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  ModelActor? actor;

  String actorId;

  ModelChatActorTypeEnum? actorType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? additionalData;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? doArchive;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? groupId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? hasThread;

  String id;

  List<ModelMentionedExternalLink> mentionedExternalLinks;

  List<ModelMentionedMember> mentionedMembers;

  List<ModelMentionedTag> mentionedTags;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? message;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? messageType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  Object? metadata;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? rootTargetId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? rootTargetType;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? tagNumber;

  String targetId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? targetType;

  String updatedAt;

  String workspaceId;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelChat &&
     other.actor == actor &&
     other.actorId == actorId &&
     other.actorType == actorType &&
     other.additionalData == additionalData &&
     other.createdAt == createdAt &&
     other.deletedAt == deletedAt &&
     other.doArchive == doArchive &&
     other.groupId == groupId &&
     other.hasThread == hasThread &&
     other.id == id &&
     other.mentionedExternalLinks == mentionedExternalLinks &&
     other.mentionedMembers == mentionedMembers &&
     other.mentionedTags == mentionedTags &&
     other.message == message &&
     other.messageType == messageType &&
     other.metadata == metadata &&
     other.rootTargetId == rootTargetId &&
     other.rootTargetType == rootTargetType &&
     other.tagNumber == tagNumber &&
     other.targetId == targetId &&
     other.targetType == targetType &&
     other.updatedAt == updatedAt &&
     other.workspaceId == workspaceId;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (actor == null ? 0 : actor!.hashCode) +
    (actorId.hashCode) +
    (actorType == null ? 0 : actorType!.hashCode) +
    (additionalData == null ? 0 : additionalData!.hashCode) +
    (createdAt.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (doArchive == null ? 0 : doArchive!.hashCode) +
    (groupId == null ? 0 : groupId!.hashCode) +
    (hasThread == null ? 0 : hasThread!.hashCode) +
    (id.hashCode) +
    (mentionedExternalLinks.hashCode) +
    (mentionedMembers.hashCode) +
    (mentionedTags.hashCode) +
    (message == null ? 0 : message!.hashCode) +
    (messageType == null ? 0 : messageType!.hashCode) +
    (metadata == null ? 0 : metadata!.hashCode) +
    (rootTargetId == null ? 0 : rootTargetId!.hashCode) +
    (rootTargetType == null ? 0 : rootTargetType!.hashCode) +
    (tagNumber == null ? 0 : tagNumber!.hashCode) +
    (targetId.hashCode) +
    (targetType == null ? 0 : targetType!.hashCode) +
    (updatedAt.hashCode) +
    (workspaceId.hashCode);

  @override
  String toString() => 'ModelChat[actor=$actor, actorId=$actorId, actorType=$actorType, additionalData=$additionalData, createdAt=$createdAt, deletedAt=$deletedAt, doArchive=$doArchive, groupId=$groupId, hasThread=$hasThread, id=$id, mentionedExternalLinks=$mentionedExternalLinks, mentionedMembers=$mentionedMembers, mentionedTags=$mentionedTags, message=$message, messageType=$messageType, metadata=$metadata, rootTargetId=$rootTargetId, rootTargetType=$rootTargetType, tagNumber=$tagNumber, targetId=$targetId, targetType=$targetType, updatedAt=$updatedAt, workspaceId=$workspaceId]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.actor != null) {
      json[r'actor'] = this.actor;
    } else {
      json[r'actor'] = null;
    }
      json[r'actor_id'] = this.actorId;
    if (this.actorType != null) {
      json[r'actor_type'] = this.actorType;
    } else {
      json[r'actor_type'] = null;
    }
    if (this.additionalData != null) {
      json[r'additional_data'] = this.additionalData;
    } else {
      json[r'additional_data'] = null;
    }
      json[r'created_at'] = this.createdAt;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.doArchive != null) {
      json[r'do_archive'] = this.doArchive;
    } else {
      json[r'do_archive'] = null;
    }
    if (this.groupId != null) {
      json[r'group_id'] = this.groupId;
    } else {
      json[r'group_id'] = null;
    }
    if (this.hasThread != null) {
      json[r'has_thread'] = this.hasThread;
    } else {
      json[r'has_thread'] = null;
    }
      json[r'id'] = this.id;
      json[r'mentioned_external_links'] = this.mentionedExternalLinks;
      json[r'mentioned_members'] = this.mentionedMembers;
      json[r'mentioned_tags'] = this.mentionedTags;
    if (this.message != null) {
      json[r'message'] = this.message;
    } else {
      json[r'message'] = null;
    }
    if (this.messageType != null) {
      json[r'message_type'] = this.messageType;
    } else {
      json[r'message_type'] = null;
    }
    if (this.metadata != null) {
      json[r'metadata'] = this.metadata;
    } else {
      json[r'metadata'] = null;
    }
    if (this.rootTargetId != null) {
      json[r'root_target_id'] = this.rootTargetId;
    } else {
      json[r'root_target_id'] = null;
    }
    if (this.rootTargetType != null) {
      json[r'root_target_type'] = this.rootTargetType;
    } else {
      json[r'root_target_type'] = null;
    }
    if (this.tagNumber != null) {
      json[r'tag_number'] = this.tagNumber;
    } else {
      json[r'tag_number'] = null;
    }
      json[r'target_id'] = this.targetId;
    if (this.targetType != null) {
      json[r'target_type'] = this.targetType;
    } else {
      json[r'target_type'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
      json[r'workspace_id'] = this.workspaceId;
    return json;
  }

  /// Returns a new [ModelChat] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelChat? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelChat[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelChat[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelChat(
        actor: ModelActor.fromJson(json[r'actor']),
        actorId: mapValueOfType<String>(json, r'actor_id')!,
        actorType: ModelChatActorTypeEnum.fromJson(json[r'actor_type']),
        additionalData: mapValueOfType<Object>(json, r'additional_data'),
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        doArchive: mapValueOfType<bool>(json, r'do_archive'),
        groupId: mapValueOfType<String>(json, r'group_id'),
        hasThread: mapValueOfType<bool>(json, r'has_thread'),
        id: mapValueOfType<String>(json, r'id')!,
        mentionedExternalLinks: ModelMentionedExternalLink.listFromJson(json[r'mentioned_external_links']) ?? const [],
        mentionedMembers: ModelMentionedMember.listFromJson(json[r'mentioned_members']) ?? const [],
        mentionedTags: ModelMentionedTag.listFromJson(json[r'mentioned_tags']) ?? const [],
        message: mapValueOfType<String>(json, r'message'),
        messageType: mapValueOfType<String>(json, r'message_type'),
        metadata: mapValueOfType<Object>(json, r'metadata'),
        rootTargetId: mapValueOfType<String>(json, r'root_target_id'),
        rootTargetType: mapValueOfType<String>(json, r'root_target_type'),
        tagNumber: mapValueOfType<int>(json, r'tag_number'),
        targetId: mapValueOfType<String>(json, r'target_id')!,
        targetType: mapValueOfType<String>(json, r'target_type'),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
        workspaceId: mapValueOfType<String>(json, r'workspace_id')!,
      );
    }
    return null;
  }

  static List<ModelChat>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelChat>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelChat.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelChat> mapFromJson(dynamic json) {
    final map = <String, ModelChat>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelChat.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelChat-objects as value to a dart map
  static Map<String, List<ModelChat>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelChat>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelChat.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'actor_id',
    'created_at',
    'id',
    'target_id',
    'updated_at',
    'workspace_id',
  };
}


class ModelChatActorTypeEnum {
  /// Instantiate a new enum with the provided [value].
  const ModelChatActorTypeEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const members = ModelChatActorTypeEnum._(r'members');
  static const externalLinks = ModelChatActorTypeEnum._(r'external_links');
  static const seatBot = ModelChatActorTypeEnum._(r'seat_bot');

  /// List of all possible values in this [enum][ModelChatActorTypeEnum].
  static const values = <ModelChatActorTypeEnum>[
    members,
    externalLinks,
    seatBot,
  ];

  static ModelChatActorTypeEnum? fromJson(dynamic value) => ModelChatActorTypeEnumTypeTransformer().decode(value);

  static List<ModelChatActorTypeEnum>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelChatActorTypeEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelChatActorTypeEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [ModelChatActorTypeEnum] to String,
/// and [decode] dynamic data back to [ModelChatActorTypeEnum].
class ModelChatActorTypeEnumTypeTransformer {
  factory ModelChatActorTypeEnumTypeTransformer() => _instance ??= const ModelChatActorTypeEnumTypeTransformer._();

  const ModelChatActorTypeEnumTypeTransformer._();

  String encode(ModelChatActorTypeEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a ModelChatActorTypeEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  ModelChatActorTypeEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'members': return ModelChatActorTypeEnum.members;
        case r'external_links': return ModelChatActorTypeEnum.externalLinks;
        case r'seat_bot': return ModelChatActorTypeEnum.seatBot;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [ModelChatActorTypeEnumTypeTransformer] instance.
  static ModelChatActorTypeEnumTypeTransformer? _instance;
}


