package member

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"golang.org/x/exp/slices"
	"gopkg.in/guregu/null.v4"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	memberCommon "clipcrow/essentialworkware/common/member"
	"clipcrow/essentialworkware/common/sliceutil"
	"clipcrow/essentialworkware/model"
	"clipcrow/essentialworkware/model/dto"
	"clipcrow/essentialworkware/model/enum/formitemtype"
	"clipcrow/essentialworkware/model/enum/logaction"
	"clipcrow/essentialworkware/model/enum/memberrole"
	"clipcrow/essentialworkware/model/enum/memberstatus"
	"clipcrow/essentialworkware/model/enum/messagetype"
	"clipcrow/essentialworkware/model/enum/targettype"
	"clipcrow/essentialworkware/pkg/apihelper"
)

func (s *memberService) FindByEmailAndWorkspaceID(ctx context.Context, email string, workspaceID uuid.UUID) (*model.Member, error) {
	db := s.c.GetRepository(ctx)

	var member *model.Member
	result := db.Preload("Workspace").Where("email = ? AND workspace_id = ? AND is_ghost IS NOT true", email, workspaceID).First(&member)

	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "error getting member")
	}

	return member, nil
}

func (s *memberService) FindByWorkspaceIDAndIDInternal(ctx context.Context, workspaceID uuid.UUID, memberID uuid.UUID) (*model.Member, error) {
	db := s.c.GetRepository(ctx)

	member := &model.Member{
		ID:          memberID,
		WorkspaceID: workspaceID,
	}

	result := db.
		Preload("Tags", db.DefaultPreloadOrderTag()).
		Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("OptionalFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("MemberFormItems").Where("is_ghost IS NOT true").
		First(&member)
	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "error getting member")
	}

	return member, nil
}

func (s *memberService) FindByWorkspaceIDAndID(ctx context.Context, workspaceID uuid.UUID, requesterID, memberID uuid.UUID, isContainRelationInfo bool) (dto.Member, error) {
	db := s.c.GetRepository(ctx)
	var requesterRole string
	var targetMemberRole string
	err := db.Model(&model.Member{}).Select("role").Where("id = ?", requesterID).Scan(&requesterRole).Error
	if err != nil {
		return dto.Member{}, err
	}
	err = db.Model(&model.Member{}).Select("role").Where("id = ?", memberID).Scan(&targetMemberRole).Error
	if err != nil {
		return dto.Member{}, err
	}
	if requesterRole == memberrole.GUEST && memberID != requesterID && targetMemberRole == memberrole.GUEST {
		// GUEST can not view other GUEST member
		return dto.Member{}, errors.New("insufficient permissions to view member")
	}
	memberResponse, err := s.GetMemberResponse(ctx, requesterID, memberID, false, false)
	if isContainRelationInfo {
		mInfo, err := s.getInfoRelationMemberID(ctx, requesterID, memberID)
		if err != nil {
			return dto.Member{}, err
		}
		memberResponse.MemberInfoRelation = mInfo
	}
	return memberResponse, err
}
func (s *memberService) FindByWorkspaceIDAndTagNumber(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, tagNumber string, isContainRelationInfo bool) (dto.Member, error) {
	db := s.c.GetRepository(ctx)
	var requesterRole string
	var targetMemberRole string
	err := db.Model(&model.Member{}).Select("role").Where("id = ?", requesterID).Scan(&requesterRole).Error
	if err != nil {
		return dto.Member{}, err
	}

	// convert tagNumber (string) to null.Int as required by model.Record.TagNumber
	tagNumberInt, convErr := strconv.ParseInt(tagNumber, 10, 64)
	if convErr != nil {
		return dto.Member{}, errors.Wrap(convErr, "invalid tag number")
	}
	targetMember := &model.Member{
		WorkspaceID: workspaceID,
		TagNumber:   null.IntFrom(tagNumberInt),
	}
	if err := db.
		Model(&model.Member{}).
		Where("workspace_id = ? AND tag_number = ?", workspaceID, tagNumberInt).
		First(targetMember).Error; err != nil {
		return dto.Member{}, err
	}
	targetMemberRole = targetMember.Role
	if requesterRole == memberrole.GUEST && targetMember.ID != requesterID && targetMemberRole == memberrole.GUEST {
		// GUEST can not view other GUEST member
		return dto.Member{}, errors.New("insufficient permissions to view member")
	}
	memberResponse, err := s.GetMemberResponse(ctx, requesterID, targetMember.ID, false, false)
	if isContainRelationInfo {
		mInfo, err := s.getInfoRelationMemberID(ctx, requesterID, targetMember.ID)
		if err != nil {
			return dto.Member{}, err
		}
		memberResponse.MemberInfoRelation = mInfo
	}
	return memberResponse, err
}

func (s *memberService) FindByWorkspaceIDAndIDAndIncludeGhostMember(ctx context.Context, workspaceID uuid.UUID, memberID uuid.UUID) (*model.Member, error) {
	db := s.c.GetRepository(ctx)

	member := &model.Member{
		ID:          memberID,
		WorkspaceID: workspaceID,
	}

	result := db.
		Preload("Tags", db.DefaultPreloadOrderTag()).
		Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("OptionalFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("MemberFormItems").
		First(&member)
	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "error getting member")
	}

	return member, nil
}

func (s *memberService) FindByWorkspaceIDAndGetTeamBodyRequest(ctx context.Context, workspaceID, requesterID uuid.UUID, requesterRole string, q dto.GetTeamBodyRequest) (*dto.GetTeamResponse, error) {
	db := s.c.GetRepository(ctx)

	tx := db.Model(&model.Member{}).
		Joins("INNER JOIN member_tags ON member_tags.member_id = members.id").
		Where(&model.Member{WorkspaceID: workspaceID}).
		Where("members.is_ghost IS NOT true  ")

	requester := model.Member{
		WorkspaceID: workspaceID,
		ID:          requesterID,
	}
	if err := db.Where(&requester).Preload("Tags", db.DefaultPreloadOrderTag()).First(&requester).Error; err != nil {
		s.c.GetLogger().Warn("error getting requester")
		return nil, err
	}
	requesterTags := []uuid.UUID{}
	for _, rTag := range requester.Tags {
		requesterTags = append(requesterTags, rTag.ID)
	}
	if len(requesterTags) > 0 {
		tx = tx.Where("member_tags.tag_id IN (?)", requesterTags)
	} else {
		tx = tx.Where("members.id = ?", requesterID)
	}

	reqTagIDs, err := s.tagRepo.ToUUIDs(workspaceID, &q.TagIDs)
	if err != nil {
		return nil, errors.Wrap(err, "error getting tag ids")
	}
	tagGroupMap := map[uuid.UUID][]uuid.UUID{}
	if len(q.TagIDs) != 0 {
		tags, err := s.tagRepo.FindByStatusPublishedAndWorkspaceIDAndIDIn(workspaceID, reqTagIDs)
		if err != nil {
			return nil, errors.Wrap(err, "error getting Tags: "+err.Error())
		}
		for _, tag := range tags {
			tagGroupMap[tag.TagGroupID] = append(tagGroupMap[tag.TagGroupID], tag.ID)
		}
	}

	idx := 0
	for _, tagIDs := range tagGroupMap {
		memberTags := fmt.Sprintf("member_tags%d", idx)
		tx = tx.Joins(fmt.Sprintf("INNER JOIN member_tags as %s ON %s.member_id = members.id", memberTags, memberTags))
		tx = tx.Where(fmt.Sprintf("%s.tag_id IN (?)", memberTags), tagIDs)
		idx++
	}

	err = s.addRoleControlFilter(ctx, tx, workspaceID, requesterID, q.RoleTags)
	if err != nil {
		return nil, err
	}

	if q.Query != "" {
		q.Query = strings.ReplaceAll(q.Query, "%", "\\%")
		q.Query = "%" + q.Query + "%"
		tx = tx.Where("COALESCE(members.name, '') || ' ' || COALESCE(members.background_text, '') ILIKE ?", q.Query)
	}
	statusTags := q.StatusTags
	if requesterRole != memberrole.MANAGER {
		statusTags = []string{memberstatus.ACTIVE}
	}
	if len(statusTags) != 0 {
		tx = tx.Where("members.status IN (?)", statusTags)
	}
	if len(q.FilteringTextReq) > 0 {
		for idx, filteringText := range q.FilteringTextReq {
			memberFiName := "member_fi_text_" + strconv.Itoa(idx)
			fiName := "fi_text_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringText.FormItemID, tx, nil)
			queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NUll " + " THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) ILIKE ?"
			tx = tx.Where(queryString, "%"+filteringText.Value+"%")
		}
	}

	if len(q.FilteringTextAreaReq) > 0 {
		for idx, filteringText := range q.FilteringTextAreaReq {
			memberFiName := "member_fi_text_area_" + strconv.Itoa(idx)
			fiName := "fi_text_area_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringText.FormItemID, tx, nil)
			queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NUll " + " THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) ILIKE ?"
			tx = tx.Where(queryString, "%"+filteringText.Value+"%")
		}
	}

	if len(q.FilteringCheckboxReq) > 0 {
		for idx, filteringCheckbox := range q.FilteringCheckboxReq {
			memberFiName := "member_fi_checkbox_" + strconv.Itoa(idx)
			fiName := "fi_checkbox_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringCheckbox.FormItemID, tx, nil)
			queryString := ""
			for idx, id := range filteringCheckbox.Value {
				if idx != 0 {
					queryString += " OR "
				}
				queryString += "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->'value' ELSE " + memberFiName + ".data_obj->'value' END) ? '" + id.String() + "' "
			}
			tx = tx.Where(queryString)
		}
	}

	if len(q.FilteringDropdownReq) > 0 {
		for idx, filteringDropdown := range q.FilteringDropdownReq {
			ids := []uuid.UUID{}
			ids = append(ids, filteringDropdown.Value...)
			if len(ids) > 0 {
				memberFiName := "member_fi_dropdown_" + strconv.Itoa(idx)
				fiName := "fi_dropdown_" + strconv.Itoa(idx)
				s.createJoinMemberFIAndFI(memberFiName, fiName, filteringDropdown.FormItemID, tx, nil)
				queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) IN ? "
				tx = tx.Where(queryString, ids)
			}
		}
	}

	if len(q.FilteringRadioButtonReq) > 0 {
		for idx, filteringRadio := range q.FilteringRadioButtonReq {
			ids := []uuid.UUID{}
			ids = append(ids, filteringRadio.Value...)
			if len(ids) > 0 {
				memberFiName := "member_fi_radio_" + strconv.Itoa(idx)
				fiName := "fi_radio_" + strconv.Itoa(idx)
				s.createJoinMemberFIAndFI(memberFiName, fiName, filteringRadio.FormItemID, tx, nil)
				queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END)  IN ? "
				tx = tx.Where(queryString, ids)
			}
		}
	}

	if len(q.FilteringDateReq) > 0 { // nolint
		for idx, filteringDate := range q.FilteringDateReq {
			memberFiName := "member_fi_date_" + strconv.Itoa(idx)
			fiName := "fi_date_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringDate.FormItemID, tx, nil)
			queryString := ""
			switch {
			case filteringDate.From != "" && filteringDate.To == "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  >= ?  AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.From)
			case filteringDate.From == "" && filteringDate.To != "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  <= ?   AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.To)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  >= ?   AT TIME ZONE 'UTC' AND " + "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  <= ?   AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.From, filteringDate.To)
			}
		}
	}

	if len(q.FilteringTimeReq) > 0 {
		for idx, filteringTime := range q.FilteringTimeReq {
			memberFiName := "member_fi_time_" + strconv.Itoa(idx)
			fiName := "fi_time_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringTime.FormItemID, tx, nil)
			queryString := ""
			switch {
			case filteringTime.From != "" && filteringTime.To == "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  >= ?::time   "
				tx = tx.Where(queryString, filteringTime.From)
			case filteringTime.From == "" && filteringTime.To != "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  <= ?::time   "
				tx = tx.Where(queryString, filteringTime.To)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  BETWEEN ?::time AND ?::time   "
				tx = tx.Where(queryString, filteringTime.From, filteringTime.To)
			}
		}
	}

	if len(q.FilteringNumberReq) > 0 {
		for idx, filteringNumber := range q.FilteringNumberReq {
			memberFiName := "member_fi_number_" + strconv.Itoa(idx)
			fiName := "fi_number_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringNumber.FormItemID, tx, nil)
			queryString := ""
			switch {
			case filteringNumber.Min.Valid && !filteringNumber.Max.Valid:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  >= ? "
				tx = tx.Where(queryString, filteringNumber.Min.Float64)
			case !filteringNumber.Min.Valid && filteringNumber.Max.Valid:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  <= ? "
				tx = tx.Where(queryString, filteringNumber.Max.Float64)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  >= ? AND" + "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  <= ? "
				tx = tx.Where(queryString, filteringNumber.Min.Float64, filteringNumber.Max.Float64)
			}
		}
	}

	if q.JoinDate != nil {
		tx = tx.Joins("LEFT JOIN chats ON chats.actor_id = members.id and chats.message IN (?)", []string{logaction.MEMBER_CREATE_WORKSPACE, logaction.MEMBER_JOIN})
		if q.JoinDate.FromDate.Valid {
			tx = tx.Where("chats.created_at >= ?", q.JoinDate.FromDate.Time)
		}
		if q.JoinDate.ToDate.Valid {
			tx = tx.Where("chats.created_at <= ?", q.JoinDate.ToDate.Time.AddDate(0, 0, 1))
		}
	}

	members := []*model.Member{}
	result := tx.
		Select("members.id, members.icon, members.name").
		Where("role != ?", memberrole.GUEST).
		Group("members.id").
		Find(&members)
	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "error getting Members")
	}

	team := dto.GetTeamResponse{}
	membersTeamHaveRelation := []*dto.MemberTeam{}
	membersTeamNoRelation := []*dto.MemberTeam{}
	for _, member := range members {
		member.WorkspaceID = workspaceID
		err = s.FillIconSignedURL(ctx, member)
		if err != nil {
			return nil, err
		}

		memberTeam := dto.MemberTeam{
			MemberID:   member.ID,
			IconURL:    member.Icon.String,
			MemberName: member.Name.String,
		}

		recordAssignees := []*model.RecordAssignee{}
		err = db.Model(&model.RecordAssignee{}).
			Joins("INNER JOIN records ON records.id = record_assignees.record_id").
			Where("records.creator_id = ? AND records.deleted_at = 0", member.ID).
			Preload("Assignee").
			Find(&recordAssignees).Error
		if err != nil {
			return nil, err
		}

		memberTeam.HasCreateTask = len(recordAssignees) > 0

		memberRelations := []*dto.MemberRelation{}
		idsExists := []uuid.UUID{}
		var requestTags = []string{}
		if len(q.RoleTags) == 0 {
			requestTags = append(requestTags, memberrole.MANAGER, memberrole.STAFF, memberrole.WORKER, memberrole.GUEST)
		}
		for _, recordAssignee := range recordAssignees {
			if recordAssignee.AssigneeID != member.ID &&
				!slices.Contains[uuid.UUID](idsExists, recordAssignee.AssigneeID) &&
				recordAssignee.Assignee != nil && recordAssignee.Assignee.Role != memberrole.GUEST &&
				slices.Contains[string](requestTags, recordAssignee.Assignee.Role) {
				idsExists = append(idsExists, recordAssignee.AssigneeID)
				memberRelation := dto.MemberRelation{
					MemberID:   recordAssignee.AssigneeID,
					MemberName: recordAssignee.Assignee.Name.String,
				}
				err := s.FillIconSignedURL(ctx, recordAssignee.Assignee)
				if err != nil {
					return nil, err
				}
				memberRelation.IconURL = recordAssignee.Assignee.Icon.String

				memberRelations = append(memberRelations, &memberRelation)
			}
		}
		memberTeam.MemberRelations = memberRelations
		if len(memberTeam.MemberRelations) > 0 {
			membersTeamHaveRelation = append(membersTeamHaveRelation, &memberTeam)
		} else {
			membersTeamNoRelation = append(membersTeamNoRelation, &memberTeam)
		}
	}
	team.Members = append(team.Members, membersTeamHaveRelation...)
	for _, memberTeamNoRelation := range membersTeamNoRelation {
		isExists := false
		for _, memberTeamHaveRelation := range membersTeamHaveRelation {
			for _, memberRelation := range memberTeamHaveRelation.MemberRelations {
				if memberRelation.MemberID == memberTeamNoRelation.MemberID {
					isExists = true
					break
				}
			}
			if isExists {
				break
			}
		}
		if !isExists {
			team.Members = append(team.Members, memberTeamNoRelation)
		}
	}

	return &team, nil
}

func (s *memberService) FindByWorkspaceIDAndIDAndAdditionData(ctx context.Context, workspaceID uuid.UUID, requesterID, memberID uuid.UUID, q dto.GetMemberRequest) (*dto.GetMemberResponse, error) {
	result := &dto.GetMemberResponse{}
	db := s.c.GetRepository(ctx)

	member, err := s.GetMemberResponse(ctx, requesterID, memberID, false, true)
	if err != nil {
		s.c.GetLogger().WithContext(ctx).Warn("error getting Member")
		return nil, err
	}
	result.Member = &member

	var mainTaskCount int64
	var subTaskCount int64

	err = db.Model(&model.Record{}).
		Joins("INNER JOIN record_assignees ON records.id = record_assignees.record_id AND record_assignees.deleted_at = 0 AND record_assignees.assignee_id = ?", memberID).
		Where("records.creator_id = ?", requesterID).
		Count(&mainTaskCount).Error
	if err != nil {
		return nil, err
	}

	err = db.Model(&model.Record{}).
		Joins("INNER JOIN record_assignees ON records.id = record_assignees.record_id AND record_assignees.deleted_at = 0 AND record_assignees.assignee_id = ?", requesterID).
		Where("records.creator_id = ?", memberID).
		Count(&subTaskCount).Error
	if err != nil {
		return nil, err
	}

	result.MainPICTaskCount = mainTaskCount
	result.SubPICTaskCount = subTaskCount

	mainMember, err := s.GetMemberResponse(ctx, requesterID, q.MainMemberID.UUID, false, true)
	if err != nil {
		s.c.GetLogger().WithContext(ctx).Warn("error getting Main Member")
		return nil, err
	}
	result.MainMember = &mainMember

	subMember, err := s.GetMemberResponse(ctx, requesterID, q.SubMemberID.UUID, false, true)
	if err != nil {
		s.c.GetLogger().WithContext(ctx).Warn("error getting Sub Member")
		return nil, err
	}
	result.SubMember = &subMember

	return result, nil
}

// MemberID not null when list contain requester, use it to find private chat
// targetIDs: targetIDs will not include memberID
func (s *memberService) GetMemberChatInfoByTargetIDs(ctx context.Context, workspaceID, requesterID uuid.UUID, targetIDs []uuid.UUID) (map[string]dto.ChatInfo, error) {
	db := s.c.GetRepository(ctx)
	var groupIDs []string
	for _, targetID := range targetIDs {
		memberRole := ""
		err := db.Model(&model.Member{}).Select("role").Where("id = ?", targetID).Scan(&memberRole).Error
		if err != nil {
			return nil, err
		}
		chatRoomId := memberCommon.GenerateChatRoomID(requesterID.String(), targetID.String(), memberRole, false)
		groupIDs = append(groupIDs, chatRoomId)
	}
	chatInfo, err := s.chatInfoService.GetMemberChatInfoByGroupIDs(ctx, workspaceID, groupIDs, targetIDs, "members", requesterID)
	return chatInfo, err
}

func (s *memberService) GetTargetIDsHasNewMessage(ctx context.Context, requesterID uuid.UUID, targetIDs []uuid.UUID) ([]string, error) {
	db := s.c.GetRepository(ctx)
	idsUnread := []string{}

	targetIDsString := []string{}
	for _, targetID := range targetIDs {
		var memberRole string
		err := db.Model(&model.Member{}).Select("role").Where("id = ?", targetID).Scan(&memberRole).Error
		if err != nil {
			return nil, err
		}
		chatRoomId := memberCommon.GenerateChatRoomID(requesterID.String(), targetID.String(), memberRole, false)
		targetIDsString = append(targetIDsString, chatRoomId)
	}

	err := db.
		Model(model.ChatInfo{}).
		Select("chat_infos.group_id").
		Joins("LEFT JOIN member_reads ON member_reads.group_id = chat_infos.group_id AND member_reads.member_id = ? ", requesterID).
		Where("chat_infos.group_id IN (?)", targetIDsString).
		Where("(chat_infos.last_mess_time >= COALESCE(member_reads.last_read_time, chat_infos.last_mess_time)) IS TRUE").
		Find(&idsUnread).Error

	if err != nil {
		return nil, err
	}

	return idsUnread, nil
}

func (s *memberService) GetMemberResponse(ctx context.Context, requesterID, memberID uuid.UUID, displayOnCard bool, allowGhost bool) (dto.Member, error) {
	var memberResponse dto.Member
	db := s.c.GetRepository(ctx)

	member := &model.Member{
		ID: memberID,
	}
	tx := db.
		Preload("OptionalFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("MemberFormItems")
	if !allowGhost {
		tx = tx.Where("is_ghost IS NOT true")
	}
	if displayOnCard {
		tx = tx.Preload("Tags", db.PreloadTagsForCard()).
			Preload("FormItems", func(db *gorm.DB) *gorm.DB {
				return db.Where("form_items.display_on_card = ?", true)
			}, db.PreloadOrderByPosition(&model.FormItem{}))
	} else {
		tx = tx.Preload("Tags", db.DefaultPreloadOrderTag()).
			Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{}))
	}
	err := tx.First(&member).Error
	if err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}

	if err := s.MapMemberFormItemsWithWorkspace(ctx, member); err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}
	if err := s.FillIconSignedURL(ctx, member); err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}

	if err := s.FillBackgroundSignedURL(ctx, member); err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}

	memberResponse.ID = member.ID
	memberResponse.CreatedAt = member.CreatedAt
	memberResponse.UpdatedAt = member.UpdatedAt
	memberResponse.WorkspaceID = member.WorkspaceID
	memberResponse.Icon = member.Icon
	memberResponse.IconKey = member.IconKey
	memberResponse.Background = member.Background
	memberResponse.BackgroundKey = member.BackgroundKey
	memberResponse.BackgroundText = member.BackgroundText
	memberResponse.Name = member.Name
	memberResponse.Email = member.Email
	memberResponse.Role = member.Role
	memberResponse.Tags = member.Tags
	memberResponse.Status = member.Status
	memberResponse.FormItems = member.FormItems
	memberResponse.OptionalFormItems = member.OptionalFormItems
	memberResponse.MemberFormItems = member.MemberFormItems
	memberResponse.IsGhost = member.IsGhost
	memberResponse.InviterID = member.InviterID
	memberResponse.InvitedAt = member.InvitedAt
	memberResponse.TextColor = member.TextColor

	memberResponse.Opacity = member.Opacity
	memberResponse.TextColor = member.TextColor
	memberResponse.ReverseBackground = member.ReverseBackground
	memberResponse.PrimaryColor = member.PrimaryColor
	memberResponse.SecondaryColor = member.SecondaryColor
	memberResponse.PrimaryColor = member.PrimaryColor
	memberResponse.Illustration = member.Illustration
	memberResponse.TextAlignment = member.TextAlignment
	memberResponse.BackgroundCommon = member.BackgroundCommon
	memberResponse.TagNumber = member.TagNumber

	if memberResponse.InviterID != nil {
		inviter := &model.Member{
			ID: *member.InviterID,
		}
		if err := db.Select("name").First(inviter).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				memberResponse.InviterName = null.StringFrom("")
			} else {
				return memberResponse, errors.Wrap(err, "error getting inviter")
			}
		} else {
			memberResponse.InviterName = inviter.Name
		}
	}

	if memberResponse.Status == memberstatus.INVITING {
		workspaceInvite := &model.WorkspaceInvite{
			WorkspaceID: member.WorkspaceID,
			MemberID:    member.ID,
		}
		if err := db.Where(workspaceInvite).First(workspaceInvite).Error; err != nil {
			return memberResponse, errors.Wrap(err, "error getting workspace invite")
		}
		memberResponse.InvitedCode = null.StringFrom(workspaceInvite.InvitedCode)
	}

	type dataObject struct {
		Data datatypes.JSONMap
		Idx  int
	}
	formItemTypeMemberIdList := map[uuid.UUID]*dataObject{}
	for idx := range memberResponse.MemberFormItems {
		formItemTypeMemberIdList[memberResponse.MemberFormItems[idx].FormItemID] = &dataObject{Data: memberResponse.MemberFormItems[idx].DataObj, Idx: idx}
	}

	for idx := range memberResponse.FormItems { //nolint
		if memberResponse.FormItems[idx].Type == formitemtype.MEMBER && memberResponse.FormItems[idx].DataObj != nil {
			dataOb := memberResponse.FormItems[idx].DataObj
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				dataOb = formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Data
			}
			idString, ok := dataOb["value"].(string)
			data := datatypes.JSONMap{}
			if ok {
				data, err = s.formitemService.FindFormItemMember(ctx, requesterID, idString)
				if err == nil {
					memberResponse.FormItems[idx].DataObj = data
				}
			}
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				memberResponse.MemberFormItems[formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Idx].DataObj = data
			} else {
				memberResponse.FormItems[idx].DataObj = data
			}
		} else if memberResponse.FormItems[idx].Type == formitemtype.DATABASE && memberResponse.FormItems[idx].DataObj != nil {
			dataOb := memberResponse.FormItems[idx].DataObj
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				dataOb = formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Data
			}
			idString, ok := dataOb["value"].(string)
			data := datatypes.JSONMap{}
			if ok {
				data, err = s.formitemService.FindFormItemDatabase(ctx, requesterID, idString)
				if err == nil {
					memberResponse.FormItems[idx].DataObj = data
				}
			}
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				memberResponse.MemberFormItems[formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Idx].DataObj = data
			} else {
				memberResponse.FormItems[idx].DataObj = data
			}
		}
	}

	for idx := range memberResponse.OptionalFormItems {
		if memberResponse.OptionalFormItems[idx].Type == formitemtype.MEMBER && memberResponse.OptionalFormItems[idx].DataObj != nil {
			idString, ok := memberResponse.OptionalFormItems[idx].DataObj["value"].(string)
			if ok {
				data, err := s.formitemService.FindFormItemMember(ctx, requesterID, idString)
				if err == nil {
					memberResponse.OptionalFormItems[idx].DataObj = data
				}
			}
		} else if memberResponse.OptionalFormItems[idx].Type == formitemtype.DATABASE && memberResponse.OptionalFormItems[idx].DataObj != nil {
			idString, ok := memberResponse.OptionalFormItems[idx].DataObj["value"].(string)
			if ok {
				data, err := s.formitemService.FindFormItemDatabase(ctx, requesterID, idString)
				if err == nil {
					memberResponse.OptionalFormItems[idx].DataObj = data
				}
			}
		}
	}
	memberBookmarked := model.MemberBookmarkedItem{}
	result := db.Where("target_id = ? AND member_id = ?", memberID, requesterID.String()).Select("id").Find(&memberBookmarked)
	if result.Error != nil {
		return memberResponse, result.Error
	}
	if result.RowsAffected > 0 {
		memberResponse.MemberBookmarkedItemID = uuid.NullUUID{UUID: memberBookmarked.ID, Valid: true}
	} else {
		memberResponse.MemberBookmarkedItemID = uuid.NullUUID{Valid: false}
	}
	if memberResponse.Role == memberrole.GUEST {
		facilitatorResponse, err := s.facilitatorService.FindByTargetID(ctx, memberResponse.ID)
		if err != nil {
			return dto.Member{}, err
		}
		memberResponse.Facilitator = facilitatorResponse

		shareScopeResponse, err := s.shareScopeService.FindByTargetID(ctx, memberResponse.ID)
		if err != nil {
			return dto.Member{}, err
		}
		memberResponse.ShareScope = shareScopeResponse
	}

	// Populate ChatInfo for the member
	chatInfos, err := s.GetMemberChatInfoByTargetIDs(ctx, member.WorkspaceID, requesterID, []uuid.UUID{memberID})
	if err != nil {
		return memberResponse, errors.Wrap(err, "error getting chat info")
	}
	if chatInfo, exists := chatInfos[memberID.String()]; exists && chatInfo.ChatInfo != nil {
		memberResponse.ChatInfo = &chatInfo
	}

	return memberResponse, nil
}

// GetMembersResponse fetches multiple member responses in a single batch operation
// This method optimizes database queries by reducing the number of round trips
func (s *memberService) GetMembersResponse(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, memberIDs []uuid.UUID, displayOnCard bool, allowGhost bool) ([]dto.Member, error) {
	if len(memberIDs) == 0 {
		return []dto.Member{}, nil
	}

	db := s.c.GetRepository(ctx)

	// Batch fetch all members with their relationships
	members := []model.Member{}
	tx := db.Where("id IN ?", memberIDs).
		Preload("OptionalFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("MemberFormItems")

	if !allowGhost {
		tx = tx.Where("is_ghost IS NOT true")
	}

	if displayOnCard {
		tx = tx.Preload("Tags", db.PreloadTagsForCard()).
			Preload("FormItems", func(db *gorm.DB) *gorm.DB {
				return db.Where("form_items.display_on_card = ?", true)
			}, db.PreloadOrderByPosition(&model.FormItem{}))
	} else {
		tx = tx.Preload("Tags", db.DefaultPreloadOrderTag()).
			Preload("FormItems", db.PreloadOrderByPosition(&model.FormItem{}))
	}

	err := tx.Find(&members).Error
	if err != nil {
		return nil, errors.Wrap(err, "error getting members")
	}

	// Create a map for quick lookup by member ID
	memberMap := make(map[uuid.UUID]*model.Member)
	for i := range members {
		memberMap[members[i].ID] = &members[i]
	}

	// Batch process member form items and signed URLs
	for i := range members {
		if err := s.MapMemberFormItemsWithWorkspace(ctx, &members[i]); err != nil {
			return nil, errors.Wrap(err, "error mapping member form items")
		}
		if err := s.FillIconSignedURL(ctx, &members[i]); err != nil {
			return nil, errors.Wrap(err, "error filling icon signed URL")
		}
		if err := s.FillBackgroundSignedURL(ctx, &members[i]); err != nil {
			return nil, errors.Wrap(err, "error filling background signed URL")
		}
	}

	// Batch fetch inviter names for members that have inviter IDs
	inviterIDs := []uuid.UUID{}
	inviterMap := make(map[uuid.UUID]string)
	for _, member := range members {
		if member.InviterID != nil {
			inviterIDs = append(inviterIDs, *member.InviterID)
		}
	}

	if len(inviterIDs) > 0 {
		var inviters []model.Member
		err = db.Where("id IN ?", inviterIDs).Select("id, name").Find(&inviters).Error
		if err != nil {
			return nil, errors.Wrap(err, "error getting inviters")
		}

		for _, inviter := range inviters {
			inviterMap[inviter.ID] = inviter.Name.String
		}
	}

	// Batch fetch workspace invites for inviting members
	invitingMemberIDs := []uuid.UUID{}
	workspaceInviteMap := make(map[uuid.UUID]string)
	for _, member := range members {
		if member.Status == memberstatus.INVITING {
			invitingMemberIDs = append(invitingMemberIDs, member.ID)
		}
	}

	if len(invitingMemberIDs) > 0 {
		var workspaceInvites []model.WorkspaceInvite
		err = db.Where("member_id IN ? AND workspace_id = ?", invitingMemberIDs, workspaceID).Find(&workspaceInvites).Error
		if err != nil {
			return nil, errors.Wrap(err, "error getting workspace invites")
		}

		for _, invite := range workspaceInvites {
			workspaceInviteMap[invite.MemberID] = invite.InvitedCode
		}
	}

	// Batch fetch member bookmarked items
	var memberBookmarkedItems []model.MemberBookmarkedItem
	err = db.Where("target_id IN ? AND member_id = ?", memberIDs, requesterID.String()).
		Select("id, target_id").Find(&memberBookmarkedItems).Error
	if err != nil {
		return nil, errors.Wrap(err, "error getting member bookmarked items")
	}

	bookmarkedMap := make(map[uuid.UUID]uuid.UUID)
	for _, bookmark := range memberBookmarkedItems {
		bookmarkedMap[bookmark.TargetID] = bookmark.ID
	}

	// Build responses maintaining the order of input IDs
	responses := make([]dto.Member, 0, len(memberIDs))
	for _, memberID := range memberIDs {
		member, exists := memberMap[memberID]
		if !exists {
			// Skip members that weren't found
			continue
		}

		memberResponse := dto.Member{}

		// Map basic member data
		memberResponse.ID = member.ID
		memberResponse.CreatedAt = member.CreatedAt
		memberResponse.UpdatedAt = member.UpdatedAt
		memberResponse.WorkspaceID = member.WorkspaceID
		memberResponse.Icon = member.Icon
		memberResponse.IconKey = member.IconKey
		memberResponse.Background = member.Background
		memberResponse.BackgroundKey = member.BackgroundKey
		memberResponse.BackgroundText = member.BackgroundText
		memberResponse.Name = member.Name
		memberResponse.Email = member.Email
		memberResponse.Role = member.Role
		memberResponse.Tags = member.Tags
		memberResponse.Status = member.Status
		memberResponse.FormItems = member.FormItems
		memberResponse.OptionalFormItems = member.OptionalFormItems
		memberResponse.MemberFormItems = member.MemberFormItems
		memberResponse.IsGhost = member.IsGhost
		memberResponse.InviterID = member.InviterID
		memberResponse.InvitedAt = member.InvitedAt
		memberResponse.TextColor = member.TextColor
		memberResponse.Opacity = member.Opacity
		memberResponse.ReverseBackground = member.ReverseBackground
		memberResponse.PrimaryColor = member.PrimaryColor
		memberResponse.SecondaryColor = member.SecondaryColor
		memberResponse.Illustration = member.Illustration
		memberResponse.TextAlignment = member.TextAlignment
		memberResponse.BackgroundCommon = member.BackgroundCommon
		memberResponse.TagNumber = member.TagNumber

		// Set inviter name if available
		if member.InviterID != nil {
			if inviterName, exists := inviterMap[*member.InviterID]; exists {
				memberResponse.InviterName = null.StringFrom(inviterName)
			} else {
				memberResponse.InviterName = null.StringFrom("")
			}
		}

		// Set invited code if member is inviting
		if member.Status == memberstatus.INVITING {
			if invitedCode, exists := workspaceInviteMap[member.ID]; exists {
				memberResponse.InvitedCode = null.StringFrom(invitedCode)
			}
		}

		// Set bookmarked item ID if exists
		if bookmarkID, exists := bookmarkedMap[member.ID]; exists {
			memberResponse.MemberBookmarkedItemID = uuid.NullUUID{UUID: bookmarkID, Valid: true}
		} else {
			memberResponse.MemberBookmarkedItemID = uuid.NullUUID{Valid: false}
		}

		responses = append(responses, memberResponse)
	}

	return responses, nil
}

func (s *memberService) GetLiteDataForMember(ctx context.Context, requesterID, memberID uuid.UUID) (dto.Member, error) {
	var memberResponse dto.Member
	db := s.c.GetRepository(ctx)

	member := &model.Member{
		ID: memberID,
	}
	tx := db.
		Preload("OptionalFormItems", db.PreloadOrderByPosition(&model.FormItem{})).
		Preload("MemberFormItems")
	tx = tx.Preload("FormItems", func(db *gorm.DB) *gorm.DB {
		return db.Where("form_items.display_on_card = ?", true)
	}, db.PreloadOrderByPosition(&model.FormItem{}))
	err := tx.First(&member).Error
	if err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}

	if err := s.MapMemberFormItemsWithWorkspace(ctx, member); err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}
	if err := s.FillIconSignedURL(ctx, member); err != nil {
		return memberResponse, errors.Wrap(err, "error getting member")
	}

	memberResponse.ID = member.ID
	memberResponse.WorkspaceID = member.WorkspaceID
	memberResponse.Icon = member.Icon
	memberResponse.IconKey = member.IconKey
	memberResponse.Name = member.Name
	memberResponse.Role = member.Role
	memberResponse.Status = member.Status
	memberResponse.FormItems = member.FormItems
	memberResponse.OptionalFormItems = member.OptionalFormItems
	memberResponse.MemberFormItems = member.MemberFormItems

	type dataObject struct {
		Data datatypes.JSONMap
		Idx  int
	}
	formItemTypeMemberIdList := map[uuid.UUID]*dataObject{}
	for idx := range memberResponse.MemberFormItems {
		formItemTypeMemberIdList[memberResponse.MemberFormItems[idx].FormItemID] = &dataObject{Data: memberResponse.MemberFormItems[idx].DataObj, Idx: idx}
	}

	for idx := range memberResponse.FormItems { // nolint
		if memberResponse.FormItems[idx].Type == formitemtype.MEMBER && memberResponse.FormItems[idx].DataObj != nil {
			dataOb := memberResponse.FormItems[idx].DataObj
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				dataOb = formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Data
			}
			idString, ok := dataOb["value"].(string)
			data := datatypes.JSONMap{}
			if ok {
				data, err = s.formitemService.FindFormItemMember(ctx, memberID, idString)
				if err == nil {
					memberResponse.FormItems[idx].DataObj = data
				}
			}
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				memberResponse.MemberFormItems[formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Idx].DataObj = data
			} else {
				memberResponse.FormItems[idx].DataObj = data
			}
		} else if memberResponse.FormItems[idx].Type == formitemtype.DATABASE && memberResponse.FormItems[idx].DataObj != nil {
			dataOb := memberResponse.FormItems[idx].DataObj
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				dataOb = formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Data
			}
			idString, ok := dataOb["value"].(string)
			data := datatypes.JSONMap{}
			if ok {
				data, err = s.formitemService.FindFormItemDatabase(ctx, memberID, idString)
				if err == nil {
					memberResponse.FormItems[idx].DataObj = data
				}
			}
			if formItemTypeMemberIdList[memberResponse.FormItems[idx].ID] != nil {
				memberResponse.MemberFormItems[formItemTypeMemberIdList[memberResponse.FormItems[idx].ID].Idx].DataObj = data
			} else {
				memberResponse.FormItems[idx].DataObj = data
			}
		}
	}
	for idx := range memberResponse.OptionalFormItems {
		if memberResponse.OptionalFormItems[idx].Type == formitemtype.MEMBER && memberResponse.OptionalFormItems[idx].DataObj != nil {
			idString, ok := memberResponse.OptionalFormItems[idx].DataObj["value"].(string)
			if ok {
				data, err := s.formitemService.FindFormItemMember(ctx, memberID, idString)
				if err == nil {
					memberResponse.OptionalFormItems[idx].DataObj = data
				}
			}
		} else if memberResponse.OptionalFormItems[idx].Type == formitemtype.DATABASE && memberResponse.OptionalFormItems[idx].DataObj != nil {
			idString, ok := memberResponse.OptionalFormItems[idx].DataObj["value"].(string)
			if ok {
				data, err := s.formitemService.FindFormItemDatabase(ctx, memberID, idString)
				if err == nil {
					memberResponse.OptionalFormItems[idx].DataObj = data
				}
			}
		}
	}

	memberBookmarked := model.MemberBookmarkedItem{}
	result := db.Where("target_id = ? AND member_id = ?", memberID, requesterID.String()).Select("id").Find(&memberBookmarked)
	if result.Error != nil {
		return memberResponse, result.Error
	}
	if result.RowsAffected > 0 {
		memberResponse.MemberBookmarkedItemID = uuid.NullUUID{UUID: memberBookmarked.ID, Valid: true}
	} else {
		memberResponse.MemberBookmarkedItemID = uuid.NullUUID{Valid: false}
	}

	return memberResponse, nil
}

func (s *memberService) GetMemberResponseForTaskDetailScreen(ctx context.Context, requesterID, memberID uuid.UUID) (dto.Member, error) {
	memberResponse, err := s.GetLiteDataForMember(ctx, requesterID, memberID)
	if err != nil {
		return memberResponse, err
	}
	return memberResponse, nil
}

func (s *memberService) FindByWorkspaceIDAndRequesterIDandRequesterRoleAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersQueryParams) ([]*dto.Member, bool, error) {
	db := s.c.GetRepository(ctx)

	reqTagIDs, err := s.tagRepo.ToUUIDs(workspaceID, &q.TagIDs)
	if err != nil {
		return nil, false, errors.Wrap(err, "error getting tag ids")
	}

	var members []*model.Member
	var dtoMembers []*dto.Member

	tagGroupMap := map[uuid.UUID][]uuid.UUID{}
	if len(q.TagIDs) != 0 {
		tags, err := s.tagRepo.FindByStatusPublishedAndWorkspaceIDAndIDIn(workspaceID, reqTagIDs)
		if err != nil {
			return nil, false, errors.Wrap(err, "error getting Tags: "+err.Error())
		}
		for _, tag := range tags {
			tagGroupMap[tag.TagGroupID] = append(tagGroupMap[tag.TagGroupID], tag.ID)
		}
	}
	order := "ASC"
	if q.Sort == "-" {
		order = "DESC"
	}

	tx := db.Model(&model.Member{})
	idx := 0
	for _, tagIDs := range tagGroupMap {
		memberTags := fmt.Sprintf("m_ts%d", idx)
		tx = tx.Joins("JOIN member_tags as " + memberTags + " ON " + memberTags + ".member_id = members.id")
		tx = tx.Where(memberTags+".tag_id IN (?)", tagIDs)
		idx++
	}
	if q.Query != "" {
		q.Query = strings.ReplaceAll(q.Query, "%", "\\%")
		q.Query = "%" + q.Query + "%"
		tx = tx.Where("members.name ILIKE ?", q.Query)
	}

	err = s.addRoleControlFilter(ctx, tx, workspaceID, requesterID, q.RoleTags)
	if err != nil {
		return nil, false, err
	}

	if len(q.StatusTags) != 0 {
		tx = tx.Where("members.status IN (?)", q.StatusTags)
	}

	if len(reqTagIDs) != 0 {
		tx = tx.Joins("JOIN member_tags ON member_tags.member_id = members.id")
	}

	if q.IgnoreCurrentUser {
		tx = tx.Where("members.id != ?", requesterID)
	}

	tx = tx.Where(&model.Member{WorkspaceID: workspaceID})
	if q.Page != 0 {
		tx = tx.Offset((q.Page - 1) * q.PageSize).Limit(q.PageSize)
	}

	result := tx.
		Where("members.is_ghost IS NOT true").
		Group("members.id").
		Order("members.name " + order).
		Select("members.id").
		Find(&members)
	if result.Error != nil {
		return nil, false, errors.Wrap(result.Error, "error getting Members")
	}

	hasMore := false
	if result.RowsAffected > int64(q.PageSize) {
		hasMore = true
	}

	workspace := &model.Workspace{ID: workspaceID}
	if err := db.Where(workspace).First(workspace).Error; err != nil {
		return nil, false, errors.Wrap(err, "error getting Workspace")
	}

	memberIDs := []uuid.UUID{}
	for _, member := range members {
		member := member
		memberRes, err := s.GetMemberResponse(ctx, requesterID, member.ID, true, true)
		if err != nil {
			return nil, false, err
		}
		dtoMembers = append(dtoMembers, &memberRes)
		if requesterID != member.ID {
			memberIDs = append(memberIDs, member.ID)
		}
	}

	idsHasUnread, err := s.GetTargetIDsHasNewMessage(ctx, requesterID, memberIDs)
	if err != nil {
		return nil, false, err
	}

	chatInfos, err := s.GetMemberChatInfoByTargetIDs(ctx, workspaceID, requesterID, memberIDs)
	if err != nil {
		return nil, false, err
	}

	for idx, dtoMember := range dtoMembers {
		chatRoomId := memberCommon.GenerateChatRoomID(dtoMember.ID.String(), requesterID.String(), dtoMember.Role, false)
		if sliceutil.Contain(idsHasUnread, chatRoomId) {
			dtoMembers[idx].HasNewMessage = null.BoolFrom(true)
		}

		chatInfo := chatInfos[chatRoomId]
		if chatInfo.ChatInfo != nil {
			dtoMembers[idx].ChatInfo = &chatInfo
		}
	}

	return dtoMembers, hasMore, nil
}

func (s *memberService) findByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, q dto.GetMembersByQueryParamsAndFacilitatorAndShareScope) ([]uuid.UUID, error) {
	db := s.c.GetRepository(ctx)

	tx := db.Model(&model.Member{}).Where(&model.Member{WorkspaceID: workspaceID}).Where("members.is_ghost IS NOT true").
		Joins("LEFT JOIN member_bookmarked_items ON member_bookmarked_items.target_id = members.id AND member_bookmarked_items.deleted_at = 0")

	order := "ASC"
	if q.Sort == "-" {
		order = "DESC"
	}
	tx = tx.Order("members.name " + order)

	if q.Page != 0 {
		tx = tx.Offset((q.Page - 1) * q.PageSize).Limit(q.PageSize)
	}

	var guestDatabaseID *uuid.UUID
	if q.ForGuestPage.Bool {
		guestPage := &model.Database{
			WorkspaceID: workspaceID,
			Type:        null.StringFrom("GUEST_DATABASE"),
		}
		if err := db.Model(&model.Database{}).Where(guestPage).First(guestPage).Error; err != nil {
			return nil, errors.Wrap(err, "error getting guest database")
		}
		guestDatabaseID = &guestPage.ID
		q.RoleTags = []string{memberrole.GUEST}
	}

	queries := []*gorm.DB{}
	if len(q.Scope.MemberIDs) != 0 {
		queries = append(queries, db.Where("members.id IN (?)", q.Scope.MemberIDs))
	}
	if len(q.Scope.TagIDs) != 0 {
		tx = tx.Joins("LEFT JOIN member_tags ON member_tags.member_id = members.id")
		queries = append(queries, db.Where("member_tags.tag_id IN (?)", q.Scope.TagIDs))
	}
	if len(q.Scope.RoleTags) != 0 {
		queries = append(queries, db.Where("members.role IN (?)", q.Scope.RoleTags))
	}
	query := db.JoinOrQueries(queries)
	if query != nil {
		tx = tx.Where(query)
	}

	if q.JoinDate != nil {
		tx = tx.Joins("LEFT JOIN chats ON chats.actor_id = members.id and chats.message IN (?)", []string{logaction.MEMBER_CREATE_WORKSPACE, logaction.MEMBER_JOIN})
		if q.JoinDate.FromDate.Valid {
			tx = tx.Where("chats.created_at >= ?", q.JoinDate.FromDate.Time)
		}
		if q.JoinDate.ToDate.Valid {
			tx = tx.Where("chats.created_at <= ?", q.JoinDate.ToDate.Time.AddDate(0, 0, 1))
		}
	}

	if q.Bookmark.Valid {
		bookMarkQuery := "member_bookmarked_items.id IS NOT NULL"
		if !q.Bookmark.Bool {
			bookMarkQuery = "member_bookmarked_items.id IS NULL"
		}
		tx = tx.Where(bookMarkQuery)
	}

	var workspaceType string
	err := db.Model(&model.Workspace{}).Where("id = ?", workspaceID).Select("pricing_plan").Scan(&workspaceType).Error
	if err != nil {
		return nil, errors.Wrap(err, "error getting workspace pricing plan: "+err.Error())
	}

	err = s.addRoleControlFilter(ctx, tx, workspaceID, requesterID, q.RoleTags)
	if err != nil {
		return nil, err
	}

	reqTagIDs, err := s.tagRepo.ToUUIDs(workspaceID, &q.TagIDs)
	if err != nil {
		return nil, errors.Wrap(err, "error getting tag ids")
	}
	tagGroupMap := map[uuid.UUID][]uuid.UUID{}
	if len(q.TagIDs) != 0 {
		tags, err := s.tagRepo.FindByStatusPublishedAndWorkspaceIDAndIDIn(workspaceID, reqTagIDs)
		if err != nil {
			return nil, errors.Wrap(err, "error getting Tags: "+err.Error())
		}
		for _, tag := range tags {
			tagGroupMap[tag.TagGroupID] = append(tagGroupMap[tag.TagGroupID], tag.ID)
		}
	}

	idx := 0
	for _, tagIDs := range tagGroupMap {
		memberTags := fmt.Sprintf("member_tags%d", idx)
		tx = tx.Joins(fmt.Sprintf("JOIN member_tags as %s ON %s.member_id = members.id", memberTags, memberTags))
		tx = tx.Where(fmt.Sprintf("%s.tag_id IN (?)", memberTags), tagIDs)
		idx++
	}

	tx = tx.Where("members.status = ?", memberstatus.ACTIVE)

	if q.Query != "" {
		q.Query = strings.ReplaceAll(q.Query, "%", "\\%")
		q.Query = "%" + q.Query + "%"
		tx = tx.Where("COALESCE(members.name, '') || ' ' || COALESCE(members.background_text, '') ILIKE ?", q.Query)
	}

	if len(q.FilteringText) > 0 {
		for idx, filteringText := range q.FilteringText {
			memberFiName := "member_fi_text_" + strconv.Itoa(idx)
			fiName := "fi_text_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringText.FormItemID, tx, guestDatabaseID)
			queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NUll " + " THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) ILIKE ?"
			tx = tx.Where(queryString, "%"+filteringText.Value+"%")
		}
	}

	if len(q.FilteringTextAreaReq) > 0 {
		for idx, filteringText := range q.FilteringTextAreaReq {
			memberFiName := "member_fi_text_area_" + strconv.Itoa(idx)
			fiName := "fi_text_are_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringText.FormItemID, tx, guestDatabaseID)
			queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NUll " + " THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) ILIKE ?"
			tx = tx.Where(queryString, "%"+filteringText.Value+"%")
		}
	}

	if len(q.FilteringCheckbox) > 0 {
		for idx, filteringCheckbox := range q.FilteringCheckbox {
			memberFiName := "member_fi_checkbox_" + strconv.Itoa(idx)
			fiName := "fi_checkbox_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringCheckbox.FormItemID, tx, guestDatabaseID)
			queryString := ""
			for idx, id := range filteringCheckbox.Value {
				if idx != 0 {
					queryString += " OR "
				}
				queryString += "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->'value' ELSE " + memberFiName + ".data_obj->'value' END) ? '" + id.String() + "' "
			}
			tx = tx.Where(queryString)
		}
	}

	if len(q.FilteringDropdown) > 0 {
		for idx, filteringDropdown := range q.FilteringDropdown {
			ids := []uuid.UUID{}
			ids = append(ids, filteringDropdown.Value...)
			if len(ids) > 0 {
				memberFiName := "member_fi_dropdown_" + strconv.Itoa(idx)
				fiName := "fi_dropdown_" + strconv.Itoa(idx)
				s.createJoinMemberFIAndFI(memberFiName, fiName, filteringDropdown.FormItemID, tx, guestDatabaseID)
				queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) IN ? "
				tx = tx.Where(queryString, ids)
			}
		}
	}

	if len(q.FilteringRadioButton) > 0 {
		for idx, filteringRadio := range q.FilteringRadioButton {
			ids := []uuid.UUID{}
			ids = append(ids, filteringRadio.Value...)
			if len(ids) > 0 {
				memberFiName := "member_fi_radio_" + strconv.Itoa(idx)
				fiName := "fi_radio_" + strconv.Itoa(idx)
				s.createJoinMemberFIAndFI(memberFiName, fiName, filteringRadio.FormItemID, tx, guestDatabaseID)
				queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END)  IN ? "
				tx = tx.Where(queryString, ids)
			}
		}
	}

	if len(q.FilteringDate) > 0 { // nolint
		for idx, filteringDate := range q.FilteringDate {
			memberFiName := "member_fi_date_" + strconv.Itoa(idx)
			fiName := "fi_date_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringDate.FormItemID, tx, guestDatabaseID)
			queryString := ""
			switch {
			case filteringDate.From != "" && filteringDate.To == "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  >= ?  AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.From)
			case filteringDate.From == "" && filteringDate.To != "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  <= ?   AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.To)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  >= ?   AT TIME ZONE 'UTC' AND " + "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  <= ?   AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.From, filteringDate.To)
			}
		}
	}

	if len(q.FilteringTime) > 0 {
		for idx, filteringTime := range q.FilteringTime {
			memberFiName := "member_fi_time_" + strconv.Itoa(idx)
			fiName := "fi_time_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringTime.FormItemID, tx, guestDatabaseID)
			queryString := ""
			switch {
			case filteringTime.From != "" && filteringTime.To == "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  >= ?::time   "
				tx = tx.Where(queryString, filteringTime.From)
			case filteringTime.From == "" && filteringTime.To != "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  <= ?::time   "
				tx = tx.Where(queryString, filteringTime.To)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  BETWEEN ?::time AND ?::time   "
				tx = tx.Where(queryString, filteringTime.From, filteringTime.To)
			}
		}
	}

	if len(q.FilteringNumber) > 0 {
		for idx, filteringNumber := range q.FilteringNumber {
			memberFiName := "member_fi_number_" + strconv.Itoa(idx)
			fiName := "fi_number_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringNumber.FormItemID, tx, guestDatabaseID)
			queryString := ""
			switch {
			case filteringNumber.Min.Valid && !filteringNumber.Max.Valid:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  >= ? "
				tx = tx.Where(queryString, filteringNumber.Min.Float64)
			case !filteringNumber.Min.Valid && filteringNumber.Max.Valid:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  <= ? "
				tx = tx.Where(queryString, filteringNumber.Max.Float64)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  >= ? AND" + "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  <= ? "
				tx = tx.Where(queryString, filteringNumber.Min.Float64, filteringNumber.Max.Float64)
			}
		}
	}

	if q.IgnoreCurrentUser {
		tx = tx.Where("members.id != ?", requesterID)
	}

	var members []*model.Member

	result := tx.
		Group("members.id").
		Select("members.id").
		Find(&members)
	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "error getting Members")
	}

	memberIDs := []uuid.UUID{}
	for _, member := range members {
		memberIDs = append(memberIDs, member.ID)
	}

	return memberIDs, nil
}

func (s *memberService) FindByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersByQueryParamsAndFacilitatorAndShareScope) ([]*dto.Member, bool, error) {

	memberIDs, err := s.findByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx, workspaceID, requesterID, q)
	if err != nil {
		return nil, false, err
	}

	hasMore := false

	// var dtoMembers []*dto.Member
	var (
		dtoMembers             = make([]*dto.Member, len(memberIDs))
		errChan                = make(chan error, len(memberIDs))
		wg                     sync.WaitGroup
		semaphore              = make(chan struct{}, 10)
		mutex                  sync.Mutex
		memberIDsDiffRequester = make([]uuid.UUID, len(memberIDs))
	)

	for i, memberID := range memberIDs {
		wg.Add(1)
		semaphore <- struct{}{}
		go func(i int, memberID uuid.UUID) {
			defer wg.Done()
			defer func() { <-semaphore }()
			mb, err := s.GetMemberResponse(ctx, requesterID, memberID, true, true)
			if err != nil {
				errChan <- err
				return
			}
			mutex.Lock()
			// dtoMembers = append(dtoMembers, &mb)
			dtoMembers[i] = &mb
			if requesterID != memberID {
				memberIDsDiffRequester = append(memberIDsDiffRequester, memberID)
			}
			mutex.Unlock()
		}(i, memberID)
	}
	wg.Wait()
	close(errChan)

	for err := range errChan {
		if err != nil {
			s.c.GetLogger().WithContext(ctx).Errorf("error getting member response: %v", err)
			return nil, false, err
		}
	}

	idsHasUnread, err := s.GetTargetIDsHasNewMessage(ctx, requesterID, memberIDsDiffRequester)
	if err != nil {
		return nil, false, err
	}

	chatInfos, err := s.GetMemberChatInfoByTargetIDs(ctx, workspaceID, requesterID, memberIDsDiffRequester)
	if err != nil {
		return nil, false, err
	}

	for idx, dtoMember := range dtoMembers {
		chatRoomId := memberCommon.GenerateChatRoomID(dtoMember.ID.String(), requesterID.String(), dtoMember.Role, false)
		if sliceutil.Contain(idsHasUnread, chatRoomId) {
			dtoMembers[idx].HasNewMessage = null.BoolFrom(true)
		}
		chatInfo := chatInfos[chatRoomId]
		if chatInfo.ChatInfo != nil {
			dtoMembers[idx].ChatInfo = &chatInfo
		}
	}

	return dtoMembers, hasMore, nil
}

func (s *memberService) FindMemberIconsByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersByQueryParamsAndFacilitatorAndShareScope) ([]*dto.MemberIcon, error) {
	memberIDs, err := s.findByWorkspaceIDAndFacilitatorAndShareScopeAndQueryParams(ctx, workspaceID, requesterID, q)
	if err != nil {
		return nil, err
	}

	var (
		memberIcons = make([]*dto.MemberIcon, len(memberIDs))
		errChan     = make(chan error, len(memberIDs))
		wg          sync.WaitGroup
		semaphore   = make(chan struct{}, 10)
		mutex       sync.Mutex
	)

	for i, memberID := range memberIDs {
		wg.Add(1)
		semaphore <- struct{}{}
		go func(i int, memberID uuid.UUID) {
			defer wg.Done()
			defer func() { <-semaphore }()
			mb, err := s.GetLiteDataForMember(ctx, requesterID, memberID)
			if err != nil {
				errChan <- err
				return
			}
			mutex.Lock()
			memberIcons[i] = mb.ToMemberIcon()
			mutex.Unlock()
		}(i, memberID)
	}
	wg.Wait()
	close(errChan)

	for err := range errChan {
		if err != nil {
			s.c.GetLogger().WithContext(ctx).Errorf("error getting member response: %v", err)
			return nil, err
		}
	}

	return memberIcons, nil
}

func (s *memberService) FindByWorkspaceIDAndRequesterIDandRequesterRoleAndQueryParamsViewByManagement(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, requesterRole string, q dto.GetMembersBodyViewByManagement) ([]*dto.Member, bool, error) {
	db := s.c.GetRepository(ctx)

	tx := db.Model(&model.Member{}).
		Joins("LEFT JOIN member_bookmarked_items ON member_bookmarked_items.target_id = members.id AND member_bookmarked_items.deleted_at = 0").
		Where(&model.Member{WorkspaceID: workspaceID}).Where("members.is_ghost IS NOT true")
	statusTags := q.StatusTags
	// if requesterRole != memberrole.MANAGER {
	// 	statusTags = []string{memberstatus.ACTIVE}
	// }

	var guestDatabaseID *uuid.UUID
	if q.ForGuestPage.Bool {
		guestPage := &model.Database{
			WorkspaceID: workspaceID,
			Type:        null.StringFrom("GUEST_DATABASE"),
		}
		if err := db.Model(&model.Database{}).Where(guestPage).First(guestPage).Error; err != nil {
			return nil, false, errors.Wrap(err, "error getting guest database id: "+err.Error())
		}

		guestDatabaseID = &guestPage.ID
		q.RoleTags = []string{memberrole.GUEST}
	}

	order := "ASC"
	if q.Sort == "-" {
		order = "DESC"
	}
	tx = tx.Order("members.name " + order)

	if q.Page != 0 {
		tx = tx.Offset((q.Page - 1) * q.PageSize).Limit(q.PageSize)
	}

	reqTagIDs, err := s.tagRepo.ToUUIDs(workspaceID, &q.TagIDs)
	if err != nil {
		return nil, false, errors.Wrap(err, "error getting tag ids")
	}
	tagGroupMap := map[uuid.UUID][]uuid.UUID{}
	if len(q.TagIDs) != 0 {
		tags, err := s.tagRepo.FindByStatusPublishedAndWorkspaceIDAndIDIn(workspaceID, reqTagIDs)
		if err != nil {
			return nil, false, errors.Wrap(err, "error getting Tags: "+err.Error())
		}
		for _, tag := range tags {
			tagGroupMap[tag.TagGroupID] = append(tagGroupMap[tag.TagGroupID], tag.ID)
		}
	}

	idx := 0
	for _, tagIDs := range tagGroupMap {
		memberTags := fmt.Sprintf("member_tags%d", idx)
		tx = tx.Joins(fmt.Sprintf("JOIN member_tags as %s ON %s.member_id = members.id", memberTags, memberTags))
		tx = tx.Where(fmt.Sprintf("%s.tag_id IN (?)", memberTags), tagIDs)
		idx++
	}

	err = s.addRoleControlFilter(ctx, tx, workspaceID, requesterID, q.RoleTags)
	if err != nil {
		return nil, false, err
	}
	if len(statusTags) != 0 {
		tx = tx.Where("members.status IN (?)", statusTags)
	}

	if q.Query != "" {
		q.Query = strings.ReplaceAll(q.Query, "%", "\\%")
		q.Query = "%" + q.Query + "%"
		tx = tx.Where("COALESCE(members.name, '') || ' ' || COALESCE(members.background_text, '') ILIKE ?", q.Query)
	}

	if len(q.FilteringTextReq) > 0 {
		for idx, filteringText := range q.FilteringTextReq {
			memberFiName := "member_fi_text_" + strconv.Itoa(idx)
			fiName := "fi_text_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringText.FormItemID, tx, guestDatabaseID)
			queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NUll " + " THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) ILIKE ?"
			tx = tx.Where(queryString, "%"+filteringText.Value+"%")
		}
	}

	if len(q.FilteringTextAreaReq) > 0 {
		for idx, filteringText := range q.FilteringTextAreaReq {
			memberFiName := "member_fi_text_area_" + strconv.Itoa(idx)
			fiName := "fi_text_area_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringText.FormItemID, tx, guestDatabaseID)
			queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NUll " + " THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) ILIKE ?"
			tx = tx.Where(queryString, "%"+filteringText.Value+"%")
		}
	}

	if len(q.FilteringCheckboxReq) > 0 {
		for idx, filteringCheckbox := range q.FilteringCheckboxReq {
			memberFiName := "member_fi_checkbox_" + strconv.Itoa(idx)
			fiName := "fi_checkbox_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringCheckbox.FormItemID, tx, guestDatabaseID)
			queryString := ""
			for idx, id := range filteringCheckbox.Value {
				if idx != 0 {
					queryString += " OR "
				}
				queryString += "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->'value' ELSE " + memberFiName + ".data_obj->'value' END) ? '" + id.String() + "' "
			}
			tx = tx.Where(queryString)
		}
	}

	if len(q.FilteringDropdownReq) > 0 {
		for idx, filteringDropdown := range q.FilteringDropdownReq {
			ids := []uuid.UUID{}
			ids = append(ids, filteringDropdown.Value...)
			if len(ids) > 0 {
				memberFiName := "member_fi_dropdown_" + strconv.Itoa(idx)
				fiName := "fi_dropdown_" + strconv.Itoa(idx)
				s.createJoinMemberFIAndFI(memberFiName, fiName, filteringDropdown.FormItemID, tx, guestDatabaseID)
				queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END) IN ? "
				tx = tx.Where(queryString, ids)
			}
		}
	}

	if len(q.FilteringRadioButtonReq) > 0 {
		for idx, filteringRadio := range q.FilteringRadioButtonReq {
			ids := []uuid.UUID{}
			ids = append(ids, filteringRadio.Value...)
			if len(ids) > 0 {
				memberFiName := "member_fi_radio_" + strconv.Itoa(idx)
				fiName := "fi_radio_" + strconv.Itoa(idx)
				s.createJoinMemberFIAndFI(memberFiName, fiName, filteringRadio.FormItemID, tx, guestDatabaseID)
				queryString := "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN " + fiName + ".data_obj->>'value' ELSE " + memberFiName + ".data_obj->>'value' END)  IN ? "
				tx = tx.Where(queryString, ids)
			}
		}
	}

	if len(q.FilteringDateReq) > 0 { // nolint
		for idx, filteringDate := range q.FilteringDateReq {
			memberFiName := "member_fi_date_" + strconv.Itoa(idx)
			fiName := "fi_date_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringDate.FormItemID, tx, guestDatabaseID)
			queryString := ""
			switch {
			case filteringDate.From != "" && filteringDate.To == "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  >= ?  AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.From)
			case filteringDate.From == "" && filteringDate.To != "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  <= ?   AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.To)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  >= ?   AT TIME ZONE 'UTC' AND " + "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::timestamp ELSE (" + memberFiName + ".data_obj->>'value')::timestamp END)  <= ?   AT TIME ZONE 'UTC'"
				tx = tx.Where(queryString, filteringDate.From, filteringDate.To)
			}
		}
	}

	if len(q.FilteringTimeReq) > 0 {
		for idx, filteringTime := range q.FilteringTimeReq {
			memberFiName := "member_fi_time_" + strconv.Itoa(idx)
			fiName := "fi_time_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringTime.FormItemID, tx, guestDatabaseID)
			queryString := ""
			switch {
			case filteringTime.From != "" && filteringTime.To == "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  >= ?::time   "
				tx = tx.Where(queryString, filteringTime.From)
			case filteringTime.From == "" && filteringTime.To != "":
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  <= ?::time   "
				tx = tx.Where(queryString, filteringTime.To)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::time ELSE (" + memberFiName + ".data_obj->>'value')::time END)  BETWEEN ?::time AND ?::time   "
				tx = tx.Where(queryString, filteringTime.From, filteringTime.To)
			}
		}
	}

	if len(q.FilteringNumberReq) > 0 {
		for idx, filteringNumber := range q.FilteringNumberReq {
			memberFiName := "member_fi_number_" + strconv.Itoa(idx)
			fiName := "fi_number_" + strconv.Itoa(idx)
			s.createJoinMemberFIAndFI(memberFiName, fiName, filteringNumber.FormItemID, tx, guestDatabaseID)
			queryString := ""
			switch {
			case filteringNumber.Min.Valid && !filteringNumber.Max.Valid:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  >= ? "
				tx = tx.Where(queryString, filteringNumber.Min.Float64)
			case !filteringNumber.Min.Valid && filteringNumber.Max.Valid:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  <= ? "
				tx = tx.Where(queryString, filteringNumber.Max.Float64)
			default:
				queryString = "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  >= ? AND" + "(CASE WHEN " + memberFiName + "." + "data_obj IS NULL THEN (" + fiName + ".data_obj->>'value')::float ELSE (" + memberFiName + ".data_obj->>'value')::float END)  <= ? "
				tx = tx.Where(queryString, filteringNumber.Min.Float64, filteringNumber.Max.Float64)
			}
		}
	}

	if q.JoinDate != nil {
		tx = tx.Joins("LEFT JOIN chats ON chats.actor_id = members.id and chats.message IN (?)", []string{logaction.MEMBER_CREATE_WORKSPACE, logaction.MEMBER_JOIN})
		if q.JoinDate.FromDate.Valid {
			tx = tx.Where("chats.created_at >= ?", q.JoinDate.FromDate.Time)
		}
		if q.JoinDate.ToDate.Valid {
			tx = tx.Where("chats.created_at <= ?", q.JoinDate.ToDate.Time.AddDate(0, 0, 1))
		}
	}

	if q.IgnoreCurrentUser {
		tx = tx.Where("members.id != ?", requesterID)
	}

	if q.Bookmark.Bool {
		tx = tx.Where("member_bookmarked_items.id IS NOT NULL")
	}

	if q.OnlyUnread.Bool {
		tx = tx.
			// Both member and requester must be participants
			Joins("LEFT JOIN chat_infos ON members.id::TEXT = ANY(chat_infos.participant_ids) AND ?::TEXT = ANY(chat_infos.participant_ids) AND chat_infos.total_mess > 0 AND chat_infos.is_private_chat = false AND members.id != ?", requesterID.String(), requesterID).
			Joins("LEFT JOIN member_reads ON member_reads.group_id = chat_infos.group_id AND member_reads.member_id = ? ", requesterID).
			Joins("LEFT JOIN records ON records.origin_id = members.id AND records.deleted_at = 0").
			Joins("LEFT JOIN chat_infos AS record_chat_infos ON record_chat_infos.group_id = records.id::TEXT AND ?::TEXT = ANY(record_chat_infos.participant_ids) AND record_chat_infos.total_mess > 0 AND record_chat_infos.is_private_chat = false", requesterID.String()).
			Joins("LEFT JOIN member_reads AS record_member_reads ON record_member_reads.group_id = record_chat_infos.group_id AND record_member_reads.member_id = ? ", requesterID).
			Where("((member_reads.last_read_time IS NULL OR chat_infos.last_mess_time >= member_reads.last_read_time) AND chat_infos.id IS NOT NULL) OR ((record_member_reads.last_read_time IS NULL OR record_chat_infos.last_mess_time >= record_member_reads.last_read_time) AND record_chat_infos.id IS NOT NULL)")
	}

	var members []*model.Member

	result := tx.
		Group("members.id").
		Select("members.id").
		Find(&members)
	if result.Error != nil {
		return nil, false, errors.Wrap(result.Error, "error getting Members")
	}

	hasMore := false
	if result.RowsAffected > int64(q.PageSize) {
		hasMore = true
	}

	// Extract member IDs for batch processing
	memberIDs := []uuid.UUID{}
	for _, member := range members {
		memberIDs = append(memberIDs, member.ID)
	}

	// Use batch method to get all member responses at once
	memberResponses, err := s.GetMembersResponse(ctx, workspaceID, requesterID, memberIDs, true, true)
	if err != nil {
		return nil, false, err
	}

	// Convert to pointer slice
	dtoMembers := make([]*dto.Member, len(memberResponses))
	for i, memberResponse := range memberResponses {
		dtoMembers[i] = &memberResponse
	}

	idsHasUnread, err := s.GetTargetIDsHasNewMessage(ctx, requesterID, memberIDs)
	if err != nil {
		return nil, false, err
	}

	chatInfos, err := s.GetMemberChatInfoByTargetIDs(ctx, workspaceID, requesterID, memberIDs)
	if err != nil {
		return nil, false, err
	}
	displayableMessageTypes := []string{messagetype.TEXT, messagetype.MEMBER, messagetype.IMAGE, messagetype.LOCATION, messagetype.TAG_NUMBER}
	for idx, dtoMember := range dtoMembers {
		chatRoomId := memberCommon.GenerateChatRoomID(requesterID.String(), dtoMember.ID.String(), dtoMember.Role, false)
		if sliceutil.Contain(idsHasUnread, chatRoomId) {
			dtoMembers[idx].HasNewMessage = null.BoolFrom(true)
		}

		chatInfo := chatInfos[chatRoomId]
		if chatInfo.ChatInfo != nil {
			dtoMembers[idx].ChatInfo = &chatInfo
			latestMessage, _ := s.FindLatestMessageByWorkspaceIDAndRecordID(ctx, chatInfo.WorkspaceID, requesterID, chatRoomId)
			if latestMessage != nil {
				if sliceutil.Contain(displayableMessageTypes, latestMessage.MessageType.String) && latestMessage.Message != "" {
					dtoMembers[idx].ChatInfo.LatestMessage = latestMessage
				}
			}
		}
	}

	return dtoMembers, hasMore, nil
}

func (s *memberService) FindLatestMessageByWorkspaceIDAndRecordID(ctx context.Context, workspaceID uuid.UUID, requesterID uuid.UUID, groupID string) (*model.Chat, error) {
	db := s.c.GetRepository(ctx)
	latestChat := model.Chat{}

	tx := db.Model(&model.Chat{}).
		// Add joins for chat_infos and member_reads
		Joins("LEFT JOIN chat_infos ON chat_infos.group_id = chats.id::TEXT AND chat_infos.workspace_id = ?", workspaceID).
		Joins("LEFT JOIN member_reads ON member_reads.group_id = chats.id::TEXT AND member_reads.member_id::text = ?", requesterID).
		Where("chats.do_archive = false").
		Group("chats.id")

	// Find the latest message related to the record
	tx = tx.
		Where("chats.workspace_id = ? AND chats.group_id = ?", workspaceID, groupID).
		Order("chats.created_at DESC")

	result := tx.First(&latestChat)
	if result.Error != nil {
		return nil, result.Error
	}

	return &latestChat, nil
}
func (s *memberService) FindInvitingMemberByInvitedCode(ctx context.Context, invitedCode string) (*model.WorkspaceInvite, error) {
	db := s.c.GetRepository(ctx)
	workspaceInviteInstance := &model.WorkspaceInvite{}
	result := db.Where("invited_code = ?", invitedCode).First(workspaceInviteInstance)
	if result.Error != nil {
		return nil, errors.Wrap(result.Error, "error getting Member")
	}

	return workspaceInviteInstance, nil
}

func (s *memberService) getInfoRelationMemberID(ctx context.Context, requesterID, memberID uuid.UUID) (*dto.MemberInfoRelation, error) {
	if requesterID == memberID {
		return nil, nil
	}
	var mInfo dto.MemberInfoRelation
	db := s.c.GetRepository(ctx)
	err := db.Model(&model.Record{}).
		Joins("INNER JOIN record_assignees ON records.id = record_assignees.record_id AND record_assignees.deleted_at = 0 AND record_assignees.assignee_id = ?", memberID).
		Where("records.creator_id = ?", requesterID).
		Count(&mInfo.TotalSelfCreatedTasksForColleague).Error
	if err != nil {
		return nil, err
	}

	err = db.Model(&model.Record{}).
		Joins("INNER JOIN record_assignees ON records.id = record_assignees.record_id AND record_assignees.deleted_at = 0 AND record_assignees.assignee_id = ?", requesterID).
		Where("records.creator_id = ?", memberID).
		Count(&mInfo.TotalColleaguesCreatedTasksForMe).Error
	if err != nil {
		return nil, err
	}
	return &mInfo, err
}

func (s *memberService) validateSuspendManager(ctx context.Context, workspaceID, managerID uuid.UUID) error {
	db := s.c.GetRepository(ctx)
	var managerIDs, tagMangerIDs, externalLinkIDs []uuid.UUID
	err := db.Model(&model.Member{}).Select("id").Where("workspace_id = ? AND id != ?", workspaceID, managerID).Scan(&managerIDs).Error
	if err != nil {
		return nil
	}

	err = db.Raw("select tag_id from member_tags where member_id IN ?", managerIDs).Scan(&tagMangerIDs).Error
	if err != nil {
		return nil
	}

	err = db.Model(&model.ExternalLink{}).Select("id").Where("workspace_id = ?", workspaceID).Scan(&externalLinkIDs).Error
	if err != nil {
		return nil
	}
	mapTargetIDs := map[uuid.UUID]int{}

	var targetMemberIDs []uuid.UUID
	err = db.Model(&model.FacilitatorMember{}).Select("DISTINCT target_id").Where("workspace_id = ? AND member_id IN ? AND target_type IN ?", workspaceID, managerIDs, []string{"workspaces", "navigations", "profile_items"}).Scan(&targetMemberIDs).Error
	if err != nil {
		return err
	}
	for _, id := range targetMemberIDs {
		mapTargetIDs[id] = 0
	}

	var targetTagIDs []uuid.UUID
	err = db.Model(&model.FacilitatorTag{}).Select("DISTINCT target_id").Where("workspace_id = ? AND tag_id IN ? AND target_type IN ?", workspaceID, tagMangerIDs, []string{"workspaces", "navigations", "profile_items"}).Scan(&targetTagIDs).Error
	if err != nil {
		return err
	}
	for _, id := range targetTagIDs {
		mapTargetIDs[id] = 0
	}

	var targetExIDs []uuid.UUID
	err = db.Model(&model.FacilitatorExternalLink{}).Select("DISTINCT target_id").Where("workspace_id = ? AND external_link_id IN ? AND target_type IN ?", workspaceID, externalLinkIDs, []string{"workspaces", "navigations", "profile_items"}).Scan(&targetExIDs).Error
	if err != nil {
		return err
	}
	for _, id := range targetExIDs {
		mapTargetIDs[id] = 0
	}
	if len(mapTargetIDs) < 3 {
		return errors.Wrap(apihelper.ErrForbidden, "The edit workspace, navigation, or profile features will not work if a member is deleted")
	}

	return nil
}

func (s *memberService) addRoleControlFilter(ctx context.Context, tx *gorm.DB, workspaceID, requesterID uuid.UUID, roleTags []string) error {
	var requester model.Member
	db := s.c.GetRepository(ctx)
	err := db.Select("role").Where("id = ?", requesterID).First(&requester).Error
	if err != nil {
		return err
	}
	if len(roleTags) == 0 {
		roleTags = append(roleTags, memberrole.MANAGER, memberrole.STAFF)
	}
	queries := []*gorm.DB{}
	for _, role := range roleTags {
		switch role {
		case memberrole.MANAGER:
			queries = append(queries, db.Where("members.role = ? ", memberrole.MANAGER))
		case memberrole.STAFF:
			queries = append(queries, db.Where("members.role = ? ", memberrole.STAFF))

		case memberrole.GUEST:
			facilitatorTargetIDs, err := s.facilitatorService.FindTargetIDByWorkspaceIDAndTargetType(ctx, workspaceID, requesterID, targettype.MEMBER)
			if err != nil {
				return err
			}
			shareScopeTargetIDs, err := s.shareScopeService.FindTargetIDByWorkspaceIDAndTargetType(ctx, workspaceID, requesterID, targettype.MEMBER)
			if err != nil {
				return err
			}
			queries = append(queries, db.Where("members.role = ? AND members.id IN ?", memberrole.GUEST, append(facilitatorTargetIDs, shareScopeTargetIDs...)))
		}
	}
	query := db.JoinOrQueries(queries)
	if query != nil {
		tx = tx.Where(query) //nolint
	}
	return nil
}

func (s *memberService) checkVisibleMember(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) error {
	err := s.CanModifyMember(ctx, workspaceID, requesterID, memberID)
	if err == nil {
		return nil
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	err = s.canViewMember(ctx, workspaceID, requesterID, memberID)
	if err != nil {
		return err
	}
	return nil
}

func (s *memberService) CanModifyMember(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) error {
	var tagIDs []uuid.UUID
	db := s.c.GetRepository(ctx)
	err := db.Raw("select tag_id from member_tags where member_id = ?", requesterID).Scan(&tagIDs).Error
	if err != nil {
		return err
	}

	var role string
	err = db.Model(&model.Member{}).Select("role").Where("id = ?", requesterID).Scan(&role).Error
	if err != nil {
		return err
	}

	var facilitatorMember model.FacilitatorMember
	err = db.Where("workspace_id = ? AND member_id = ? AND target_id = ?", workspaceID, requesterID, memberID).First(&facilitatorMember).Error
	if err == nil {
		return nil
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	var facilitatorTag model.FacilitatorTag
	err = db.Where("workspace_id = ? AND tag_id IN ? AND target_id = ?", workspaceID, tagIDs, memberID).First(&facilitatorTag).Error
	if err == nil {
		return nil
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	var facilitatorRole model.FacilitatorRole
	err = db.Where("role = ? AND target_id = ?", role, memberID).First(&facilitatorRole).Error
	if err != nil {
		return err
	}
	return nil
}

func (s *memberService) canViewMember(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) error {
	var shareMember model.ShareScopeMember
	var tagIDs []uuid.UUID
	db := s.c.GetRepository(ctx)
	err := db.Raw("select tag_id from member_tags where member_id = ?", requesterID).Scan(&tagIDs).Error
	if err != nil {
		return err
	}
	err = db.Where("workspace_id = ? AND member_id = ? AND target_id = ?", workspaceID, requesterID, memberID).First(&shareMember).Error
	if err == nil {
		return nil
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	var shareTag model.ShareScopeTag
	err = db.Where("workspace_id = ? AND tag_id IN ? AND target_id = ?", workspaceID, tagIDs, memberID).First(&shareTag).Error
	if err != nil {
		return err
	}
	return nil
}

func (s *memberService) FindTeammate(ctx context.Context, workspaceID, requesterID uuid.UUID, requesterRole string) ([]*dto.Member, error) {
	var members []*dto.Member
	var err error

	if requesterRole != "GUEST" {

		members, _, err = s.FindByWorkspaceIDAndRequesterIDandRequesterRoleAndQueryParamsViewByManagement(ctx, workspaceID, requesterID, requesterRole, dto.GetMembersBodyViewByManagement{
			IgnoreCurrentUser: true,
			RoleTags:          []string{memberrole.MANAGER, memberrole.STAFF, memberrole.GUEST},
			StatusTags:        []string{memberstatus.ACTIVE},
		})
		if err != nil {
			return nil, err
		}

	} else { // nolint
		res, err := s.GetChatMemberVisible(ctx, workspaceID, requesterID, requesterID)
		if err != nil {
			return nil, err
		}

		members = []*dto.Member{}
		for _, id := range res {
			if id != requesterID {
				member, err := s.GetMemberResponse(ctx, requesterID, id, true, true)
				if err != nil {
					return nil, err
				}
				members = append(members, &member)
			}
		}
	}

	return members, nil
}

func (s *memberService) GetChatMemberVisible(ctx context.Context, workspaceID, requesterID, memberID uuid.UUID) ([]uuid.UUID, error) {
	db := s.c.GetRepository(ctx)

	member, err := s.GetMemberResponse(ctx, requesterID, memberID, false, false)
	if err != nil {
		return nil, err
	}

	var res []uuid.UUID

	// if member.Role != memberrole.GUEST {
	// 	return []uuid.UUID{requesterID, memberID}, nil
	// }

	res = append(res, requesterID, memberID)

	facilitator := member.Facilitator

	if facilitator != nil {
		memberIDs := []uuid.UUID{}
		for _, facilitatorMember := range facilitator.FacilitatorMembersResponse {
			memberIDs = append(memberIDs, facilitatorMember.MemberID)
		}

		tagIDs := []uuid.UUID{}
		for _, facilitatorTag := range facilitator.FacilitatorTagsResponse {
			tagIDs = append(tagIDs, facilitatorTag.TagID)
		}

		if len(tagIDs) > 0 {
			var memberTagIDs []uuid.UUID
			err = db.
				Model(&model.Member{}).
				Select("id").
				Joins("JOIN member_tags ON members.id = member_tags.member_id").
				Where("tag_id IN (?)", tagIDs).Scan(&memberTagIDs).Error
			if err != nil {
				return nil, err
			}
			memberIDs = append(memberIDs, memberTagIDs...)
		}

		roles := []string{}
		for _, facilitatorRole := range facilitator.FacilitatorRolesResponse {
			roles = append(roles, facilitatorRole.Role)
		}

		if len(roles) > 0 {
			var memberRoleIDs []uuid.UUID
			err = db.
				Model(&model.Member{}).
				Select("id").
				Where("role IN (?)", roles).Scan(&memberRoleIDs).Error
			if err != nil {
				return nil, err
			}
			memberIDs = append(memberIDs, memberRoleIDs...)
		}
		unique := sliceutil.RemoveDuplicates(memberIDs)
		res = append(res, unique...)
	}

	shareScope := member.ShareScope

	if shareScope != nil {
		memberIDs := []uuid.UUID{}
		for _, shareScopeMember := range shareScope.ShareScopeMembersResponse {
			memberIDs = append(memberIDs, shareScopeMember.MemberID)
		}

		tagIDs := []uuid.UUID{}
		for _, shareScopeTag := range shareScope.ShareScopeTagsResponse {
			tagIDs = append(tagIDs, shareScopeTag.TagID)
		}

		if len(tagIDs) > 0 {
			var memberTagIDs []uuid.UUID
			err = db.
				Model(&model.Member{}).
				Select("id").
				Joins("JOIN member_tags ON members.id = member_tags.member_id").
				Where("tag_id IN (?)", tagIDs).Scan(&memberTagIDs).Error
			if err != nil {
				return nil, err
			}
			memberIDs = append(memberIDs, memberTagIDs...)
		}

		unique := sliceutil.RemoveDuplicates(memberIDs)
		res = append(res, unique...)
	}

	unique := sliceutil.RemoveDuplicates(res)

	return unique, nil
}
