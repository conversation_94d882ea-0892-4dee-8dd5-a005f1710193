//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelMentionedExternalLink {
  /// Returns a new [ModelMentionedExternalLink] instance.
  ModelMentionedExternalLink({
    this.deletedAt,
    required this.id,
    this.name,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelMentionedExternalLink &&
     other.deletedAt == deletedAt &&
     other.id == id &&
     other.name == name;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (id.hashCode) +
    (name == null ? 0 : name!.hashCode);

  @override
  String toString() => 'ModelMentionedExternalLink[deletedAt=$deletedAt, id=$id, name=$name]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
      json[r'id'] = this.id;
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
    return json;
  }

  /// Returns a new [ModelMentionedExternalLink] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelMentionedExternalLink? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelMentionedExternalLink[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelMentionedExternalLink[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelMentionedExternalLink(
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        id: mapValueOfType<String>(json, r'id')!,
        name: mapValueOfType<String>(json, r'name'),
      );
    }
    return null;
  }

  static List<ModelMentionedExternalLink>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelMentionedExternalLink>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelMentionedExternalLink.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelMentionedExternalLink> mapFromJson(dynamic json) {
    final map = <String, ModelMentionedExternalLink>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMentionedExternalLink.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelMentionedExternalLink-objects as value to a dart map
  static Map<String, List<ModelMentionedExternalLink>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelMentionedExternalLink>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelMentionedExternalLink.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
  };
}

