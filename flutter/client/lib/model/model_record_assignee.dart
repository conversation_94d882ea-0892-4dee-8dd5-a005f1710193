//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class ModelRecordAssignee {
  /// Returns a new [ModelRecordAssignee] instance.
  ModelRecordAssignee({
    required this.assigneeId,
    this.completedAt,
    required this.createdAt,
    this.deletedAt,
    this.dueDate,
    required this.id,
    this.pausedAt,
    required this.recordId,
    this.resumedAt,
    this.startedAt,
    this.status,
    this.taskAssigneeReportFormItems = const [],
    this.totalWorkTime,
    required this.updatedAt,
  });

  String assigneeId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? completedAt;

  String createdAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? deletedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? dueDate;

  String id;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? pausedAt;

  String recordId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? resumedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? startedAt;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? status;

  List<ModelRecordAssigneeReportFormItem> taskAssigneeReportFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  int? totalWorkTime;

  String updatedAt;

  @override
  bool operator ==(Object other) => identical(this, other) || other is ModelRecordAssignee &&
     other.assigneeId == assigneeId &&
     other.completedAt == completedAt &&
     other.createdAt == createdAt &&
     other.deletedAt == deletedAt &&
     other.dueDate == dueDate &&
     other.id == id &&
     other.pausedAt == pausedAt &&
     other.recordId == recordId &&
     other.resumedAt == resumedAt &&
     other.startedAt == startedAt &&
     other.status == status &&
     other.taskAssigneeReportFormItems == taskAssigneeReportFormItems &&
     other.totalWorkTime == totalWorkTime &&
     other.updatedAt == updatedAt;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (assigneeId.hashCode) +
    (completedAt == null ? 0 : completedAt!.hashCode) +
    (createdAt.hashCode) +
    (deletedAt == null ? 0 : deletedAt!.hashCode) +
    (dueDate == null ? 0 : dueDate!.hashCode) +
    (id.hashCode) +
    (pausedAt == null ? 0 : pausedAt!.hashCode) +
    (recordId.hashCode) +
    (resumedAt == null ? 0 : resumedAt!.hashCode) +
    (startedAt == null ? 0 : startedAt!.hashCode) +
    (status == null ? 0 : status!.hashCode) +
    (taskAssigneeReportFormItems.hashCode) +
    (totalWorkTime == null ? 0 : totalWorkTime!.hashCode) +
    (updatedAt.hashCode);

  @override
  String toString() => 'ModelRecordAssignee[assigneeId=$assigneeId, completedAt=$completedAt, createdAt=$createdAt, deletedAt=$deletedAt, dueDate=$dueDate, id=$id, pausedAt=$pausedAt, recordId=$recordId, resumedAt=$resumedAt, startedAt=$startedAt, status=$status, taskAssigneeReportFormItems=$taskAssigneeReportFormItems, totalWorkTime=$totalWorkTime, updatedAt=$updatedAt]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'assignee_id'] = this.assigneeId;
    if (this.completedAt != null) {
      json[r'completed_at'] = this.completedAt;
    } else {
      json[r'completed_at'] = null;
    }
      json[r'created_at'] = this.createdAt;
    if (this.deletedAt != null) {
      json[r'deleted_at'] = this.deletedAt;
    } else {
      json[r'deleted_at'] = null;
    }
    if (this.dueDate != null) {
      json[r'due_date'] = this.dueDate;
    } else {
      json[r'due_date'] = null;
    }
      json[r'id'] = this.id;
    if (this.pausedAt != null) {
      json[r'paused_at'] = this.pausedAt;
    } else {
      json[r'paused_at'] = null;
    }
      json[r'record_id'] = this.recordId;
    if (this.resumedAt != null) {
      json[r'resumed_at'] = this.resumedAt;
    } else {
      json[r'resumed_at'] = null;
    }
    if (this.startedAt != null) {
      json[r'started_at'] = this.startedAt;
    } else {
      json[r'started_at'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
      json[r'task_assignee_report_form_items'] = this.taskAssigneeReportFormItems;
    if (this.totalWorkTime != null) {
      json[r'total_work_time'] = this.totalWorkTime;
    } else {
      json[r'total_work_time'] = null;
    }
      json[r'updated_at'] = this.updatedAt;
    return json;
  }

  /// Returns a new [ModelRecordAssignee] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static ModelRecordAssignee? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "ModelRecordAssignee[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "ModelRecordAssignee[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return ModelRecordAssignee(
        assigneeId: mapValueOfType<String>(json, r'assignee_id')!,
        completedAt: mapValueOfType<String>(json, r'completed_at'),
        createdAt: mapValueOfType<String>(json, r'created_at')!,
        deletedAt: mapValueOfType<int>(json, r'deleted_at'),
        dueDate: mapValueOfType<String>(json, r'due_date'),
        id: mapValueOfType<String>(json, r'id')!,
        pausedAt: mapValueOfType<String>(json, r'paused_at'),
        recordId: mapValueOfType<String>(json, r'record_id')!,
        resumedAt: mapValueOfType<String>(json, r'resumed_at'),
        startedAt: mapValueOfType<String>(json, r'started_at'),
        status: mapValueOfType<String>(json, r'status'),
        taskAssigneeReportFormItems: ModelRecordAssigneeReportFormItem.listFromJson(json[r'task_assignee_report_form_items']) ?? const [],
        totalWorkTime: mapValueOfType<int>(json, r'total_work_time'),
        updatedAt: mapValueOfType<String>(json, r'updated_at')!,
      );
    }
    return null;
  }

  static List<ModelRecordAssignee>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <ModelRecordAssignee>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = ModelRecordAssignee.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, ModelRecordAssignee> mapFromJson(dynamic json) {
    final map = <String, ModelRecordAssignee>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelRecordAssignee.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of ModelRecordAssignee-objects as value to a dart map
  static Map<String, List<ModelRecordAssignee>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<ModelRecordAssignee>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = ModelRecordAssignee.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'assignee_id',
    'created_at',
    'id',
    'record_id',
    'updated_at',
  };
}

