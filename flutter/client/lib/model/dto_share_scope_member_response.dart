//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoShareScopeMemberResponse {
  /// Returns a new [DtoShareScopeMemberResponse] instance.
  DtoShareScopeMemberResponse({
    this.email,
    this.icon,
    this.iconKey,
    this.memberFormItems = const [],
    this.memberId,
    this.name,
    this.optionalFormItems = const [],
    this.role,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? email;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? icon;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? iconKey;

  List<ModelMemberFormItem> memberFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? memberId;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? name;

  List<ModelFormItem> optionalFormItems;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? role;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoShareScopeMemberResponse &&
     other.email == email &&
     other.icon == icon &&
     other.iconKey == iconKey &&
     other.memberFormItems == memberFormItems &&
     other.memberId == memberId &&
     other.name == name &&
     other.optionalFormItems == optionalFormItems &&
     other.role == role;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (email == null ? 0 : email!.hashCode) +
    (icon == null ? 0 : icon!.hashCode) +
    (iconKey == null ? 0 : iconKey!.hashCode) +
    (memberFormItems.hashCode) +
    (memberId == null ? 0 : memberId!.hashCode) +
    (name == null ? 0 : name!.hashCode) +
    (optionalFormItems.hashCode) +
    (role == null ? 0 : role!.hashCode);

  @override
  String toString() => 'DtoShareScopeMemberResponse[email=$email, icon=$icon, iconKey=$iconKey, memberFormItems=$memberFormItems, memberId=$memberId, name=$name, optionalFormItems=$optionalFormItems, role=$role]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.email != null) {
      json[r'email'] = this.email;
    } else {
      json[r'email'] = null;
    }
    if (this.icon != null) {
      json[r'icon'] = this.icon;
    } else {
      json[r'icon'] = null;
    }
    if (this.iconKey != null) {
      json[r'icon_key'] = this.iconKey;
    } else {
      json[r'icon_key'] = null;
    }
      json[r'member_form_items'] = this.memberFormItems;
    if (this.memberId != null) {
      json[r'member_id'] = this.memberId;
    } else {
      json[r'member_id'] = null;
    }
    if (this.name != null) {
      json[r'name'] = this.name;
    } else {
      json[r'name'] = null;
    }
      json[r'optional_form_items'] = this.optionalFormItems;
    if (this.role != null) {
      json[r'role'] = this.role;
    } else {
      json[r'role'] = null;
    }
    return json;
  }

  /// Returns a new [DtoShareScopeMemberResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoShareScopeMemberResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoShareScopeMemberResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoShareScopeMemberResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoShareScopeMemberResponse(
        email: mapValueOfType<String>(json, r'email'),
        icon: mapValueOfType<String>(json, r'icon'),
        iconKey: mapValueOfType<String>(json, r'icon_key'),
        memberFormItems: ModelMemberFormItem.listFromJson(json[r'member_form_items']) ?? const [],
        memberId: mapValueOfType<String>(json, r'member_id'),
        name: mapValueOfType<String>(json, r'name'),
        optionalFormItems: ModelFormItem.listFromJson(json[r'optional_form_items']) ?? const [],
        role: mapValueOfType<String>(json, r'role'),
      );
    }
    return null;
  }

  static List<DtoShareScopeMemberResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoShareScopeMemberResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoShareScopeMemberResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoShareScopeMemberResponse> mapFromJson(dynamic json) {
    final map = <String, DtoShareScopeMemberResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoShareScopeMemberResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoShareScopeMemberResponse-objects as value to a dart map
  static Map<String, List<DtoShareScopeMemberResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoShareScopeMemberResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoShareScopeMemberResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

