//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DtoGetChatSignedURLResponse {
  /// Returns a new [DtoGetChatSignedURLResponse] instance.
  DtoGetChatSignedURLResponse({
    this.signedUrls = const [],
  });

  List<DtoChatImageURL> signedUrls;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DtoGetChatSignedURLResponse &&
     other.signedUrls == signedUrls;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (signedUrls.hashCode);

  @override
  String toString() => 'DtoGetChatSignedURLResponse[signedUrls=$signedUrls]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'signed_urls'] = this.signedUrls;
    return json;
  }

  /// Returns a new [DtoGetChatSignedURLResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DtoGetChatSignedURLResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DtoGetChatSignedURLResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DtoGetChatSignedURLResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DtoGetChatSignedURLResponse(
        signedUrls: DtoChatImageURL.listFromJson(json[r'signed_urls']) ?? const [],
      );
    }
    return null;
  }

  static List<DtoGetChatSignedURLResponse>? listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DtoGetChatSignedURLResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DtoGetChatSignedURLResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DtoGetChatSignedURLResponse> mapFromJson(dynamic json) {
    final map = <String, DtoGetChatSignedURLResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetChatSignedURLResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DtoGetChatSignedURLResponse-objects as value to a dart map
  static Map<String, List<DtoGetChatSignedURLResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DtoGetChatSignedURLResponse>>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DtoGetChatSignedURLResponse.listFromJson(entry.value, growable: growable,);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

