# openapi.model.DtoExecuteWebhookRequest

## Load the model package
```dart
import 'package:openapi/api.dart';
```

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**action** | **String** |  | 
**bot** | [**WebhookCard**](WebhookCard.md) |  | 
**card** | [**WebhookCard**](WebhookCard.md) |  | 
**current** | [**WebhookMessage**](WebhookMessage.md) |  | 
**history** | [**List<WebhookMessage>**](WebhookMessage.md) |  | [optional] [default to const []]
**reaction** | [**WebhookReaction**](WebhookReaction.md) |  | [optional] 
**workspace** | [**WebhookCard**](WebhookCard.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


