//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.12

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class OTPsApi {
  OTPsApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// create Otp
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoOTP] body (required):
  ///   OTP object
  Future<Response> otpCreatePostWithHttpInfo(DtoOTP body,) async {
    // ignore: prefer_const_declarations
    final path = r'/otp/create';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// create Otp
  ///
  /// Parameters:
  ///
  /// * [DtoOTP] body (required):
  ///   OTP object
  Future<void> otpCreatePost(DtoOTP body,) async {
    final response = await otpCreatePostWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// verify Otp
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [DtoOTPVerification] body (required):
  ///   OTPVerification object
  Future<Response> otpVerifyPutWithHttpInfo(DtoOTPVerification body,) async {
    // ignore: prefer_const_declarations
    final path = r'/otp/verify';

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// verify Otp
  ///
  /// Parameters:
  ///
  /// * [DtoOTPVerification] body (required):
  ///   OTPVerification object
  Future<void> otpVerifyPut(DtoOTPVerification body,) async {
    final response = await otpVerifyPutWithHttpInfo(body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}
